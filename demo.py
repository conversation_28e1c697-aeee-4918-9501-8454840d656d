#!/usr/bin/env python3
"""
AGI Knowledge Graph System - 快速演示脚本
"""
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

def demo_basic_functionality():
    """演示基本功能"""
    print("🚀 AGI Knowledge Graph System - 基本功能演示")
    print("=" * 60)
    
    try:
        # 导入核心模块
        from core.entities.concept import Concept, ConceptType
        from core.entities.entity import Entity, EntityType
        from core.semantic.relationship import Relationship, RelationshipType
        from core.knowledge.knowledge_base import KnowledgeBase, KnowledgeItem, KnowledgeType
        
        print("✅ 核心模块导入成功")
        
        # 1. 创建概念
        print("\n1. 创建概念层次...")
        animal_concept = Concept(
            name="动物",
            concept_type=ConceptType.ABSTRACT,
            description="生物界中的动物类别",
            abstraction_level=2,
            domain="生物学"
        )
        
        dog_concept = Concept(
            name="狗",
            concept_type=ConceptType.CONCRETE,
            description="犬科动物",
            abstraction_level=1,
            domain="生物学"
        )
        
        # 建立层次关系
        dog_concept.add_parent_concept(animal_concept.id)
        animal_concept.add_child_concept(dog_concept.id)
        
        print(f"   ✅ 创建概念: {animal_concept.name}")
        print(f"   ✅ 创建概念: {dog_concept.name}")
        print(f"   ✅ 建立层次关系: {dog_concept.name} -> {animal_concept.name}")
        
        # 2. 创建实体
        print("\n2. 创建实体...")
        person_entity = Entity(
            name="张三",
            description="一个普通人",
            entity_type=EntityType.PERSON,
            location="北京"
        )
        person_entity.set_attribute("age", 30)
        person_entity.set_attribute("occupation", "工程师")
        
        pet_entity = Entity(
            name="小白",
            description="张三的宠物狗",
            entity_type=EntityType.OBJECT,
            concept_id=dog_concept.id
        )
        pet_entity.set_attribute("breed", "金毛")
        pet_entity.set_attribute("age", 3)
        
        print(f"   ✅ 创建实体: {person_entity.name} ({person_entity.entity_type.value})")
        print(f"   ✅ 创建实体: {pet_entity.name} ({pet_entity.entity_type.value})")
        
        # 3. 创建关系
        print("\n3. 创建语义关系...")
        owns_relationship = Relationship(
            relationship_type=RelationshipType.OWNS,
            source_id=person_entity.id,
            target_id=pet_entity.id,
            strength=1.0,
            confidence=1.0,
            description="张三拥有小白"
        )
        
        is_a_relationship = Relationship(
            relationship_type=RelationshipType.IS_A,
            source_id=pet_entity.id,
            target_id=dog_concept.id,
            strength=1.0,
            confidence=1.0,
            description="小白是一只狗"
        )
        
        print(f"   ✅ 创建关系: {person_entity.name} {owns_relationship.relationship_type.value} {pet_entity.name}")
        print(f"   ✅ 创建关系: {pet_entity.name} {is_a_relationship.relationship_type.value} {dog_concept.name}")
        
        # 4. 创建知识库
        print("\n4. 创建知识库...")
        knowledge_base = KnowledgeBase()
        
        # 添加事实性知识
        factual_knowledge = KnowledgeItem(
            title="狗的基本信息",
            content="狗是人类最早驯化的动物之一，通常寿命为10-15年",
            knowledge_type=KnowledgeType.FACTUAL,
            category="动物知识",
            domain="生物学",
            keywords=["狗", "宠物", "寿命"],
            confidence=0.9
        )
        factual_knowledge.add_related_concept(dog_concept.id)
        
        # 添加程序性知识
        procedural_knowledge = KnowledgeItem(
            title="如何照顾宠物狗",
            content={
                "steps": [
                    "每天喂食2-3次",
                    "定期遛狗运动",
                    "定期洗澡和梳毛",
                    "定期体检和疫苗接种"
                ]
            },
            knowledge_type=KnowledgeType.PROCEDURAL,
            category="宠物护理",
            domain="生活常识",
            keywords=["宠物护理", "狗", "日常照顾"]
        )
        
        knowledge_base.add_knowledge(factual_knowledge)
        knowledge_base.add_knowledge(procedural_knowledge)
        
        print(f"   ✅ 添加知识: {factual_knowledge.title}")
        print(f"   ✅ 添加知识: {procedural_knowledge.title}")
        
        # 5. 演示查询功能
        print("\n5. 演示查询功能...")
        
        # 搜索知识
        search_results = knowledge_base.search_by_keyword("狗")
        print(f"   🔍 搜索关键词 '狗' 找到 {len(search_results)} 个结果:")
        for item in search_results:
            print(f"      - {item.title}")
        
        # 全文搜索
        full_text_results = knowledge_base.full_text_search("照顾")
        print(f"   🔍 全文搜索 '照顾' 找到 {len(full_text_results)} 个结果:")
        for item in full_text_results:
            print(f"      - {item.title}")
        
        # 6. 显示统计信息
        print("\n6. 系统统计信息...")
        kb_stats = knowledge_base.get_knowledge_statistics()
        print(f"   📊 知识库统计:")
        print(f"      - 总知识项数: {kb_stats['total_items']}")
        print(f"      - 平均置信度: {kb_stats['average_confidence']:.2f}")
        print(f"      - 总分类数: {kb_stats['total_categories']}")
        print(f"      - 总关键词数: {kb_stats['total_keywords']}")
        
        # 7. 演示概念验证
        print("\n7. 概念验证...")
        print(f"   ✅ {animal_concept.name} 验证: {animal_concept.validate_node()}")
        print(f"   ✅ {dog_concept.name} 验证: {dog_concept.validate_node()}")
        print(f"   ✅ {person_entity.name} 验证: {person_entity.validate_node()}")
        print(f"   ✅ {pet_entity.name} 验证: {pet_entity.validate_node()}")
        
        # 8. 演示层次关系查询
        print("\n8. 层次关系查询...")
        print(f"   🔗 {dog_concept.name} 的父概念: {len(dog_concept.parent_concepts)} 个")
        print(f"   🔗 {animal_concept.name} 的子概念: {len(animal_concept.child_concepts)} 个")
        print(f"   🔗 {dog_concept.name} 是否为 {animal_concept.name} 的子概念: {dog_concept.is_child_of(animal_concept.id)}")
        
        print("\n" + "=" * 60)
        print("🎉 演示完成！AGI Knowledge Graph System 基本功能正常运行")
        print("💡 提示: 运行 'python main.py' 启动API服务器")
        print("📚 提示: 运行 'python cli.py --help' 查看更多命令")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    success = demo_basic_functionality()
    
    if success:
        print("\n🚀 想要体验更多功能？")
        print("   • 运行完整演示: python examples/basic_usage.py")
        print("   • 启动API服务: python main.py")
        print("   • 使用CLI工具: python cli.py")
        print("   • 查看帮助: python cli.py --help")
    else:
        print("\n❌ 演示失败，请检查错误信息并修复问题")
        sys.exit(1)


if __name__ == "__main__":
    main()
