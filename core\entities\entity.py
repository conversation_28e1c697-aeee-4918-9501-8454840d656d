"""
实体类定义 - 具体的知识实例或数据点
"""
from typing import Dict, Any, List, Optional, Set
from enum import Enum
from datetime import datetime
from pydantic import Field

from .base import BaseNode, NodeMetadata


class EntityType(str, Enum):
    """实体类型枚举"""
    PERSON = "person"          # 人物
    ORGANIZATION = "organization"  # 组织
    LOCATION = "location"      # 地点
    EVENT = "event"           # 事件
    OBJECT = "object"         # 物体
    CONCEPT_INSTANCE = "concept_instance"  # 概念实例
    TASK = "task"             # 任务
    GOAL = "goal"             # 目标
    RESOURCE = "resource"     # 资源
    CUSTOM = "custom"         # 自定义


class Entity(BaseNode):
    """实体类 - 表示具体的知识实例或数据点"""
    
    entity_type: EntityType = Field(..., description="实体类型")
    concept_id: Optional[str] = Field(None, description="所属概念ID")
    related_entities: Set[str] = Field(default_factory=set, description="相关实体ID集合")
    metadata: NodeMetadata = Field(default_factory=NodeMetadata)
    
    # 实体特有属性
    aliases: List[str] = Field(default_factory=list, description="别名列表")
    attributes: Dict[str, Any] = Field(default_factory=dict, description="实体属性")
    location: Optional[str] = Field(None, description="位置信息")
    time_range: Optional[Dict[str, datetime]] = Field(None, description="时间范围")
    status: str = Field(default="active", description="状态")
    
    def add_alias(self, alias: str) -> None:
        """添加别名"""
        if alias not in self.aliases:
            self.aliases.append(alias)
            self.updated_at = datetime.now()
    
    def add_related_entity(self, entity_id: str) -> None:
        """添加相关实体"""
        self.related_entities.add(entity_id)
        self.updated_at = datetime.now()
    
    def set_attribute(self, key: str, value: Any) -> None:
        """设置实体属性"""
        self.attributes[key] = value
        self.updated_at = datetime.now()
    
    def get_attribute(self, key: str, default: Any = None) -> Any:
        """获取实体属性"""
        return self.attributes.get(key, default)
    
    def set_time_range(self, start_time: Optional[datetime] = None, 
                      end_time: Optional[datetime] = None) -> None:
        """设置时间范围"""
        if self.time_range is None:
            self.time_range = {}
        
        if start_time:
            self.time_range["start"] = start_time
        if end_time:
            self.time_range["end"] = end_time
        
        self.updated_at = datetime.now()
    
    def is_active_at(self, check_time: datetime) -> bool:
        """检查在指定时间是否活跃"""
        if not self.time_range:
            return True
        
        start_time = self.time_range.get("start")
        end_time = self.time_range.get("end")
        
        if start_time and check_time < start_time:
            return False
        if end_time and check_time > end_time:
            return False
        
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        time_range_dict = None
        if self.time_range:
            time_range_dict = {
                k: v.isoformat() if isinstance(v, datetime) else v
                for k, v in self.time_range.items()
            }
        
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "entity_type": self.entity_type.value,
            "concept_id": self.concept_id,
            "related_entities": list(self.related_entities),
            "aliases": self.aliases,
            "attributes": self.attributes,
            "location": self.location,
            "time_range": time_range_dict,
            "status": self.status,
            "properties": self.properties,
            "metadata": self.metadata.model_dump(),
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "version": self.version
        }
    
    def validate_node(self) -> bool:
        """验证实体节点有效性"""
        if not self.name or not self.entity_type:
            return False
        
        # 检查时间范围合理性
        if self.time_range:
            start_time = self.time_range.get("start")
            end_time = self.time_range.get("end")
            if start_time and end_time and start_time > end_time:
                return False
        
        # 检查状态有效性
        valid_statuses = ["active", "inactive", "archived", "deleted"]
        if self.status not in valid_statuses:
            return False
        
        return True


class EntityManager:
    """实体管理器"""
    
    def __init__(self):
        self.entities: Dict[str, Entity] = {}
        self.entity_index: Dict[str, Set[str]] = {
            "by_type": {},
            "by_concept": {},
            "by_location": {},
            "by_status": {}
        }
    
    def add_entity(self, entity: Entity) -> None:
        """添加实体"""
        if not entity.validate_node():
            raise ValueError(f"Invalid entity: {entity.id}")
        
        self.entities[entity.id] = entity
        self._update_index(entity)
    
    def get_entity(self, entity_id: str) -> Optional[Entity]:
        """获取实体"""
        return self.entities.get(entity_id)
    
    def find_entities_by_type(self, entity_type: EntityType) -> List[Entity]:
        """根据类型查找实体"""
        entity_ids = self.entity_index["by_type"].get(entity_type.value, set())
        return [self.entities[eid] for eid in entity_ids if eid in self.entities]
    
    def find_entities_by_concept(self, concept_id: str) -> List[Entity]:
        """根据概念查找实体"""
        entity_ids = self.entity_index["by_concept"].get(concept_id, set())
        return [self.entities[eid] for eid in entity_ids if eid in self.entities]
    
    def find_entities_by_location(self, location: str) -> List[Entity]:
        """根据位置查找实体"""
        entity_ids = self.entity_index["by_location"].get(location, set())
        return [self.entities[eid] for eid in entity_ids if eid in self.entities]
    
    def find_entities_by_name(self, name: str, fuzzy: bool = False) -> List[Entity]:
        """根据名称查找实体"""
        results = []
        
        for entity in self.entities.values():
            if fuzzy:
                if name.lower() in entity.name.lower():
                    results.append(entity)
                # 检查别名
                for alias in entity.aliases:
                    if name.lower() in alias.lower():
                        results.append(entity)
                        break
            else:
                if entity.name == name or name in entity.aliases:
                    results.append(entity)
        
        return results
    
    def find_related_entities(self, entity_id: str, max_depth: int = 2) -> List[Entity]:
        """查找相关实体"""
        if entity_id not in self.entities:
            return []
        
        visited = set()
        queue = [(entity_id, 0)]
        related = []
        
        while queue:
            current_id, depth = queue.pop(0)
            
            if current_id in visited or depth > max_depth:
                continue
            
            visited.add(current_id)
            current_entity = self.entities.get(current_id)
            
            if current_entity and current_id != entity_id:
                related.append(current_entity)
            
            if current_entity and depth < max_depth:
                for related_id in current_entity.related_entities:
                    if related_id not in visited:
                        queue.append((related_id, depth + 1))
        
        return related
    
    def _update_index(self, entity: Entity) -> None:
        """更新索引"""
        # 按类型索引
        if entity.entity_type.value not in self.entity_index["by_type"]:
            self.entity_index["by_type"][entity.entity_type.value] = set()
        self.entity_index["by_type"][entity.entity_type.value].add(entity.id)
        
        # 按概念索引
        if entity.concept_id:
            if entity.concept_id not in self.entity_index["by_concept"]:
                self.entity_index["by_concept"][entity.concept_id] = set()
            self.entity_index["by_concept"][entity.concept_id].add(entity.id)
        
        # 按位置索引
        if entity.location:
            if entity.location not in self.entity_index["by_location"]:
                self.entity_index["by_location"][entity.location] = set()
            self.entity_index["by_location"][entity.location].add(entity.id)
        
        # 按状态索引
        if entity.status not in self.entity_index["by_status"]:
            self.entity_index["by_status"][entity.status] = set()
        self.entity_index["by_status"][entity.status].add(entity.id)
