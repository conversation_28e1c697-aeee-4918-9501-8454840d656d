{"$schema": "http://json-schema.org/draft-07/schema#", "title": "AGI推理层元数据标准", "description": "定义AGI系统中推理层的元数据结构和标准", "version": "1.0.0", "type": "object", "properties": {"metadata_info": {"type": "object", "description": "元数据基本信息", "properties": {"schema_version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "created_by": {"type": "string"}, "last_modified_by": {"type": "string"}}, "required": ["schema_version", "created_at", "created_by"]}, "reasoning_definition": {"type": "object", "description": "推理定义", "properties": {"reasoning_id": {"type": "string", "description": "推理规则唯一标识符", "pattern": "^reasoning_[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$"}, "reasoning_name": {"type": "string", "description": "推理规则名称", "minLength": 1, "maxLength": 255}, "reasoning_type": {"type": "string", "description": "推理类型", "enum": ["deductive", "inductive", "abductive", "analogical", "causal", "temporal", "spatial", "probabilistic", "fuzzy", "default", "non_monotonic", "modal", "epistemic", "deontic"]}, "description": {"type": "string", "description": "推理规则描述", "maxLength": 2000}, "domain": {"type": "string", "description": "适用领域", "enum": ["general", "mathematics", "logic", "science", "medicine", "law", "business", "engineering", "social_science", "custom"]}}, "required": ["reasoning_id", "reasoning_name", "reasoning_type"]}, "rule_specification": {"type": "object", "description": "规则规范", "properties": {"formal_representation": {"type": "object", "description": "形式化表示", "properties": {"logic_form": {"type": "string", "description": "逻辑形式", "enum": ["propositional", "predicate", "modal", "temporal", "fuzzy", "probabilistic"]}, "syntax": {"type": "string", "description": "语法格式", "enum": ["prolog", "horn_clause", "first_order_logic", "description_logic", "rule_ml", "custom"]}, "formula": {"type": "string", "description": "公式表示", "maxLength": 5000}, "variables": {"type": "array", "description": "变量定义", "items": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string", "enum": ["individual", "predicate", "function", "constant"]}, "domain": {"type": "string"}, "description": {"type": "string"}}, "required": ["name", "type"]}}}, "required": ["logic_form", "syntax", "formula"]}, "natural_language": {"type": "object", "description": "自然语言表示", "properties": {"premise": {"type": "string", "description": "前提条件", "maxLength": 2000}, "conclusion": {"type": "string", "description": "结论", "maxLength": 2000}, "conditions": {"type": "array", "description": "条件列表", "items": {"type": "string", "maxLength": 500}}, "exceptions": {"type": "array", "description": "例外情况", "items": {"type": "string", "maxLength": 500}}}}, "computational_representation": {"type": "object", "description": "计算表示", "properties": {"algorithm": {"type": "string", "description": "推理算法", "enum": ["forward_chaining", "backward_chaining", "resolution", "tableau", "model_checking", "constraint_satisfaction", "probabilistic_inference", "neural_reasoning"]}, "implementation": {"type": "object", "description": "实现细节", "properties": {"language": {"type": "string", "description": "实现语言"}, "framework": {"type": "string", "description": "使用框架"}, "code": {"type": "string", "description": "代码实现"}, "complexity": {"type": "object", "properties": {"time_complexity": {"type": "string"}, "space_complexity": {"type": "string"}}}}}}}}, "required": ["formal_representation"]}, "reasoning_properties": {"type": "object", "description": "推理属性", "properties": {"logical_properties": {"type": "object", "description": "逻辑属性", "properties": {"soundness": {"type": "boolean", "description": "可靠性"}, "completeness": {"type": "boolean", "description": "完备性"}, "consistency": {"type": "boolean", "description": "一致性"}, "decidability": {"type": "boolean", "description": "可判定性"}, "monotonicity": {"type": "boolean", "description": "单调性"}}}, "computational_properties": {"type": "object", "description": "计算属性", "properties": {"complexity_class": {"type": "string", "description": "复杂度类", "enum": ["P", "NP", "PSPACE", "EXPTIME", "undecidable"]}, "tractability": {"type": "string", "description": "可处理性", "enum": ["tractable", "intractable", "unknown"]}, "scalability": {"type": "string", "description": "可扩展性", "enum": ["excellent", "good", "moderate", "poor"]}, "parallelizable": {"type": "boolean", "description": "可并行化"}}}, "epistemic_properties": {"type": "object", "description": "认知属性", "properties": {"certainty_level": {"type": "string", "description": "确定性水平", "enum": ["certain", "probable", "possible", "uncertain"]}, "confidence": {"type": "number", "description": "置信度", "minimum": 0.0, "maximum": 1.0}, "strength": {"type": "number", "description": "推理强度", "minimum": 0.0, "maximum": 1.0}, "defeasibility": {"type": "boolean", "description": "可废止性"}}}}}, "application_context": {"type": "object", "description": "应用上下文", "properties": {"applicable_domains": {"type": "array", "description": "适用领域", "items": {"type": "string"}}, "prerequisites": {"type": "array", "description": "前置条件", "items": {"type": "object", "properties": {"condition_id": {"type": "string"}, "condition_type": {"type": "string", "enum": ["knowledge", "data", "context", "resource"]}, "description": {"type": "string"}, "required": {"type": "boolean", "default": true}}}}, "constraints": {"type": "array", "description": "约束条件", "items": {"type": "object", "properties": {"constraint_type": {"type": "string", "enum": ["temporal", "spatial", "resource", "logical", "ethical"]}, "description": {"type": "string"}, "severity": {"type": "string", "enum": ["hard", "soft", "preference"]}}}}, "performance_metrics": {"type": "object", "description": "性能指标", "properties": {"accuracy": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "precision": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "recall": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "f1_score": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "execution_time": {"type": "number", "description": "执行时间（毫秒）", "minimum": 0}, "memory_usage": {"type": "number", "description": "内存使用（MB）", "minimum": 0}}}}}, "inference_patterns": {"type": "object", "description": "推理模式", "properties": {"pattern_type": {"type": "string", "description": "模式类型", "enum": ["modus_ponens", "modus_tollens", "hypothetical_syllogism", "disjunctive_syllogism", "universal_instantiation", "existential_generalization", "resolution", "unification", "case_based", "analogical"]}, "pattern_structure": {"type": "object", "description": "模式结构", "properties": {"antecedent": {"type": "array", "description": "前件", "items": {"type": "string"}}, "consequent": {"type": "string", "description": "后件"}, "side_conditions": {"type": "array", "description": "边界条件", "items": {"type": "string"}}}}, "instantiation_examples": {"type": "array", "description": "实例化示例", "items": {"type": "object", "properties": {"example_id": {"type": "string"}, "description": {"type": "string"}, "premises": {"type": "array", "items": {"type": "string"}}, "conclusion": {"type": "string"}, "validity": {"type": "boolean"}}}}}}, "validation_info": {"type": "object", "description": "验证信息", "properties": {"validation_method": {"type": "string", "description": "验证方法", "enum": ["formal_proof", "model_checking", "theorem_proving", "empirical_testing", "expert_review", "peer_review", "automated_testing"]}, "validation_status": {"type": "string", "description": "验证状态", "enum": ["verified", "partially_verified", "unverified", "invalid"]}, "test_cases": {"type": "array", "description": "测试用例", "items": {"type": "object", "properties": {"test_id": {"type": "string"}, "input": {"type": "object", "description": "输入"}, "expected_output": {"type": "object", "description": "期望输出"}, "actual_output": {"type": "object", "description": "实际输出"}, "result": {"type": "string", "enum": ["pass", "fail", "error"]}}}}, "validation_report": {"type": "object", "description": "验证报告", "properties": {"summary": {"type": "string"}, "issues_found": {"type": "array", "items": {"type": "string"}}, "recommendations": {"type": "array", "items": {"type": "string"}}, "validator": {"type": "string"}, "validation_date": {"type": "string", "format": "date-time"}}}}}, "quality_metrics": {"type": "object", "description": "质量度量", "properties": {"correctness": {"type": "number", "minimum": 0.0, "maximum": 1.0, "description": "正确性"}, "completeness": {"type": "number", "minimum": 0.0, "maximum": 1.0, "description": "完整性"}, "consistency": {"type": "number", "minimum": 0.0, "maximum": 1.0, "description": "一致性"}, "efficiency": {"type": "number", "minimum": 0.0, "maximum": 1.0, "description": "效率"}, "robustness": {"type": "number", "minimum": 0.0, "maximum": 1.0, "description": "鲁棒性"}, "interpretability": {"type": "number", "minimum": 0.0, "maximum": 1.0, "description": "可解释性"}}}, "provenance_info": {"type": "object", "description": "数据溯源信息", "properties": {"source": {"type": "string", "description": "来源", "enum": ["expert_knowledge", "literature_review", "formal_analysis", "empirical_study", "machine_learning", "automated_discovery", "user_input"]}, "author": {"type": "string", "description": "作者"}, "institution": {"type": "string", "description": "机构"}, "publication": {"type": "string", "description": "发表信息"}, "version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$"}}, "required": ["source", "version"]}, "extensions": {"type": "object", "description": "扩展字段", "patternProperties": {"^ext_[a-zA-Z_][a-zA-Z0-9_]*$": {"description": "扩展字段，字段名必须以ext_开头"}}}}, "required": ["metadata_info", "reasoning_definition", "rule_specification", "reasoning_properties", "quality_metrics", "provenance_info"], "additionalProperties": false}