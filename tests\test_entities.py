"""
测试概念和实体模块
"""
import pytest
from datetime import datetime

from core.entities.concept import Concept, ConceptType, ConceptHierarchy
from core.entities.entity import Entity, EntityType, EntityManager


class TestConcept:
    """测试概念类"""
    
    def test_create_concept(self):
        """测试创建概念"""
        concept = Concept(
            name="动物",
            concept_type=ConceptType.ABSTRACT,
            description="生物界中的动物类别",
            abstraction_level=2,
            domain="生物学"
        )
        
        assert concept.name == "动物"
        assert concept.concept_type == ConceptType.ABSTRACT
        assert concept.abstraction_level == 2
        assert concept.domain == "生物学"
        assert concept.validate_node() == True
    
    def test_concept_hierarchy(self):
        """测试概念层次关系"""
        parent_concept = Concept(
            name="动物",
            concept_type=ConceptType.ABSTRACT,
            abstraction_level=2
        )
        
        child_concept = Concept(
            name="狗",
            concept_type=ConceptType.CONCRETE,
            abstraction_level=1
        )
        
        # 建立层次关系
        child_concept.add_parent_concept(parent_concept.id)
        parent_concept.add_child_concept(child_concept.id)
        
        assert parent_concept.is_parent_of(child_concept.id)
        assert child_concept.is_child_of(parent_concept.id)
    
    def test_concept_hierarchy_manager(self):
        """测试概念层次管理器"""
        hierarchy = ConceptHierarchy()
        
        # 创建概念
        animal = Concept(name="动物", concept_type=ConceptType.ABSTRACT, abstraction_level=2)
        mammal = Concept(name="哺乳动物", concept_type=ConceptType.ABSTRACT, abstraction_level=1)
        dog = Concept(name="狗", concept_type=ConceptType.CONCRETE, abstraction_level=0)
        
        # 建立层次关系
        mammal.add_parent_concept(animal.id)
        dog.add_parent_concept(mammal.id)
        animal.add_child_concept(mammal.id)
        mammal.add_child_concept(dog.id)
        
        # 添加到层次管理器
        hierarchy.add_concept(animal)
        hierarchy.add_concept(mammal)
        hierarchy.add_concept(dog)
        
        # 测试祖先查找
        ancestors = hierarchy.get_ancestors(dog.id)
        ancestor_names = [a.name for a in ancestors]
        assert "哺乳动物" in ancestor_names
        assert "动物" in ancestor_names
        
        # 测试后代查找
        descendants = hierarchy.get_descendants(animal.id)
        descendant_names = [d.name for d in descendants]
        assert "哺乳动物" in descendant_names
        assert "狗" in descendant_names


class TestEntity:
    """测试实体类"""
    
    def test_create_entity(self):
        """测试创建实体"""
        entity = Entity(
            name="张三",
            description="一个普通人",
            entity_type=EntityType.PERSON,
            location="北京"
        )
        
        assert entity.name == "张三"
        assert entity.entity_type == EntityType.PERSON
        assert entity.location == "北京"
        assert entity.validate_node() == True
    
    def test_entity_attributes(self):
        """测试实体属性"""
        entity = Entity(
            name="小白",
            entity_type=EntityType.OBJECT
        )
        
        # 设置属性
        entity.set_attribute("breed", "金毛")
        entity.set_attribute("age", 3)
        
        assert entity.get_attribute("breed") == "金毛"
        assert entity.get_attribute("age") == 3
        assert entity.get_attribute("color", "unknown") == "unknown"
    
    def test_entity_time_range(self):
        """测试实体时间范围"""
        entity = Entity(
            name="项目A",
            entity_type=EntityType.EVENT
        )
        
        start_time = datetime(2024, 1, 1)
        end_time = datetime(2024, 12, 31)
        
        entity.set_time_range(start_time, end_time)
        
        # 测试时间范围内
        check_time = datetime(2024, 6, 15)
        assert entity.is_active_at(check_time) == True
        
        # 测试时间范围外
        check_time = datetime(2025, 1, 1)
        assert entity.is_active_at(check_time) == False
    
    def test_entity_manager(self):
        """测试实体管理器"""
        manager = EntityManager()
        
        # 创建实体
        person = Entity(name="张三", entity_type=EntityType.PERSON, location="北京")
        pet = Entity(name="小白", entity_type=EntityType.OBJECT, location="北京")
        
        # 添加到管理器
        manager.add_entity(person)
        manager.add_entity(pet)
        
        # 测试按类型查找
        persons = manager.find_entities_by_type(EntityType.PERSON)
        assert len(persons) == 1
        assert persons[0].name == "张三"
        
        # 测试按位置查找
        beijing_entities = manager.find_entities_by_location("北京")
        assert len(beijing_entities) == 2
        
        # 测试按名称查找
        found_entities = manager.find_entities_by_name("张三")
        assert len(found_entities) == 1
        assert found_entities[0].name == "张三"
        
        # 测试模糊查找
        fuzzy_results = manager.find_entities_by_name("张", fuzzy=True)
        assert len(fuzzy_results) == 1


if __name__ == "__main__":
    pytest.main([__file__])
