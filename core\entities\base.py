"""
基础节点类定义
"""
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, Any, Optional, List
from uuid import uuid4
from pydantic import BaseModel, Field


class BaseNode(BaseModel, ABC):
    """知识图谱中所有节点的基类"""
    
    id: str = Field(default_factory=lambda: str(uuid4()))
    name: str = Field(..., description="节点名称")
    description: Optional[str] = Field(None, description="节点描述")
    properties: Dict[str, Any] = Field(default_factory=dict, description="节点属性")
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    version: int = Field(default=1, description="版本号")
    
    class Config:
        arbitrary_types_allowed = True
        
    def update_properties(self, **kwargs) -> None:
        """更新节点属性"""
        self.properties.update(kwargs)
        self.updated_at = datetime.now()
        self.version += 1
    
    def get_property(self, key: str, default: Any = None) -> Any:
        """获取属性值"""
        return self.properties.get(key, default)
    
    def set_property(self, key: str, value: Any) -> None:
        """设置属性值"""
        self.properties[key] = value
        self.updated_at = datetime.now()
    
    @abstractmethod
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        pass
    
    @abstractmethod
    def validate_node(self) -> bool:
        """验证节点有效性"""
        pass


class NodeMetadata(BaseModel):
    """节点元数据"""
    
    confidence: float = Field(default=1.0, ge=0.0, le=1.0, description="置信度")
    source: Optional[str] = Field(None, description="数据来源")
    tags: List[str] = Field(default_factory=list, description="标签")
    priority: int = Field(default=0, description="优先级")
    access_level: str = Field(default="public", description="访问级别")
    
    def add_tag(self, tag: str) -> None:
        """添加标签"""
        if tag not in self.tags:
            self.tags.append(tag)
    
    def remove_tag(self, tag: str) -> None:
        """移除标签"""
        if tag in self.tags:
            self.tags.remove(tag)
