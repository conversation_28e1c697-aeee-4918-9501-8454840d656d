{"$schema": "http://json-schema.org/draft-07/schema#", "title": "AGI概念层元数据标准", "description": "定义AGI系统中概念层的元数据结构和标准", "version": "1.0.0", "type": "object", "properties": {"metadata_info": {"type": "object", "description": "元数据基本信息", "properties": {"schema_version": {"type": "string", "description": "元数据模式版本", "pattern": "^\\d+\\.\\d+\\.\\d+$", "example": "1.0.0"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "description": "最后更新时间"}, "created_by": {"type": "string", "description": "创建者标识"}, "last_modified_by": {"type": "string", "description": "最后修改者标识"}}, "required": ["schema_version", "created_at", "created_by"]}, "concept_definition": {"type": "object", "description": "概念定义信息", "properties": {"concept_id": {"type": "string", "description": "概念唯一标识符", "pattern": "^concept_[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$", "example": "concept_12345678-1234-1234-1234-123456789abc"}, "concept_name": {"type": "string", "description": "概念名称", "minLength": 1, "maxLength": 255}, "concept_type": {"type": "string", "description": "概念类型", "enum": ["abstract", "concrete", "relation", "attribute", "action", "state", "event", "process"]}, "definition": {"type": "string", "description": "概念的正式定义", "maxLength": 2000}, "description": {"type": "string", "description": "概念的详细描述", "maxLength": 5000}, "aliases": {"type": "array", "description": "概念的别名列表", "items": {"type": "string", "maxLength": 255}, "uniqueItems": true}, "keywords": {"type": "array", "description": "关键词标签", "items": {"type": "string", "maxLength": 100}, "uniqueItems": true}}, "required": ["concept_id", "concept_name", "concept_type"]}, "hierarchical_structure": {"type": "object", "description": "概念层次结构信息", "properties": {"abstraction_level": {"type": "integer", "description": "抽象层级，0为最具体，数值越大越抽象", "minimum": 0, "maximum": 10}, "parent_concepts": {"type": "array", "description": "父概念ID列表", "items": {"type": "string", "pattern": "^concept_[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$"}, "uniqueItems": true}, "child_concepts": {"type": "array", "description": "子概念ID列表", "items": {"type": "string", "pattern": "^concept_[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$"}, "uniqueItems": true}, "sibling_concepts": {"type": "array", "description": "同级概念ID列表", "items": {"type": "string", "pattern": "^concept_[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$"}, "uniqueItems": true}, "hierarchy_path": {"type": "array", "description": "从根概念到当前概念的路径", "items": {"type": "string", "pattern": "^concept_[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$"}}}}, "semantic_properties": {"type": "object", "description": "语义属性信息", "properties": {"domain": {"type": "string", "description": "所属领域", "enum": ["general", "science", "technology", "medicine", "biology", "physics", "chemistry", "mathematics", "computer_science", "psychology", "philosophy", "linguistics", "economics", "sociology", "history", "geography", "arts", "literature", "music", "sports", "business", "law", "education", "custom"]}, "custom_domain": {"type": "string", "description": "自定义领域名称（当domain为custom时使用）", "maxLength": 100}, "semantic_tags": {"type": "array", "description": "语义标签", "items": {"type": "string", "maxLength": 50}, "uniqueItems": true}, "properties": {"type": "object", "description": "概念的属性集合", "patternProperties": {"^[a-zA-Z_][a-zA-Z0-9_]*$": {"type": "object", "properties": {"value": {"description": "属性值"}, "data_type": {"type": "string", "enum": ["string", "number", "boolean", "array", "object", "null"]}, "description": {"type": "string", "description": "属性描述"}, "required": {"type": "boolean", "description": "是否为必需属性", "default": false}, "constraints": {"type": "object", "description": "属性约束条件"}}, "required": ["value", "data_type"]}}}}}, "quality_metrics": {"type": "object", "description": "质量度量指标", "properties": {"confidence": {"type": "number", "description": "概念定义的置信度", "minimum": 0.0, "maximum": 1.0, "default": 1.0}, "completeness": {"type": "number", "description": "概念定义的完整性", "minimum": 0.0, "maximum": 1.0}, "consistency": {"type": "number", "description": "概念定义的一致性", "minimum": 0.0, "maximum": 1.0}, "relevance": {"type": "number", "description": "概念的相关性", "minimum": 0.0, "maximum": 1.0}, "usage_frequency": {"type": "integer", "description": "概念使用频率", "minimum": 0}, "last_accessed": {"type": "string", "format": "date-time", "description": "最后访问时间"}}}, "provenance_info": {"type": "object", "description": "数据溯源信息", "properties": {"source": {"type": "string", "description": "数据来源", "enum": ["manual_input", "automatic_extraction", "machine_learning", "expert_knowledge", "external_import", "user_feedback", "system_inference"]}, "source_reference": {"type": "string", "description": "来源引用或链接", "maxLength": 500}, "author": {"type": "string", "description": "作者或创建者", "maxLength": 100}, "version": {"type": "string", "description": "版本号", "pattern": "^\\d+\\.\\d+\\.\\d+$"}, "change_log": {"type": "array", "description": "变更历史记录", "items": {"type": "object", "properties": {"version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$"}, "timestamp": {"type": "string", "format": "date-time"}, "author": {"type": "string"}, "changes": {"type": "string", "description": "变更描述"}, "change_type": {"type": "string", "enum": ["create", "update", "delete", "merge", "split"]}}, "required": ["version", "timestamp", "author", "changes", "change_type"]}}}, "required": ["source", "version"]}, "validation_rules": {"type": "object", "description": "验证规则", "properties": {"structural_constraints": {"type": "array", "description": "结构约束", "items": {"type": "object", "properties": {"rule_id": {"type": "string"}, "rule_type": {"type": "string", "enum": ["hierarchy", "cardinality", "dependency", "exclusion"]}, "description": {"type": "string"}, "condition": {"type": "string", "description": "约束条件表达式"}, "severity": {"type": "string", "enum": ["error", "warning", "info"]}}, "required": ["rule_id", "rule_type", "condition"]}}, "semantic_constraints": {"type": "array", "description": "语义约束", "items": {"type": "object", "properties": {"rule_id": {"type": "string"}, "rule_type": {"type": "string", "enum": ["domain_specific", "logical_consistency", "semantic_coherence"]}, "description": {"type": "string"}, "condition": {"type": "string"}, "severity": {"type": "string", "enum": ["error", "warning", "info"]}}, "required": ["rule_id", "rule_type", "condition"]}}}}, "extensions": {"type": "object", "description": "扩展字段，用于自定义属性", "patternProperties": {"^ext_[a-zA-Z_][a-zA-Z0-9_]*$": {"description": "扩展字段，字段名必须以ext_开头"}}}}, "required": ["metadata_info", "concept_definition", "quality_metrics", "provenance_info"], "additionalProperties": false}