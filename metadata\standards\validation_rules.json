{"$schema": "http://json-schema.org/draft-07/schema#", "title": "AGI元数据验证规则", "description": "定义AGI系统元数据的完整验证规则集合", "version": "1.0.0", "validation_framework": {"validation_levels": [{"level": "syntax", "description": "语法层验证", "scope": "字段格式、数据类型、基本约束"}, {"level": "semantic", "description": "语义层验证", "scope": "业务逻辑、关系一致性、领域规则"}, {"level": "pragmatic", "description": "实用层验证", "scope": "性能影响、资源约束、操作可行性"}], "validation_timing": ["on_create", "on_update", "on_delete", "periodic_check", "on_demand"], "severity_levels": [{"level": "error", "description": "严重错误，阻止操作继续", "action": "reject_operation"}, {"level": "warning", "description": "警告，记录但允许操作", "action": "log_and_continue"}, {"level": "info", "description": "信息提示，仅记录", "action": "log_only"}]}, "syntax_validation_rules": {"field_format_rules": {"id_format": {"rule_id": "SYN_001", "description": "ID字段格式验证", "applies_to": ["concept_id", "entity_id", "relationship_id", "knowledge_id"], "validation_logic": {"pattern": "^(concept|entity|rel|knowledge)_[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$", "case_sensitive": true}, "error_message": "ID必须符合'{prefix}_{uuid}'格式", "severity": "error", "examples": {"valid": ["concept_12345678-1234-1234-1234-123456789abc"], "invalid": ["concept_123", "CONCEPT_12345678-1234-1234-1234-123456789abc"]}}, "datetime_format": {"rule_id": "SYN_002", "description": "日期时间格式验证", "applies_to": ["created_at", "updated_at", "valid_from", "valid_to"], "validation_logic": {"format": "ISO_8601", "timezone_required": true, "precision": "seconds"}, "error_message": "日期时间必须符合ISO 8601格式且包含时区信息", "severity": "error", "examples": {"valid": ["2024-01-01T12:00:00Z", "2024-01-01T12:00:00+08:00"], "invalid": ["2024-01-01", "2024-01-01 12:00:00", "01/01/2024"]}}, "version_format": {"rule_id": "SYN_003", "description": "版本号格式验证", "applies_to": ["version", "schema_version"], "validation_logic": {"pattern": "^\\d+\\.\\d+\\.\\d+$", "semantic_versioning": true}, "error_message": "版本号必须符合语义化版本格式(major.minor.patch)", "severity": "error", "examples": {"valid": ["1.0.0", "2.1.3", "10.15.2"], "invalid": ["1.0", "v1.0.0", "1.0.0-beta"]}}}, "data_type_rules": {"string_constraints": {"rule_id": "SYN_010", "description": "字符串字段约束验证", "validation_logic": {"encoding": "UTF-8", "max_length_check": true, "min_length_check": true, "null_check": true}, "field_specific_rules": {"concept_name": {"min_length": 1, "max_length": 255, "allowed_characters": "letters, numbers, spaces, hyphens, underscores"}, "description": {"min_length": 0, "max_length": 2000, "allowed_characters": "any UTF-8 characters"}}, "severity": "error"}, "numeric_constraints": {"rule_id": "SYN_011", "description": "数值字段约束验证", "validation_logic": {"range_check": true, "precision_check": true, "null_check": true}, "field_specific_rules": {"confidence": {"min_value": 0.0, "max_value": 1.0, "decimal_places": 2}, "abstraction_level": {"min_value": 0, "max_value": 10, "integer_only": true}}, "severity": "error"}, "array_constraints": {"rule_id": "SYN_012", "description": "数组字段约束验证", "validation_logic": {"max_items_check": true, "unique_items_check": true, "item_format_check": true}, "field_specific_rules": {"keywords": {"max_items": 50, "unique_items": true, "item_max_length": 50}, "parent_concepts": {"max_items": 10, "unique_items": true, "item_pattern": "^concept_[a-f0-9-]+$"}}, "severity": "error"}}, "enum_validation": {"rule_id": "SYN_020", "description": "枚举值验证", "applies_to": ["concept_type", "relationship_type", "knowledge_type"], "validation_logic": {"case_sensitive": true, "allowed_values_check": true}, "field_specific_enums": {"concept_type": ["abstract", "concrete", "relation", "attribute", "action", "state", "event", "process"], "relationship_type": ["is_a", "part_of", "contains", "has_property", "causes", "enables"], "knowledge_type": ["factual", "procedural", "declarative", "conceptual", "metacognitive"]}, "error_message": "字段值必须是预定义的枚举值之一", "severity": "error"}}, "semantic_validation_rules": {"business_logic_rules": {"concept_uniqueness": {"rule_id": "SEM_001", "description": "概念名称在领域内唯一性验证", "applies_to": ["concept_name", "domain"], "validation_logic": {"check_type": "uniqueness_constraint", "scope": "domain_specific", "case_sensitivity": false}, "error_message": "概念名称在同一领域内必须唯一", "severity": "error", "implementation": "SELECT COUNT(*) FROM concepts WHERE LOWER(concept_name) = LOWER(?) AND domain = ? AND concept_id != ?"}, "hierarchy_consistency": {"rule_id": "SEM_002", "description": "概念层次一致性验证", "applies_to": ["parent_concepts", "child_concepts", "abstraction_level"], "validation_logic": {"check_type": "hierarchical_consistency", "rules": ["parent_abstraction_level > child_abstraction_level", "no_circular_references", "max_hierarchy_depth <= 10"]}, "error_message": "概念层次结构必须保持一致性", "severity": "error"}, "relationship_validity": {"rule_id": "SEM_003", "description": "关系有效性验证", "applies_to": ["source_id", "target_id", "relationship_type"], "validation_logic": {"check_type": "relationship_constraints", "rules": ["source_and_target_exist", "relationship_type_compatible_with_node_types", "no_self_reference_for_non_reflexive_relations"]}, "error_message": "关系必须在有效的节点之间建立", "severity": "error"}}, "cross_field_validation": {"temporal_consistency": {"rule_id": "SEM_010", "description": "时间字段一致性验证", "applies_to": ["created_at", "updated_at", "valid_from", "valid_to"], "validation_logic": {"rules": ["updated_at >= created_at", "valid_to > valid_from (if both specified)", "created_at <= current_time", "valid_from <= valid_to"]}, "error_message": "时间字段之间必须保持逻辑一致性", "severity": "error"}, "content_format_consistency": {"rule_id": "SEM_011", "description": "内容格式一致性验证", "applies_to": ["content", "content_format"], "validation_logic": {"check_type": "format_content_match", "format_validators": {"text": "validate_plain_text", "json": "validate_json_structure", "xml": "validate_xml_structure", "markdown": "validate_markdown_syntax"}}, "error_message": "内容必须与声明的格式匹配", "severity": "warning"}}, "domain_specific_rules": {"knowledge_domain_rules": {"rule_id": "SEM_020", "description": "知识领域特定规则", "applies_to": ["domain", "concept_type", "knowledge_type"], "validation_logic": {"domain_constraints": {"medicine": {"required_fields": ["source_reference", "confidence"], "min_confidence": 0.8}, "science": {"required_fields": ["source_reference"], "min_confidence": 0.7}}}, "severity": "warning"}}}, "pragmatic_validation_rules": {"performance_rules": {"content_size_limits": {"rule_id": "PRAG_001", "description": "内容大小限制验证", "applies_to": ["content", "description"], "validation_logic": {"max_content_size": "10MB", "max_text_length": 1000000, "compression_check": true}, "error_message": "内容大小超出系统限制", "severity": "error"}, "relationship_count_limits": {"rule_id": "PRAG_002", "description": "关系数量限制验证", "applies_to": ["parent_concepts", "child_concepts"], "validation_logic": {"max_parents": 10, "max_children": 100, "max_total_relationships": 1000}, "error_message": "关系数量超出系统限制", "severity": "warning"}}, "resource_constraints": {"storage_efficiency": {"rule_id": "PRAG_010", "description": "存储效率验证", "validation_logic": {"duplicate_content_check": true, "compression_ratio_check": true, "storage_optimization_suggestions": true}, "severity": "info"}}}, "validation_procedures": {"pre_create_validation": {"description": "创建前验证流程", "steps": [{"step": 1, "action": "syntax_validation", "rules": ["SYN_001", "SYN_002", "SYN_003", "SYN_010", "SYN_011", "SYN_012", "SYN_020"]}, {"step": 2, "action": "semantic_validation", "rules": ["SEM_001", "SEM_002", "SEM_003", "SEM_010", "SEM_011"]}, {"step": 3, "action": "pragmatic_validation", "rules": ["PRAG_001", "PRAG_002"]}], "failure_handling": "reject_on_error_continue_on_warning"}, "pre_update_validation": {"description": "更新前验证流程", "steps": [{"step": 1, "action": "immutable_field_check", "rules": ["check_immutable_fields"]}, {"step": 2, "action": "syntax_validation", "rules": ["SYN_001", "SYN_002", "SYN_003", "SYN_010", "SYN_011", "SYN_012", "SYN_020"]}, {"step": 3, "action": "semantic_validation", "rules": ["SEM_001", "SEM_002", "SEM_003", "SEM_010", "SEM_011"]}, {"step": 4, "action": "version_increment_check", "rules": ["check_version_increment"]}]}, "pre_delete_validation": {"description": "删除前验证流程", "steps": [{"step": 1, "action": "dependency_check", "rules": ["check_dependent_relationships", "check_referenced_entities"]}, {"step": 2, "action": "cascade_impact_analysis", "rules": ["analyze_cascade_effects"]}]}, "periodic_validation": {"description": "定期验证流程", "frequency": "daily", "steps": [{"step": 1, "action": "data_quality_check", "rules": ["check_data_completeness", "check_data_consistency"]}, {"step": 2, "action": "performance_analysis", "rules": ["PRAG_001", "PRAG_002", "PRAG_010"]}, {"step": 3, "action": "generate_quality_report", "rules": ["compile_validation_results"]}]}}, "error_handling": {"error_codes": {"VAL_001": "字段格式错误", "VAL_002": "数据类型不匹配", "VAL_003": "必填字段缺失", "VAL_004": "唯一性约束违反", "VAL_005": "引用完整性错误", "VAL_006": "业务规则违反", "VAL_007": "时间逻辑错误", "VAL_008": "层次结构错误", "VAL_009": "内容格式不匹配", "VAL_010": "资源限制超出"}, "error_messages": {"templates": {"field_format_error": "字段 '{field_name}' 的值 '{field_value}' 不符合要求的格式 '{expected_format}'", "uniqueness_violation": "字段 '{field_name}' 的值 '{field_value}' 已存在，违反唯一性约束", "reference_error": "字段 '{field_name}' 引用的对象 '{referenced_id}' 不存在", "business_rule_violation": "操作违反业务规则: {rule_description}"}, "localization": {"supported_languages": ["zh-CN", "en-US"], "default_language": "zh-CN"}}, "recovery_strategies": {"auto_correction": {"enabled": true, "rules": ["trim_whitespace", "normalize_case", "fix_common_typos"]}, "suggestion_generation": {"enabled": true, "types": ["similar_valid_values", "format_examples", "correction_hints"]}}}, "validation_configuration": {"global_settings": {"strict_mode": false, "auto_fix_enabled": true, "validation_timeout_seconds": 30, "max_validation_errors": 100}, "environment_specific": {"development": {"strict_mode": false, "warning_as_error": false, "detailed_logging": true}, "testing": {"strict_mode": true, "warning_as_error": false, "detailed_logging": true}, "production": {"strict_mode": true, "warning_as_error": false, "detailed_logging": false}}}, "implementation_guidelines": {"validation_engine_integration": {"recommended_libraries": ["<PERSON>sonschema (Python)", "joi (JavaScript)", "<PERSON>er<PERSON><PERSON> (Python)", "ajv (JavaScript)"], "custom_validators": {"id_format_validator": "Implement UUID format validation with prefix checking", "hierarchy_validator": "Implement graph-based hierarchy consistency checking", "domain_specific_validator": "Implement domain-aware business rule validation"}}, "performance_optimization": {"caching_strategy": "Cache validation results for immutable fields", "batch_validation": "Validate multiple records in batches for efficiency", "lazy_validation": "Defer expensive validations until necessary", "parallel_validation": "Run independent validations in parallel"}, "monitoring_and_alerting": {"validation_metrics": ["validation_success_rate", "validation_execution_time", "error_frequency_by_type", "auto_correction_rate"], "alert_conditions": ["validation_failure_rate > 5%", "validation_timeout_rate > 1%", "critical_error_count > 10/hour"]}}}