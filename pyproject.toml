[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "agi-knowledge-graph"
version = "1.0.0"
description = "一个基于动态知识图谱的人工通用智能(AGI)工程项目"
readme = "README.md"
requires-python = ">=3.8"
license = {text = "MIT"}
authors = [
    {name = "AGI Development Team", email = "<EMAIL>"}
]
keywords = ["agi", "knowledge-graph", "ai", "semantic-network", "reasoning"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules",
]

dependencies = [
    "fastapi>=0.104.1",
    "uvicorn>=0.24.0",
    "pydantic>=2.5.0",
    "sqlalchemy>=2.0.23",
    "alembic>=1.12.1",
    "neo4j>=5.14.1",
    "py2neo>=2021.2.4",
    "openai>=1.3.7",
    "transformers>=4.36.0",
    "torch>=2.1.1",
    "numpy>=1.24.3",
    "scikit-learn>=1.3.2",
    "pandas>=2.1.3",
    "networkx>=3.2.1",
    "rdflib>=7.0.0",
    "python-dotenv>=1.0.0",
    "loguru>=0.7.2",
    "typer>=0.9.0",
    "rich>=13.7.0",
    "httpx>=0.25.2",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.7.1",
]

[project.urls]
Homepage = "https://github.com/example/agi-knowledge-graph"
Documentation = "https://agi-knowledge-graph.readthedocs.io/"
Repository = "https://github.com/example/agi-knowledge-graph.git"
Issues = "https://github.com/example/agi-knowledge-graph/issues"

[project.scripts]
agi-system = "main:main"
agi-init-db = "scripts.init_db:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["core*", "scripts*", "examples*"]
exclude = ["tests*"]

[tool.black]
line-length = 100
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "neo4j.*",
    "py2neo.*",
    "networkx.*",
    "sklearn.*",
    "transformers.*",
    "torch.*"
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--cov=core",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["core"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/env/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
