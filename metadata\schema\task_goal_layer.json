{"$schema": "http://json-schema.org/draft-07/schema#", "title": "AGI任务与目标层元数据标准", "description": "定义AGI系统中任务与目标层的元数据结构和标准", "version": "1.0.0", "type": "object", "properties": {"metadata_info": {"type": "object", "description": "元数据基本信息", "properties": {"schema_version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "created_by": {"type": "string"}, "last_modified_by": {"type": "string"}}, "required": ["schema_version", "created_at", "created_by"]}, "task_goal_definition": {"type": "object", "description": "任务或目标定义", "properties": {"id": {"type": "string", "description": "任务或目标唯一标识符", "pattern": "^(task|goal)_[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$"}, "name": {"type": "string", "description": "任务或目标名称", "minLength": 1, "maxLength": 255}, "type": {"type": "string", "description": "类型", "enum": ["task", "goal", "objective", "milestone", "subtask", "subgoal"]}, "category": {"type": "string", "description": "分类", "enum": ["cognitive", "analytical", "creative", "communication", "execution", "learning", "monitoring", "maintenance", "planning", "decision_making", "problem_solving", "optimization"]}, "description": {"type": "string", "description": "详细描述", "maxLength": 2000}, "objective": {"type": "string", "description": "目标陈述", "maxLength": 1000}, "scope": {"type": "string", "description": "范围", "enum": ["local", "global", "domain_specific", "cross_domain", "system_wide"]}, "complexity_level": {"type": "string", "description": "复杂度级别", "enum": ["simple", "moderate", "complex", "very_complex"]}}, "required": ["id", "name", "type", "category", "description"]}, "hierarchical_structure": {"type": "object", "description": "层次结构", "properties": {"parent_id": {"type": "string", "description": "父任务/目标ID", "pattern": "^(task|goal)_[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$"}, "child_ids": {"type": "array", "description": "子任务/目标ID列表", "items": {"type": "string", "pattern": "^(task|goal)_[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$"}, "uniqueItems": true}, "hierarchy_level": {"type": "integer", "description": "层次级别，0为根级别", "minimum": 0}, "decomposition_strategy": {"type": "string", "description": "分解策略", "enum": ["sequential", "parallel", "conditional", "iterative", "hierarchical", "hybrid"]}, "aggregation_method": {"type": "string", "description": "聚合方法", "enum": ["sum", "average", "weighted_average", "min", "max", "custom"]}}}, "execution_specification": {"type": "object", "description": "执行规范", "properties": {"status": {"type": "string", "description": "当前状态", "enum": ["pending", "ready", "in_progress", "paused", "completed", "failed", "cancelled", "blocked", "deferred"]}, "priority": {"type": "object", "description": "优先级", "properties": {"level": {"type": "string", "enum": ["low", "normal", "high", "urgent", "critical"]}, "score": {"type": "number", "description": "优先级分数", "minimum": 0.0, "maximum": 1.0}, "dynamic": {"type": "boolean", "description": "是否为动态优先级", "default": false}}, "required": ["level"]}, "estimated_effort": {"type": "object", "description": "预估工作量", "properties": {"duration": {"type": "string", "description": "预估持续时间（ISO 8601格式）", "pattern": "^P(?:\\d+Y)?(?:\\d+M)?(?:\\d+D)?(?:T(?:\\d+H)?(?:\\d+M)?(?:\\d+(?:\\.\\d+)?S)?)?$"}, "effort_points": {"type": "number", "description": "工作量点数", "minimum": 0.0}, "complexity_factor": {"type": "number", "description": "复杂度因子", "minimum": 0.1, "maximum": 10.0}}}, "actual_metrics": {"type": "object", "description": "实际度量", "properties": {"start_time": {"type": "string", "format": "date-time"}, "end_time": {"type": "string", "format": "date-time"}, "actual_duration": {"type": "string", "pattern": "^P(?:\\d+Y)?(?:\\d+M)?(?:\\d+D)?(?:T(?:\\d+H)?(?:\\d+M)?(?:\\d+(?:\\.\\d+)?S)?)?$"}, "progress": {"type": "number", "description": "完成进度", "minimum": 0.0, "maximum": 1.0}, "quality_score": {"type": "number", "description": "质量分数", "minimum": 0.0, "maximum": 1.0}}}}, "required": ["status", "priority"]}, "dependencies_constraints": {"type": "object", "description": "依赖关系和约束", "properties": {"prerequisites": {"type": "array", "description": "前置条件", "items": {"type": "object", "properties": {"prerequisite_id": {"type": "string"}, "prerequisite_type": {"type": "string", "enum": ["task", "goal", "resource", "knowledge", "skill", "condition"]}, "description": {"type": "string"}, "required": {"type": "boolean", "default": true}, "condition_expression": {"type": "string", "description": "条件表达式"}}, "required": ["prerequisite_id", "prerequisite_type"]}}, "dependencies": {"type": "array", "description": "依赖关系", "items": {"type": "object", "properties": {"dependency_id": {"type": "string"}, "dependency_type": {"type": "string", "enum": ["finish_to_start", "start_to_start", "finish_to_finish", "start_to_finish"]}, "lag_time": {"type": "string", "description": "滞后时间", "pattern": "^P(?:\\d+Y)?(?:\\d+M)?(?:\\d+D)?(?:T(?:\\d+H)?(?:\\d+M)?(?:\\d+(?:\\.\\d+)?S)?)?$"}, "strength": {"type": "number", "description": "依赖强度", "minimum": 0.0, "maximum": 1.0}}, "required": ["dependency_id", "dependency_type"]}}, "constraints": {"type": "array", "description": "约束条件", "items": {"type": "object", "properties": {"constraint_id": {"type": "string"}, "constraint_type": {"type": "string", "enum": ["temporal", "resource", "budget", "quality", "scope", "regulatory", "technical", "business"]}, "description": {"type": "string"}, "constraint_expression": {"type": "string", "description": "约束表达式"}, "severity": {"type": "string", "enum": ["hard", "soft", "preference"]}, "violation_penalty": {"type": "number", "description": "违反约束的惩罚分数", "minimum": 0.0}}, "required": ["constraint_id", "constraint_type", "constraint_expression", "severity"]}}}}, "resource_requirements": {"type": "object", "description": "资源需求", "properties": {"human_resources": {"type": "array", "description": "人力资源", "items": {"type": "object", "properties": {"role": {"type": "string"}, "skills_required": {"type": "array", "items": {"type": "string"}}, "experience_level": {"type": "string", "enum": ["beginner", "intermediate", "advanced", "expert"]}, "allocation": {"type": "number", "description": "分配比例", "minimum": 0.0, "maximum": 1.0}}, "required": ["role", "skills_required"]}}, "computational_resources": {"type": "object", "description": "计算资源", "properties": {"cpu_cores": {"type": "integer", "minimum": 1}, "memory_gb": {"type": "number", "minimum": 0.1}, "storage_gb": {"type": "number", "minimum": 0.1}, "gpu_required": {"type": "boolean", "default": false}, "network_bandwidth": {"type": "string", "description": "网络带宽要求"}}}, "knowledge_resources": {"type": "array", "description": "知识资源", "items": {"type": "object", "properties": {"knowledge_id": {"type": "string"}, "knowledge_type": {"type": "string"}, "access_level": {"type": "string", "enum": ["read", "write", "execute", "admin"]}, "criticality": {"type": "string", "enum": ["essential", "important", "helpful", "optional"]}}, "required": ["knowledge_id", "knowledge_type", "access_level"]}}, "external_services": {"type": "array", "description": "外部服务", "items": {"type": "object", "properties": {"service_name": {"type": "string"}, "service_type": {"type": "string"}, "api_endpoint": {"type": "string", "format": "uri"}, "authentication_required": {"type": "boolean"}, "rate_limits": {"type": "object", "description": "速率限制"}}, "required": ["service_name", "service_type"]}}}}, "success_criteria": {"type": "object", "description": "成功标准", "properties": {"completion_criteria": {"type": "array", "description": "完成标准", "items": {"type": "object", "properties": {"criterion_id": {"type": "string"}, "description": {"type": "string"}, "measurement_method": {"type": "string"}, "target_value": {"description": "目标值"}, "tolerance": {"type": "number", "description": "容差", "minimum": 0.0}, "weight": {"type": "number", "description": "权重", "minimum": 0.0, "maximum": 1.0}}, "required": ["criterion_id", "description", "measurement_method", "target_value"]}}, "quality_metrics": {"type": "array", "description": "质量指标", "items": {"type": "object", "properties": {"metric_name": {"type": "string"}, "metric_type": {"type": "string", "enum": ["accuracy", "precision", "recall", "f1_score", "efficiency", "usability", "reliability"]}, "target_value": {"type": "number"}, "minimum_acceptable": {"type": "number"}, "measurement_unit": {"type": "string"}}, "required": ["metric_name", "metric_type", "target_value"]}}, "acceptance_tests": {"type": "array", "description": "验收测试", "items": {"type": "object", "properties": {"test_id": {"type": "string"}, "test_name": {"type": "string"}, "test_description": {"type": "string"}, "test_type": {"type": "string", "enum": ["functional", "performance", "security", "usability", "integration"]}, "expected_outcome": {"type": "string"}, "pass_criteria": {"type": "string"}}, "required": ["test_id", "test_name", "test_type", "expected_outcome"]}}}}, "risk_management": {"type": "object", "description": "风险管理", "properties": {"identified_risks": {"type": "array", "description": "已识别风险", "items": {"type": "object", "properties": {"risk_id": {"type": "string"}, "risk_name": {"type": "string"}, "description": {"type": "string"}, "category": {"type": "string", "enum": ["technical", "operational", "financial", "schedule", "quality", "external"]}, "probability": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "impact": {"type": "string", "enum": ["low", "medium", "high", "critical"]}, "risk_score": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "mitigation_strategy": {"type": "string"}, "contingency_plan": {"type": "string"}}, "required": ["risk_id", "risk_name", "category", "probability", "impact"]}}, "risk_tolerance": {"type": "object", "description": "风险容忍度", "properties": {"overall_tolerance": {"type": "string", "enum": ["low", "medium", "high"]}, "category_tolerances": {"type": "object", "patternProperties": {"^(technical|operational|financial|schedule|quality|external)$": {"type": "string", "enum": ["low", "medium", "high"]}}}}}}}, "monitoring_control": {"type": "object", "description": "监控和控制", "properties": {"kpis": {"type": "array", "description": "关键绩效指标", "items": {"type": "object", "properties": {"kpi_name": {"type": "string"}, "description": {"type": "string"}, "measurement_frequency": {"type": "string", "enum": ["real_time", "hourly", "daily", "weekly", "monthly", "milestone"]}, "target_value": {"type": "number"}, "current_value": {"type": "number"}, "trend": {"type": "string", "enum": ["improving", "stable", "declining", "unknown"]}}, "required": ["kpi_name", "measurement_frequency", "target_value"]}}, "checkpoints": {"type": "array", "description": "检查点", "items": {"type": "object", "properties": {"checkpoint_id": {"type": "string"}, "name": {"type": "string"}, "scheduled_date": {"type": "string", "format": "date-time"}, "criteria": {"type": "array", "items": {"type": "string"}}, "status": {"type": "string", "enum": ["pending", "passed", "failed", "skipped"]}}, "required": ["checkpoint_id", "name", "scheduled_date", "criteria"]}}, "escalation_rules": {"type": "array", "description": "升级规则", "items": {"type": "object", "properties": {"rule_id": {"type": "string"}, "trigger_condition": {"type": "string"}, "escalation_level": {"type": "string", "enum": ["warning", "alert", "critical", "emergency"]}, "notification_targets": {"type": "array", "items": {"type": "string"}}, "automatic_actions": {"type": "array", "items": {"type": "string"}}}, "required": ["rule_id", "trigger_condition", "escalation_level"]}}}}, "provenance_info": {"type": "object", "description": "数据溯源信息", "properties": {"source": {"type": "string", "enum": ["manual_planning", "automatic_generation", "template_based", "ai_assisted", "imported", "derived", "collaborative"]}, "planning_method": {"type": "string", "description": "规划方法"}, "author": {"type": "string"}, "stakeholders": {"type": "array", "description": "利益相关者", "items": {"type": "object", "properties": {"stakeholder_id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "involvement_level": {"type": "string", "enum": ["primary", "secondary", "observer", "approver"]}}, "required": ["stakeholder_id", "name", "role"]}}, "approval_status": {"type": "string", "enum": ["draft", "pending_approval", "approved", "rejected", "under_review"]}, "version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$"}}, "required": ["source", "version"]}, "extensions": {"type": "object", "description": "扩展字段", "patternProperties": {"^ext_[a-zA-Z_][a-zA-Z0-9_]*$": {"description": "扩展字段，字段名必须以ext_开头"}}}}, "required": ["metadata_info", "task_goal_definition", "execution_specification", "success_criteria", "provenance_info"], "additionalProperties": false}