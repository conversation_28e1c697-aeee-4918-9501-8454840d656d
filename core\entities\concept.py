"""
概念类定义 - 认知系统中的核心抽象概念
"""
from typing import Dict, Any, List, Optional, Set
from enum import Enum
from datetime import datetime
from pydantic import Field

from .base import BaseNode, NodeMetadata


class ConceptType(str, Enum):
    """概念类型枚举"""
    ABSTRACT = "abstract"      # 抽象概念
    CONCRETE = "concrete"      # 具体概念
    RELATION = "relation"      # 关系概念
    ATTRIBUTE = "attribute"    # 属性概念
    ACTION = "action"          # 行为概念
    STATE = "state"           # 状态概念


class Concept(BaseNode):
    """概念类 - 表示认知系统中的抽象概念"""
    
    concept_type: ConceptType = Field(..., description="概念类型")
    parent_concepts: Set[str] = Field(default_factory=set, description="父概念ID集合")
    child_concepts: Set[str] = Field(default_factory=set, description="子概念ID集合")
    related_concepts: Set[str] = Field(default_factory=set, description="相关概念ID集合")
    metadata: NodeMetadata = Field(default_factory=NodeMetadata)
    
    # 概念特有属性
    abstraction_level: int = Field(default=0, description="抽象层级，0为最具体")
    domain: Optional[str] = Field(None, description="所属领域")
    definition: Optional[str] = Field(None, description="概念定义")
    examples: List[str] = Field(default_factory=list, description="概念示例")
    
    def add_parent_concept(self, parent_id: str) -> None:
        """添加父概念"""
        self.parent_concepts.add(parent_id)
        self.updated_at = datetime.now()
    
    def add_child_concept(self, child_id: str) -> None:
        """添加子概念"""
        self.child_concepts.add(child_id)
        self.updated_at = datetime.now()
    
    def add_related_concept(self, related_id: str) -> None:
        """添加相关概念"""
        self.related_concepts.add(related_id)
        self.updated_at = datetime.now()
    
    def is_parent_of(self, concept_id: str) -> bool:
        """检查是否为指定概念的父概念"""
        return concept_id in self.child_concepts
    
    def is_child_of(self, concept_id: str) -> bool:
        """检查是否为指定概念的子概念"""
        return concept_id in self.parent_concepts
    
    def get_hierarchy_depth(self) -> int:
        """获取层次深度"""
        return len(self.parent_concepts)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "concept_type": self.concept_type.value,
            "parent_concepts": list(self.parent_concepts),
            "child_concepts": list(self.child_concepts),
            "related_concepts": list(self.related_concepts),
            "abstraction_level": self.abstraction_level,
            "domain": self.domain,
            "definition": self.definition,
            "examples": self.examples,
            "properties": self.properties,
            "metadata": self.metadata.model_dump(),
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "version": self.version
        }
    
    def validate_node(self) -> bool:
        """验证概念节点有效性"""
        if not self.name or not self.concept_type:
            return False
        
        # 检查抽象层级合理性
        if self.abstraction_level < 0:
            return False
        
        # 检查循环依赖
        if self.id in self.parent_concepts or self.id in self.child_concepts:
            return False
        
        return True


class ConceptHierarchy:
    """概念层次结构管理器"""
    
    def __init__(self):
        self.concepts: Dict[str, Concept] = {}
        self.root_concepts: Set[str] = set()
    
    def add_concept(self, concept: Concept) -> None:
        """添加概念到层次结构"""
        self.concepts[concept.id] = concept
        
        # 如果没有父概念，则为根概念
        if not concept.parent_concepts:
            self.root_concepts.add(concept.id)
    
    def get_concept(self, concept_id: str) -> Optional[Concept]:
        """获取概念"""
        return self.concepts.get(concept_id)
    
    def get_ancestors(self, concept_id: str) -> List[Concept]:
        """获取所有祖先概念"""
        ancestors = []
        concept = self.get_concept(concept_id)
        
        if not concept:
            return ancestors
        
        visited = set()
        queue = list(concept.parent_concepts)
        
        while queue:
            parent_id = queue.pop(0)
            if parent_id in visited:
                continue
            
            visited.add(parent_id)
            parent_concept = self.get_concept(parent_id)
            
            if parent_concept:
                ancestors.append(parent_concept)
                queue.extend(parent_concept.parent_concepts)
        
        return ancestors
    
    def get_descendants(self, concept_id: str) -> List[Concept]:
        """获取所有后代概念"""
        descendants = []
        concept = self.get_concept(concept_id)
        
        if not concept:
            return descendants
        
        visited = set()
        queue = list(concept.child_concepts)
        
        while queue:
            child_id = queue.pop(0)
            if child_id in visited:
                continue
            
            visited.add(child_id)
            child_concept = self.get_concept(child_id)
            
            if child_concept:
                descendants.append(child_concept)
                queue.extend(child_concept.child_concepts)
        
        return descendants
    
    def find_common_ancestor(self, concept_id1: str, concept_id2: str) -> Optional[Concept]:
        """找到两个概念的最近公共祖先"""
        ancestors1 = set(c.id for c in self.get_ancestors(concept_id1))
        ancestors2 = set(c.id for c in self.get_ancestors(concept_id2))
        
        common_ancestors = ancestors1.intersection(ancestors2)
        
        if not common_ancestors:
            return None
        
        # 找到最近的公共祖先（抽象层级最低的）
        min_level = float('inf')
        closest_ancestor = None
        
        for ancestor_id in common_ancestors:
            ancestor = self.get_concept(ancestor_id)
            if ancestor and ancestor.abstraction_level < min_level:
                min_level = ancestor.abstraction_level
                closest_ancestor = ancestor
        
        return closest_ancestor
