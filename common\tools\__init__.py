"""
数据库工具包
提供统一的数据库操作接口，支持多种数据库类型，与业务逻辑解耦
"""

__version__ = "1.0.0"
__author__ = "AGI Development Team"
__description__ = "通用数据库工具包，支持Neo4j、InfluxDB、Milvus、PostgreSQL、MongoDB"

# 导入基础类和接口
from .database_base import (
    DatabaseInterface,
    DatabaseType,
    DatabaseConfig,
    QueryResult,
    OperationResult,
    ConnectionStatus,
    # 专用接口
    GraphDatabaseInterface,
    TimeSeriesDatabaseInterface,
    VectorDatabaseInterface,
    RelationalDatabaseInterface,
    DocumentDatabaseInterface,
    # 异常类
    DatabaseException,
    ConnectionException,
    QueryException,
    OperationException,
    ValidationException
)

# 导入具体工具类
from .neo4j_tool import Neo4jTool, Neo4jConfig
from .influxdb_tool import InfluxDBTool, InfluxDBConfig
from .milvus_tool import MilvusTool, MilvusConfig
from .postgresql_tool import PostgreSQLTool, PostgreSQLConfig
from .mongodb_tool import MongoDBTool, MongoDBConfig

# 导入工厂类和管理器
from .database_factory import (
    DatabaseFactory,
    DatabaseManager,
    get_database_manager,
    create_database_tool,
    add_database,
    get_database,
    connect_database,
    disconnect_database,
    # 类型特定的便捷函数
    get_graph_database,
    get_timeseries_database,
    get_vector_database,
    get_relational_database,
    get_document_database
)

# 定义公开的API
__all__ = [
    # 基础类和接口
    "DatabaseInterface",
    "DatabaseType",
    "DatabaseConfig",
    "QueryResult",
    "OperationResult",
    "ConnectionStatus",
    
    # 专用接口
    "GraphDatabaseInterface",
    "TimeSeriesDatabaseInterface",
    "VectorDatabaseInterface",
    "RelationalDatabaseInterface",
    "DocumentDatabaseInterface",
    
    # 异常类
    "DatabaseException",
    "ConnectionException",
    "QueryException",
    "OperationException",
    "ValidationException",
    
    # 具体工具类
    "Neo4jTool",
    "Neo4jConfig",
    "InfluxDBTool",
    "InfluxDBConfig",
    "MilvusTool",
    "MilvusConfig",
    "PostgreSQLTool",
    "PostgreSQLConfig",
    "MongoDBTool",
    "MongoDBConfig",
    
    # 工厂类和管理器
    "DatabaseFactory",
    "DatabaseManager",
    "get_database_manager",
    "create_database_tool",
    "add_database",
    "get_database",
    "connect_database",
    "disconnect_database",
    
    # 类型特定的便捷函数
    "get_graph_database",
    "get_timeseries_database",
    "get_vector_database",
    "get_relational_database",
    "get_document_database"
]

# 支持的数据库类型信息
SUPPORTED_DATABASES = {
    "neo4j": {
        "name": "Neo4j",
        "type": "图数据库",
        "description": "高性能图数据库，适合复杂关系查询",
        "tool_class": "Neo4jTool",
        "config_class": "Neo4jConfig",
        "interface": "GraphDatabaseInterface",
        "use_cases": ["知识图谱", "社交网络", "推荐系统", "欺诈检测"]
    },
    "influxdb": {
        "name": "InfluxDB",
        "type": "时序数据库",
        "description": "专为时序数据优化的数据库",
        "tool_class": "InfluxDBTool",
        "config_class": "InfluxDBConfig",
        "interface": "TimeSeriesDatabaseInterface",
        "use_cases": ["监控指标", "IoT数据", "日志分析", "性能监控"]
    },
    "milvus": {
        "name": "Milvus",
        "type": "向量数据库",
        "description": "专为向量相似性搜索设计的数据库",
        "tool_class": "MilvusTool",
        "config_class": "MilvusConfig",
        "interface": "VectorDatabaseInterface",
        "use_cases": ["相似性搜索", "推荐系统", "图像检索", "自然语言处理"]
    },
    "postgresql": {
        "name": "PostgreSQL",
        "type": "关系型数据库",
        "description": "功能强大的开源关系型数据库",
        "tool_class": "PostgreSQLTool",
        "config_class": "PostgreSQLConfig",
        "interface": "RelationalDatabaseInterface",
        "use_cases": ["事务处理", "数据仓库", "Web应用", "企业应用"]
    },
    "mongodb": {
        "name": "MongoDB",
        "type": "文档数据库",
        "description": "灵活的NoSQL文档数据库",
        "tool_class": "MongoDBTool",
        "config_class": "MongoDBConfig",
        "interface": "DocumentDatabaseInterface",
        "use_cases": ["内容管理", "用户配置", "产品目录", "实时分析"]
    }
}

def get_supported_databases():
    """获取支持的数据库信息"""
    return SUPPORTED_DATABASES

def get_database_info(db_type: str):
    """获取特定数据库的信息"""
    return SUPPORTED_DATABASES.get(db_type.lower())

def list_database_types():
    """列出所有支持的数据库类型"""
    return list(SUPPORTED_DATABASES.keys())

def get_dependencies():
    """获取依赖信息"""
    return {
        "neo4j": ">=5.0.0",
        "influxdb-client": ">=1.36.0",
        "pymilvus": ">=2.3.0",
        "psycopg2-binary": ">=2.9.0",
        "pymongo": ">=4.0.0"
    }

def print_package_info():
    """打印包信息"""
    print(f"数据库工具包 v{__version__}")
    print(f"作者: {__author__}")
    print(f"描述: {__description__}")
    print("\n支持的数据库:")
    for db_type, info in SUPPORTED_DATABASES.items():
        print(f"  - {info['name']} ({info['type']})")
        print(f"    {info['description']}")
        print(f"    用途: {', '.join(info['use_cases'])}")
    print(f"\n依赖包:")
    for pkg, version in get_dependencies().items():
        print(f"  - {pkg} {version}")

# 快速开始函数
def quick_start():
    """快速开始指南"""
    print("数据库工具包快速开始:")
    print("1. 创建数据库工具:")
    print("   from common.tools import create_database_tool")
    print("   tool = create_database_tool('postgresql', config)")
    print("")
    print("2. 使用数据库管理器:")
    print("   from common.tools import get_database_manager")
    print("   manager = get_database_manager()")
    print("   manager.add_database('my_db', 'postgresql', config)")
    print("")
    print("3. 获取数据库实例:")
    print("   from common.tools import get_database")
    print("   db = get_database('my_db')")
    print("")
    print("更多示例请查看 examples/database_tools_example.py")

# 在导入时显示简要信息
import logging
logger = logging.getLogger(__name__)
logger.info(f"数据库工具包 v{__version__} 已加载")
logger.info(f"支持的数据库: {', '.join(list_database_types())}")

# 版本兼容性检查
def check_dependencies():
    """检查依赖包是否安装"""
    missing_deps = []
    deps = get_dependencies()
    
    for package in deps:
        try:
            if package == "neo4j":
                import neo4j
            elif package == "influxdb-client":
                import influxdb_client
            elif package == "pymilvus":
                import pymilvus
            elif package == "psycopg2-binary":
                import psycopg2
            elif package == "pymongo":
                import pymongo
        except ImportError:
            missing_deps.append(package)
    
    if missing_deps:
        logger.warning(f"缺少依赖包: {', '.join(missing_deps)}")
        logger.warning("请使用 pip install 安装缺少的依赖包")
    else:
        logger.info("所有依赖包已安装")
    
    return len(missing_deps) == 0

# 自动检查依赖
try:
    check_dependencies()
except Exception as e:
    logger.warning(f"依赖检查失败: {e}")

# 工具类映射（用于动态创建）
TOOL_CLASSES = {
    "neo4j": Neo4jTool,
    "influxdb": InfluxDBTool,
    "milvus": MilvusTool,
    "postgresql": PostgreSQLTool,
    "mongodb": MongoDBTool
}

CONFIG_CLASSES = {
    "neo4j": Neo4jConfig,
    "influxdb": InfluxDBConfig,
    "milvus": MilvusConfig,
    "postgresql": PostgreSQLConfig,
    "mongodb": MongoDBConfig
}
