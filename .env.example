# Application Settings
DEBUG=false
APP_NAME="AGI Knowledge Graph System"

# Database Configuration
DATABASE_URL=sqlite:///./agi_system.db

# Neo4j Graph Database
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_neo4j_password

# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-3.5-turbo

# API Settings
API_HOST=0.0.0.0
API_PORT=8000

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/agi_system.log

# Knowledge Graph Settings
MAX_GRAPH_DEPTH=5
SIMILARITY_THRESHOLD=0.7

# Learning Settings
LEARNING_RATE=0.001
FEEDBACK_WEIGHT=0.8

# Context Settings
CONTEXT_WINDOW_SIZE=100
CONTEXT_UPDATE_INTERVAL=60

# Security Settings
SECRET_KEY=your-super-secret-key-change-this-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30
