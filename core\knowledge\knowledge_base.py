"""
知识库模块 - 事实性、程序性和描述性知识的集合
"""
from typing import Dict, Any, List, Optional, Set, Union
from enum import Enum
from datetime import datetime
from pydantic import BaseModel, Field
from uuid import uuid4


class KnowledgeType(str, Enum):
    """知识类型枚举"""
    FACTUAL = "factual"           # 事实性知识
    PROCEDURAL = "procedural"     # 程序性知识
    DECLARATIVE = "declarative"   # 描述性知识
    CONCEPTUAL = "conceptual"     # 概念性知识
    METACOGNITIVE = "metacognitive"  # 元认知知识
    EXPERIENTIAL = "experiential"    # 经验性知识


class KnowledgeSource(str, Enum):
    """知识来源枚举"""
    MANUAL = "manual"             # 手动输入
    LEARNED = "learned"           # 学习获得
    INFERRED = "inferred"         # 推理得出
    EXTERNAL = "external"         # 外部导入
    GENERATED = "generated"       # 自动生成


class KnowledgeItem(BaseModel):
    """知识项"""
    
    id: str = Field(default_factory=lambda: str(uuid4()))
    title: str = Field(..., description="知识标题")
    content: Union[str, Dict[str, Any]] = Field(..., description="知识内容")
    knowledge_type: KnowledgeType = Field(..., description="知识类型")
    
    # 元数据
    description: Optional[str] = Field(None, description="知识描述")
    keywords: List[str] = Field(default_factory=list, description="关键词")
    tags: List[str] = Field(default_factory=list, description="标签")
    category: Optional[str] = Field(None, description="分类")
    domain: Optional[str] = Field(None, description="领域")
    
    # 质量指标
    confidence: float = Field(default=1.0, ge=0.0, le=1.0, description="置信度")
    reliability: float = Field(default=1.0, ge=0.0, le=1.0, description="可靠性")
    importance: float = Field(default=0.5, ge=0.0, le=1.0, description="重要性")
    
    # 来源信息
    source: KnowledgeSource = Field(default=KnowledgeSource.MANUAL, description="知识来源")
    source_reference: Optional[str] = Field(None, description="来源引用")
    author: Optional[str] = Field(None, description="作者")
    
    # 关联信息
    related_concepts: Set[str] = Field(default_factory=set, description="相关概念ID")
    related_entities: Set[str] = Field(default_factory=set, description="相关实体ID")
    prerequisites: Set[str] = Field(default_factory=set, description="前置知识ID")
    
    # 时间信息
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    valid_from: Optional[datetime] = Field(None, description="有效开始时间")
    valid_to: Optional[datetime] = Field(None, description="有效结束时间")
    
    # 使用统计
    access_count: int = Field(default=0, description="访问次数")
    last_accessed: Optional[datetime] = Field(None, description="最后访问时间")
    
    class Config:
        arbitrary_types_allowed = True
    
    def add_keyword(self, keyword: str) -> None:
        """添加关键词"""
        if keyword not in self.keywords:
            self.keywords.append(keyword)
            self.updated_at = datetime.now()
    
    def add_tag(self, tag: str) -> None:
        """添加标签"""
        if tag not in self.tags:
            self.tags.append(tag)
            self.updated_at = datetime.now()
    
    def add_related_concept(self, concept_id: str) -> None:
        """添加相关概念"""
        self.related_concepts.add(concept_id)
        self.updated_at = datetime.now()
    
    def add_related_entity(self, entity_id: str) -> None:
        """添加相关实体"""
        self.related_entities.add(entity_id)
        self.updated_at = datetime.now()
    
    def add_prerequisite(self, prerequisite_id: str) -> None:
        """添加前置知识"""
        self.prerequisites.add(prerequisite_id)
        self.updated_at = datetime.now()
    
    def is_valid_at(self, check_time: datetime) -> bool:
        """检查知识在指定时间是否有效"""
        if self.valid_from and check_time < self.valid_from:
            return False
        if self.valid_to and check_time > self.valid_to:
            return False
        return True
    
    def record_access(self) -> None:
        """记录访问"""
        self.access_count += 1
        self.last_accessed = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "title": self.title,
            "content": self.content,
            "knowledge_type": self.knowledge_type.value,
            "description": self.description,
            "keywords": self.keywords,
            "tags": self.tags,
            "category": self.category,
            "domain": self.domain,
            "confidence": self.confidence,
            "reliability": self.reliability,
            "importance": self.importance,
            "source": self.source.value,
            "source_reference": self.source_reference,
            "author": self.author,
            "related_concepts": list(self.related_concepts),
            "related_entities": list(self.related_entities),
            "prerequisites": list(self.prerequisites),
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "valid_from": self.valid_from.isoformat() if self.valid_from else None,
            "valid_to": self.valid_to.isoformat() if self.valid_to else None,
            "access_count": self.access_count,
            "last_accessed": self.last_accessed.isoformat() if self.last_accessed else None
        }


class KnowledgeBase:
    """知识库"""
    
    def __init__(self):
        self.knowledge_items: Dict[str, KnowledgeItem] = {}
        
        # 索引
        self.type_index: Dict[KnowledgeType, Set[str]] = {}
        self.category_index: Dict[str, Set[str]] = {}
        self.domain_index: Dict[str, Set[str]] = {}
        self.keyword_index: Dict[str, Set[str]] = {}
        self.tag_index: Dict[str, Set[str]] = {}
    
    def add_knowledge(self, knowledge_item: KnowledgeItem) -> None:
        """添加知识项"""
        self.knowledge_items[knowledge_item.id] = knowledge_item
        self._update_indices(knowledge_item)
    
    def get_knowledge(self, knowledge_id: str) -> Optional[KnowledgeItem]:
        """获取知识项"""
        knowledge_item = self.knowledge_items.get(knowledge_id)
        if knowledge_item:
            knowledge_item.record_access()
        return knowledge_item
    
    def search_by_type(self, knowledge_type: KnowledgeType) -> List[KnowledgeItem]:
        """按类型搜索知识"""
        knowledge_ids = self.type_index.get(knowledge_type, set())
        return [self.knowledge_items[kid] for kid in knowledge_ids 
                if kid in self.knowledge_items]
    
    def search_by_category(self, category: str) -> List[KnowledgeItem]:
        """按分类搜索知识"""
        knowledge_ids = self.category_index.get(category, set())
        return [self.knowledge_items[kid] for kid in knowledge_ids 
                if kid in self.knowledge_items]
    
    def search_by_domain(self, domain: str) -> List[KnowledgeItem]:
        """按领域搜索知识"""
        knowledge_ids = self.domain_index.get(domain, set())
        return [self.knowledge_items[kid] for kid in knowledge_ids 
                if kid in self.knowledge_items]
    
    def search_by_keyword(self, keyword: str) -> List[KnowledgeItem]:
        """按关键词搜索知识"""
        knowledge_ids = self.keyword_index.get(keyword.lower(), set())
        return [self.knowledge_items[kid] for kid in knowledge_ids 
                if kid in self.knowledge_items]
    
    def search_by_tag(self, tag: str) -> List[KnowledgeItem]:
        """按标签搜索知识"""
        knowledge_ids = self.tag_index.get(tag, set())
        return [self.knowledge_items[kid] for kid in knowledge_ids 
                if kid in self.knowledge_items]
    
    def full_text_search(self, query: str) -> List[KnowledgeItem]:
        """全文搜索"""
        query_lower = query.lower()
        results = []
        
        for knowledge_item in self.knowledge_items.values():
            # 搜索标题
            if query_lower in knowledge_item.title.lower():
                results.append(knowledge_item)
                continue
            
            # 搜索描述
            if knowledge_item.description and query_lower in knowledge_item.description.lower():
                results.append(knowledge_item)
                continue
            
            # 搜索内容
            if isinstance(knowledge_item.content, str):
                if query_lower in knowledge_item.content.lower():
                    results.append(knowledge_item)
                    continue
            
            # 搜索关键词
            for keyword in knowledge_item.keywords:
                if query_lower in keyword.lower():
                    results.append(knowledge_item)
                    break
        
        return results
    
    def get_related_knowledge(self, knowledge_id: str, max_depth: int = 2) -> List[KnowledgeItem]:
        """获取相关知识"""
        if knowledge_id not in self.knowledge_items:
            return []
        
        related_ids = set()
        visited = set()
        queue = [(knowledge_id, 0)]
        
        while queue:
            current_id, depth = queue.pop(0)
            
            if current_id in visited or depth > max_depth:
                continue
            
            visited.add(current_id)
            current_item = self.knowledge_items.get(current_id)
            
            if current_item and current_id != knowledge_id:
                related_ids.add(current_id)
            
            if current_item and depth < max_depth:
                # 通过相关概念查找
                for concept_id in current_item.related_concepts:
                    for item_id, item in self.knowledge_items.items():
                        if concept_id in item.related_concepts and item_id not in visited:
                            queue.append((item_id, depth + 1))
                
                # 通过相关实体查找
                for entity_id in current_item.related_entities:
                    for item_id, item in self.knowledge_items.items():
                        if entity_id in item.related_entities and item_id not in visited:
                            queue.append((item_id, depth + 1))
        
        return [self.knowledge_items[rid] for rid in related_ids 
                if rid in self.knowledge_items]
    
    def get_knowledge_statistics(self) -> Dict[str, Any]:
        """获取知识库统计信息"""
        total_items = len(self.knowledge_items)
        
        if total_items == 0:
            return {"total_items": 0}
        
        # 按类型统计
        type_stats = {}
        for knowledge_type in KnowledgeType:
            count = len(self.type_index.get(knowledge_type, set()))
            type_stats[knowledge_type.value] = count
        
        # 按来源统计
        source_stats = {}
        for item in self.knowledge_items.values():
            source = item.source.value
            source_stats[source] = source_stats.get(source, 0) + 1
        
        # 质量统计
        confidences = [item.confidence for item in self.knowledge_items.values()]
        reliabilities = [item.reliability for item in self.knowledge_items.values()]
        importances = [item.importance for item in self.knowledge_items.values()]
        
        return {
            "total_items": total_items,
            "type_distribution": type_stats,
            "source_distribution": source_stats,
            "average_confidence": sum(confidences) / len(confidences),
            "average_reliability": sum(reliabilities) / len(reliabilities),
            "average_importance": sum(importances) / len(importances),
            "total_categories": len(self.category_index),
            "total_domains": len(self.domain_index),
            "total_keywords": len(self.keyword_index),
            "total_tags": len(self.tag_index)
        }
    
    def _update_indices(self, knowledge_item: KnowledgeItem) -> None:
        """更新索引"""
        # 类型索引
        if knowledge_item.knowledge_type not in self.type_index:
            self.type_index[knowledge_item.knowledge_type] = set()
        self.type_index[knowledge_item.knowledge_type].add(knowledge_item.id)
        
        # 分类索引
        if knowledge_item.category:
            if knowledge_item.category not in self.category_index:
                self.category_index[knowledge_item.category] = set()
            self.category_index[knowledge_item.category].add(knowledge_item.id)
        
        # 领域索引
        if knowledge_item.domain:
            if knowledge_item.domain not in self.domain_index:
                self.domain_index[knowledge_item.domain] = set()
            self.domain_index[knowledge_item.domain].add(knowledge_item.id)
        
        # 关键词索引
        for keyword in knowledge_item.keywords:
            keyword_lower = keyword.lower()
            if keyword_lower not in self.keyword_index:
                self.keyword_index[keyword_lower] = set()
            self.keyword_index[keyword_lower].add(knowledge_item.id)
        
        # 标签索引
        for tag in knowledge_item.tags:
            if tag not in self.tag_index:
                self.tag_index[tag] = set()
            self.tag_index[tag].add(knowledge_item.id)
