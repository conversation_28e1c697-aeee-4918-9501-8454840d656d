#!/usr/bin/env python3
"""
AGI Knowledge Graph System - 快速启动脚本
"""
import sys
import subprocess
import os
from pathlib import Path


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要 Python 3.8 或更高版本")
        print(f"当前版本: {sys.version}")
        sys.exit(1)
    print(f"✅ Python 版本检查通过: {sys.version.split()[0]}")


def check_dependencies():
    """检查依赖是否安装"""
    required_packages = [
        "fastapi",
        "uvicorn", 
        "pydantic",
        "sqlalchemy",
        "networkx",
        "loguru",
        "typer",
        "rich"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} (未安装)")
    
    if missing_packages:
        print(f"\n缺少依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False
    
    return True


def install_dependencies():
    """安装依赖"""
    print("正在安装依赖包...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False


def initialize_system():
    """初始化系统"""
    print("\n正在初始化系统...")
    try:
        from scripts.init_db import main as init_db_main
        init_db_main()
        print("✅ 系统初始化完成")
        return True
    except Exception as e:
        print(f"❌ 系统初始化失败: {e}")
        return False


def run_demo():
    """运行演示"""
    print("\n正在运行演示...")
    try:
        from examples.basic_usage import main as demo_main
        demo_main()
        print("✅ 演示运行完成")
        return True
    except Exception as e:
        print(f"❌ 演示运行失败: {e}")
        return False


def start_server():
    """启动服务器"""
    print("\n正在启动API服务器...")
    try:
        from main import main as server_main
        server_main()
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")


def main():
    """主函数"""
    print("=" * 60)
    print("🚀 AGI Knowledge Graph System - 快速启动")
    print("=" * 60)
    
    # 检查Python版本
    print("\n1. 检查Python版本...")
    check_python_version()
    
    # 检查依赖
    print("\n2. 检查依赖包...")
    if not check_dependencies():
        response = input("\n是否自动安装缺少的依赖? (y/n): ").lower().strip()
        if response == 'y':
            if not install_dependencies():
                print("依赖安装失败，请手动安装后重试")
                sys.exit(1)
        else:
            print("请手动安装依赖后重试")
            sys.exit(1)
    
    # 检查是否已初始化
    print("\n3. 检查系统初始化状态...")
    db_file = Path("agi_system.db")
    if not db_file.exists():
        print("系统尚未初始化")
        response = input("是否初始化系统? (y/n): ").lower().strip()
        if response == 'y':
            if not initialize_system():
                print("系统初始化失败")
                sys.exit(1)
        else:
            print("跳过初始化")
    else:
        print("✅ 系统已初始化")
    
    # 询问用户操作
    print("\n4. 选择操作:")
    print("1. 运行演示")
    print("2. 启动API服务器")
    print("3. 退出")
    
    while True:
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == "1":
            run_demo()
            break
        elif choice == "2":
            start_server()
            break
        elif choice == "3":
            print("再见！")
            break
        else:
            print("无效选择，请输入 1-3")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()
