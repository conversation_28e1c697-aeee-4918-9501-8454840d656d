# AGI数据存储组件

## 概述

本目录包含AGI系统的核心数据存储组件，提供多种专业化的存储解决方案来满足不同类型数据的存储和查询需求。

## 存储方案架构

| 数据类型 | 存储方案 | 组件文件 | 理由 |
|---------|---------|----------|------|
| 知识图谱 | Neo4j图数据库 | `knowledge_graph.py` | 高效处理关联查询和复杂关系推理 |
| 执行日志 | InfluxDB时序数据库 | `execution_log.py` | 适合按时间分析进化趋势和性能监控 |
| 模型参数 | Milvus向量数据库 | `model_parameters.py` | 快速检索相似案例和向量相似性搜索 |
| 系统元数据 | PostgreSQL关系型数据库 | `system_metadata.py` | 保证事务完整性和ACID特性 |

## 组件详情

### 1. 知识图谱组件 (knowledge_graph.py)

**功能特性:**
- 概念和实体的图形化存储
- 语义关系建模和查询
- 最短路径查找
- 语义搜索和推理
- 图统计分析

**主要方法:**
```python
# 创建概念节点
create_concept_node(concept_data)

# 创建关系
create_relationship(start_id, end_id, relationship_type, properties)

# 语义搜索
semantic_search(query_text, node_types, limit)

# 查找相关概念
find_related_concepts(concept_id, relationship_types, max_depth)

# 最短路径查找
find_shortest_path(start_id, end_id, max_depth)
```

### 2. 执行日志组件 (execution_log.py)

**功能特性:**
- 执行事件记录
- 性能指标监控
- 学习进度跟踪
- 时序数据分析
- 错误分析和告警

**主要方法:**
```python
# 记录执行事件
log_execution_event(event)

# 记录性能指标
log_performance_metrics(metrics)

# 记录学习进度
log_learning_progress(progress)

# 查询执行事件
query_execution_events(start_time, end_time, filters)

# 获取性能趋势
get_performance_trend(component, metric, time_range)
```

### 3. 模型参数组件 (model_parameters.py)

**功能特性:**
- 模型向量化存储
- 相似性搜索
- 批量操作支持
- 参数过滤查询
- 集合统计分析

**主要方法:**
```python
# 插入模型向量
insert_model_vector(collection_name, model_vector)

# 搜索相似模型
search_similar_models(collection_name, query_vector, top_k)

# 根据参数搜索
search_by_parameters(collection_name, parameter_filters)

# 批量插入
batch_insert_model_vectors(collection_name, model_vectors)
```

### 4. 系统元数据组件 (system_metadata.py)

**功能特性:**
- 事务性元数据存储
- 版本控制和历史追踪
- 模式定义管理
- 复杂查询支持
- 数据完整性保证

**主要方法:**
```python
# 创建元数据记录
create_metadata_record(record)

# 更新元数据记录
update_metadata_record(record_id, updates, modified_by)

# 查询元数据记录
query_metadata_records(filters, limit, offset)

# 获取版本历史
get_version_history(entity_id)

# 模式定义管理
create_schema_definition(schema)
```

## 统一管理器 (data_storage_manager.py)

数据存储管理器提供统一的接口来管理所有存储组件：

**核心功能:**
- 统一初始化和配置管理
- 服务健康检查和监控
- 便捷的API接口
- 错误处理和日志记录
- 配置备份和恢复

**使用示例:**
```python
from data_storage_manager import initialize_storage_manager, get_storage_manager

# 初始化存储管理器
success = initialize_storage_manager(config_file="config/storage_config.json")

# 获取管理器实例
manager = get_storage_manager()

# 使用各种存储功能
manager.add_knowledge(knowledge_data)
manager.log_event(event_data)
manager.store_model(model_data)
manager.store_metadata(metadata)
```

## 配置文件

### 配置文件结构 (config/storage_config.json)

```json
{
  "knowledge_graph": {
    "enabled": true,
    "neo4j": {
      "uri": "bolt://localhost:7687",
      "username": "neo4j",
      "password": "password"
    }
  },
  "execution_log": {
    "enabled": true,
    "influxdb": {
      "url": "http://localhost:8086",
      "token": "your-token",
      "org": "your-org",
      "bucket": "agi-logs"
    }
  },
  "model_parameters": {
    "enabled": true,
    "milvus": {
      "host": "localhost",
      "port": "19530",
      "vector_dim": 512
    }
  },
  "system_metadata": {
    "enabled": true,
    "postgresql": {
      "host": "localhost",
      "port": 5432,
      "database": "agi_metadata",
      "username": "postgres",
      "password": "password"
    }
  }
}
```

## 安装依赖

```bash
# Neo4j驱动
pip install neo4j

# InfluxDB客户端
pip install influxdb-client

# Milvus客户端
pip install pymilvus

# PostgreSQL驱动
pip install psycopg2-binary

# 其他依赖
pip install numpy pandas
```

## 数据库部署

### 1. Neo4j部署
```bash
# Docker部署
docker run -d \
  --name neo4j \
  -p 7474:7474 -p 7687:7687 \
  -e NEO4J_AUTH=neo4j/password \
  neo4j:latest
```

### 2. InfluxDB部署
```bash
# Docker部署
docker run -d \
  --name influxdb \
  -p 8086:8086 \
  -e INFLUXDB_DB=agi_logs \
  influxdb:latest
```

### 3. Milvus部署
```bash
# Docker Compose部署
wget https://github.com/milvus-io/milvus/releases/download/v2.3.0/milvus-standalone-docker-compose.yml
docker-compose -f milvus-standalone-docker-compose.yml up -d
```

### 4. PostgreSQL部署
```bash
# Docker部署
docker run -d \
  --name postgres \
  -p 5432:5432 \
  -e POSTGRES_DB=agi_metadata \
  -e POSTGRES_USER=agi_user \
  -e POSTGRES_PASSWORD=password \
  postgres:latest
```

## 使用示例

查看 `examples/storage_usage_example.py` 文件获取完整的使用示例。

### 快速开始

```python
from component.data_storage_manager import initialize_storage_manager, get_storage_manager

# 1. 初始化存储管理器
initialize_storage_manager(config_file="config/storage_config.json")
manager = get_storage_manager()

# 2. 添加知识到图谱
knowledge_data = {
    "concepts": [{"concept_id": "ai_001", "concept_name": "人工智能"}],
    "relationships": [{"start_id": "ml_001", "end_id": "ai_001", "type": "is_a"}]
}
manager.add_knowledge(knowledge_data)

# 3. 记录执行日志
event_data = {
    "event_type": "training",
    "component": "neural_network",
    "status": "success",
    "duration_ms": 150.5
}
manager.log_event(event_data)

# 4. 存储模型参数
model_data = {
    "model_id": "model_001",
    "vector": [0.1, 0.2, 0.3, ...],  # 512维向量
    "parameters": {"layers": 3, "neurons": [128, 64, 10]}
}
manager.store_model(model_data)

# 5. 存储系统元数据
metadata = {
    "metadata_type": "concept",
    "entity_id": "ai_001",
    "content": {"name": "人工智能", "type": "abstract"}
}
manager.store_metadata(metadata)
```

## 性能优化

### 1. 知识图谱优化
- 使用适当的索引策略
- 批量操作减少网络开销
- 查询优化和缓存

### 2. 时序数据优化
- 合理设置数据保留策略
- 使用数据聚合减少存储
- 异步写入提高性能

### 3. 向量数据优化
- 选择合适的索引类型
- 调整搜索参数平衡精度和速度
- 使用分片提高并发性能

### 4. 关系数据优化
- 连接池管理
- 查询优化和索引设计
- 事务管理和锁优化

## 监控和维护

### 健康检查
```python
# 系统健康检查
health = manager.health_check()
print(f"系统状态: {health['overall_status']}")

# 获取统计信息
stats = manager.get_system_statistics()
print(f"可用服务: {stats['services']['available']}")
```

### 备份和恢复
- 定期备份配置和数据
- 测试恢复流程
- 监控存储空间使用

## 故障排除

### 常见问题
1. **连接失败**: 检查数据库服务状态和网络连接
2. **性能问题**: 检查索引配置和查询优化
3. **数据不一致**: 检查事务配置和并发控制
4. **内存不足**: 调整缓存大小和连接池配置

### 日志分析
- 启用详细日志记录
- 监控错误率和响应时间
- 分析性能瓶颈

## 扩展开发

### 添加新的存储组件
1. 实现存储管理器接口
2. 添加配置支持
3. 集成到统一管理器
4. 编写测试用例

### 自定义查询接口
1. 扩展查询方法
2. 添加过滤和聚合功能
3. 优化查询性能
4. 文档化API接口

---

更多详细信息请参考各组件的源代码和注释。
