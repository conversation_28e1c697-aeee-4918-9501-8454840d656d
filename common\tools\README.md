# 数据库工具包

## 概述

本工具包提供了统一的数据库操作接口，支持多种数据库类型，确保与业务逻辑解耦。通过抽象接口和工厂模式，可以轻松切换和管理不同类型的数据库。

## 支持的数据库

| 数据库 | 类型 | 工具类 | 用途 |
|--------|------|--------|------|
| **Neo4j** | 图数据库 | `Neo4jTool` | 知识图谱、社交网络、推荐系统 |
| **InfluxDB** | 时序数据库 | `InfluxDBTool` | 监控指标、IoT数据、性能分析 |
| **Milvus** | 向量数据库 | `MilvusTool` | 相似性搜索、推荐系统、AI应用 |
| **PostgreSQL** | 关系型数据库 | `PostgreSQLTool` | 事务处理、数据仓库、Web应用 |
| **MongoDB** | 文档数据库 | `MongoDBTool` | 内容管理、配置存储、实时分析 |

## 核心特性

### 🎯 统一接口
- 所有数据库工具实现相同的基础接口
- 标准化的连接、查询、操作方法
- 一致的错误处理和结果格式

### 🔧 类型特化
- 每种数据库类型有专门的接口扩展
- 图数据库：节点、关系操作
- 时序数据库：时间范围查询、聚合
- 向量数据库：相似性搜索、向量操作
- 关系型数据库：SQL操作、事务支持
- 文档数据库：文档CRUD、聚合查询

### 🏭 工厂模式
- 通过工厂类统一创建数据库工具
- 支持配置驱动的数据库选择
- 易于扩展新的数据库类型

### 📊 统一管理
- 数据库管理器统一管理多个连接
- 健康检查和监控
- 批量操作和统计信息

## 快速开始

### 1. 安装依赖

```bash
# Neo4j
pip install neo4j

# InfluxDB
pip install influxdb-client

# Milvus
pip install pymilvus

# PostgreSQL
pip install psycopg2-binary

# MongoDB
pip install pymongo
```

### 2. 基本使用

```python
from common.tools import create_database_tool

# 创建PostgreSQL工具
config = {
    "host": "localhost",
    "port": 5432,
    "database": "mydb",
    "username": "user",
    "password": "password"
}

pg_tool = create_database_tool("postgresql", config)

# 连接数据库
if pg_tool.connect():
    # 执行操作
    result = pg_tool.select_records("users", limit=10)
    print(f"Found {result.count} users")
    
    # 关闭连接
    pg_tool.disconnect()
```

### 3. 使用数据库管理器

```python
from common.tools import get_database_manager, add_database, get_database

# 添加数据库连接
add_database("main_db", "postgresql", {
    "host": "localhost",
    "port": 5432,
    "database": "main",
    "username": "user",
    "password": "password"
})

add_database("cache_db", "mongodb", {
    "host": "localhost",
    "port": 27017,
    "database": "cache"
})

# 获取管理器
manager = get_database_manager()

# 连接所有数据库
manager.connect_all()

# 使用特定数据库
pg_db = get_database("main_db")
mongo_db = get_database("cache_db")

# 健康检查
health = manager.health_check()
print(f"系统状态: {health['overall_health']}")
```

## 详细使用指南

### Neo4j 图数据库

```python
from common.tools import Neo4jTool, Neo4jConfig

# 配置
config = Neo4jConfig(
    uri="bolt://localhost:7687",
    username="neo4j",
    password="password"
)

neo4j_tool = Neo4jTool(config)
neo4j_tool.connect()

# 创建节点
result = neo4j_tool.create_node(
    labels=["Person"],
    properties={"name": "Alice", "age": 30}
)

# 创建关系
neo4j_tool.create_relationship(
    start_node_id="node1",
    end_node_id="node2",
    relationship_type="KNOWS"
)

# 查找节点
nodes = neo4j_tool.find_nodes(
    labels=["Person"],
    properties={"age": 30}
)

# 查找路径
path = neo4j_tool.find_path("node1", "node2", max_depth=3)
```

### InfluxDB 时序数据库

```python
from common.tools import InfluxDBTool, InfluxDBConfig
from datetime import datetime, timedelta

# 配置
config = InfluxDBConfig(
    url="http://localhost:8086",
    token="your-token",
    org="your-org",
    bucket="metrics"
)

influx_tool = InfluxDBTool(config)
influx_tool.connect()

# 写入数据点
influx_tool.write_point(
    measurement="temperature",
    tags={"location": "room1"},
    fields={"value": 23.5},
    timestamp=datetime.now()
)

# 范围查询
end_time = datetime.now()
start_time = end_time - timedelta(hours=1)

result = influx_tool.query_range(
    measurement="temperature",
    start_time=start_time,
    end_time=end_time,
    tags={"location": "room1"}
)

# 聚合查询
agg_result = influx_tool.aggregate_query(
    measurement="temperature",
    start_time=start_time,
    end_time=end_time,
    aggregation="mean",
    interval="5m"
)
```

### Milvus 向量数据库

```python
from common.tools import MilvusTool, MilvusConfig
import numpy as np

# 配置
config = MilvusConfig(
    host="localhost",
    port=19530
)

milvus_tool = MilvusTool(config)
milvus_tool.connect()

# 创建集合
milvus_tool.create_collection(
    collection_name="embeddings",
    dimension=128,
    index_type="IVF_FLAT"
)

# 插入向量
vectors = [np.random.random(128).tolist() for _ in range(100)]
ids = [f"vec_{i}" for i in range(100)]

milvus_tool.insert_vectors(
    collection_name="embeddings",
    vectors=vectors,
    ids=ids
)

# 搜索相似向量
query_vector = np.random.random(128).tolist()
results = milvus_tool.search_vectors(
    collection_name="embeddings",
    query_vectors=[query_vector],
    top_k=10
)
```

### PostgreSQL 关系型数据库

```python
from common.tools import PostgreSQLTool, PostgreSQLConfig

# 配置
config = PostgreSQLConfig(
    host="localhost",
    port=5432,
    database="mydb",
    username="user",
    password="password"
)

pg_tool = PostgreSQLTool(config)
pg_tool.connect()

# 创建表
schema = {
    "id": "SERIAL PRIMARY KEY",
    "name": "VARCHAR(100)",
    "email": "VARCHAR(255) UNIQUE"
}
pg_tool.create_table("users", schema)

# 插入记录
pg_tool.insert_record("users", {
    "name": "John Doe",
    "email": "<EMAIL>"
})

# 查询记录
result = pg_tool.select_records(
    table_name="users",
    conditions={"name": "John Doe"}
)

# 执行事务
operations = [
    {
        "type": "insert",
        "table": "users",
        "data": {"name": "Jane", "email": "<EMAIL>"}
    },
    {
        "type": "update",
        "table": "users",
        "data": {"name": "Jane Smith"},
        "conditions": {"email": "<EMAIL>"}
    }
]
pg_tool.execute_transaction(operations)
```

### MongoDB 文档数据库

```python
from common.tools import MongoDBTool, MongoDBConfig

# 配置
config = MongoDBConfig(
    host="localhost",
    port=27017,
    database="mydb"
)

mongo_tool = MongoDBTool(config)
mongo_tool.connect()

# 插入文档
mongo_tool.insert_document("users", {
    "name": "Alice",
    "age": 30,
    "skills": ["Python", "JavaScript"]
})

# 查找文档
result = mongo_tool.find_documents(
    collection_name="users",
    query={"age": {"$gte": 25}},
    limit=10
)

# 更新文档
mongo_tool.update_document(
    collection_name="users",
    query={"name": "Alice"},
    update={"$set": {"age": 31}}
)

# 聚合查询
pipeline = [
    {"$group": {"_id": None, "avg_age": {"$avg": "$age"}}}
]
agg_result = mongo_tool.aggregate("users", pipeline)
```

## 扩展开发

### 添加新的数据库类型

1. **创建配置类**：
```python
from common.tools.database_base import DatabaseConfig

class MyDBConfig(DatabaseConfig):
    def __init__(self, host, port, **kwargs):
        super().__init__(host=host, port=port, **kwargs)
        # 添加特定配置
```

2. **实现工具类**：
```python
from common.tools.database_base import DatabaseInterface

class MyDBTool(DatabaseInterface):
    @property
    def database_type(self):
        return DatabaseType.CUSTOM
    
    def connect(self):
        # 实现连接逻辑
        pass
    
    # 实现其他必需方法...
```

3. **注册到工厂**：
```python
from common.tools import DatabaseFactory

DatabaseFactory.register_tool("mydb", MyDBTool, MyDBConfig)
```

### 自定义接口

可以继承现有接口来创建特定用途的接口：

```python
from common.tools.database_base import DatabaseInterface

class CacheInterface(DatabaseInterface):
    @abstractmethod
    def get(self, key: str) -> Any:
        pass
    
    @abstractmethod
    def set(self, key: str, value: Any, ttl: int = None) -> bool:
        pass
    
    @abstractmethod
    def delete(self, key: str) -> bool:
        pass
```

## 最佳实践

### 1. 连接管理
- 使用连接池避免频繁连接
- 及时关闭不用的连接
- 实现连接重试机制

### 2. 错误处理
- 捕获并处理数据库特定异常
- 记录详细的错误日志
- 实现优雅降级

### 3. 性能优化
- 使用批量操作减少网络开销
- 合理设置查询限制
- 监控查询性能

### 4. 安全考虑
- 使用参数化查询防止注入
- 加密敏感配置信息
- 实现访问控制

## 故障排除

### 常见问题

1. **连接失败**
   - 检查数据库服务状态
   - 验证网络连接
   - 确认认证信息

2. **性能问题**
   - 检查索引配置
   - 优化查询语句
   - 调整连接池大小

3. **内存不足**
   - 限制查询结果大小
   - 使用流式处理
   - 调整缓存配置

### 调试技巧

- 启用详细日志记录
- 使用健康检查监控状态
- 分析执行时间和资源使用

## 示例代码

完整的使用示例请参考：
- `examples/database_tools_example.py` - 基础使用示例
- 各工具类的文档字符串中的示例代码

---

更多详细信息请参考各工具类的源代码和注释。
