"""
数据库工厂类和统一管理器
提供统一的数据库工具创建和管理接口，与业务逻辑解耦
"""

from typing import Dict, List, Optional, Any, Union, Type
from enum import Enum
import logging

from .database_base import (
    DatabaseInterface, DatabaseType, DatabaseConfig,
    GraphDatabaseInterface, TimeSeriesDatabaseInterface, VectorDatabaseInterface,
    RelationalDatabaseInterface, DocumentDatabaseInterface
)
from .neo4j_tool import Neo4jTool, Neo4jConfig
from .influxdb_tool import InfluxDBTool, InfluxDBConfig
from .milvus_tool import MilvusTool, MilvusConfig
from .postgresql_tool import PostgreSQLTool, PostgreSQLConfig
from .mongodb_tool import MongoDBTool, MongoDBConfig

logger = logging.getLogger(__name__)


class DatabaseFactory:
    """数据库工厂类"""
    
    # 注册的数据库工具类
    _registered_tools: Dict[str, Type[DatabaseInterface]] = {
        "neo4j": Neo4jTool,
        "influxdb": InfluxDBTool,
        "milvus": MilvusTool,
        "postgresql": PostgreSQLTool,
        "mongodb": MongoDBTool
    }
    
    # 注册的配置类
    _registered_configs: Dict[str, Type[DatabaseConfig]] = {
        "neo4j": Neo4jConfig,
        "influxdb": InfluxDBConfig,
        "milvus": MilvusConfig,
        "postgresql": PostgreSQLConfig,
        "mongodb": MongoDBConfig
    }
    
    @classmethod
    def create_database_tool(cls, db_type: str, config: Union[Dict[str, Any], DatabaseConfig]) -> Optional[DatabaseInterface]:
        """
        创建数据库工具实例
        
        Args:
            db_type: 数据库类型 (neo4j, influxdb, milvus, postgresql, mongodb)
            config: 数据库配置
            
        Returns:
            数据库工具实例或None
        """
        db_type = db_type.lower()
        
        if db_type not in cls._registered_tools:
            logger.error(f"Unsupported database type: {db_type}")
            return None
        
        try:
            # 如果config是字典，转换为对应的配置类
            if isinstance(config, dict):
                config_class = cls._registered_configs[db_type]
                config = config_class(**config)
            
            # 创建工具实例
            tool_class = cls._registered_tools[db_type]
            return tool_class(config)
            
        except Exception as e:
            logger.error(f"Failed to create {db_type} tool: {e}")
            return None
    
    @classmethod
    def register_tool(cls, db_type: str, tool_class: Type[DatabaseInterface], 
                     config_class: Type[DatabaseConfig]):
        """
        注册新的数据库工具
        
        Args:
            db_type: 数据库类型标识
            tool_class: 工具类
            config_class: 配置类
        """
        cls._registered_tools[db_type.lower()] = tool_class
        cls._registered_configs[db_type.lower()] = config_class
        logger.info(f"Registered database tool: {db_type}")
    
    @classmethod
    def get_supported_types(cls) -> List[str]:
        """获取支持的数据库类型"""
        return list(cls._registered_tools.keys())
    
    @classmethod
    def get_tool_info(cls, db_type: str) -> Dict[str, Any]:
        """获取工具信息"""
        db_type = db_type.lower()
        if db_type not in cls._registered_tools:
            return {}
        
        tool_class = cls._registered_tools[db_type]
        config_class = cls._registered_configs[db_type]
        
        return {
            "type": db_type,
            "tool_class": tool_class.__name__,
            "config_class": config_class.__name__,
            "database_type": tool_class.database_type if hasattr(tool_class, 'database_type') else None,
            "description": tool_class.__doc__ or ""
        }


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        """初始化数据库管理器"""
        self.connections: Dict[str, DatabaseInterface] = {}
        self.configs: Dict[str, DatabaseConfig] = {}
        
    def add_database(self, name: str, db_type: str, config: Union[Dict[str, Any], DatabaseConfig]) -> bool:
        """
        添加数据库连接
        
        Args:
            name: 连接名称
            db_type: 数据库类型
            config: 数据库配置
            
        Returns:
            是否添加成功
        """
        try:
            tool = DatabaseFactory.create_database_tool(db_type, config)
            if tool is None:
                return False
            
            self.connections[name] = tool
            self.configs[name] = tool.config
            
            logger.info(f"Added database connection: {name} ({db_type})")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add database {name}: {e}")
            return False
    
    def connect(self, name: str) -> bool:
        """
        连接到指定数据库
        
        Args:
            name: 连接名称
            
        Returns:
            是否连接成功
        """
        if name not in self.connections:
            logger.error(f"Database connection not found: {name}")
            return False
        
        return self.connections[name].connect()
    
    def disconnect(self, name: str) -> bool:
        """
        断开指定数据库连接
        
        Args:
            name: 连接名称
            
        Returns:
            是否断开成功
        """
        if name not in self.connections:
            logger.error(f"Database connection not found: {name}")
            return False
        
        return self.connections[name].disconnect()
    
    def connect_all(self) -> Dict[str, bool]:
        """
        连接所有数据库
        
        Returns:
            连接结果字典
        """
        results = {}
        for name in self.connections:
            results[name] = self.connect(name)
        return results
    
    def disconnect_all(self) -> Dict[str, bool]:
        """
        断开所有数据库连接
        
        Returns:
            断开结果字典
        """
        results = {}
        for name in self.connections:
            results[name] = self.disconnect(name)
        return results
    
    def get_database(self, name: str) -> Optional[DatabaseInterface]:
        """
        获取数据库工具实例
        
        Args:
            name: 连接名称
            
        Returns:
            数据库工具实例或None
        """
        return self.connections.get(name)
    
    def remove_database(self, name: str) -> bool:
        """
        移除数据库连接
        
        Args:
            name: 连接名称
            
        Returns:
            是否移除成功
        """
        if name not in self.connections:
            return False
        
        # 先断开连接
        self.disconnect(name)
        
        # 移除连接
        del self.connections[name]
        del self.configs[name]
        
        logger.info(f"Removed database connection: {name}")
        return True
    
    def list_databases(self) -> List[str]:
        """列出所有数据库连接名称"""
        return list(self.connections.keys())
    
    def get_connection_status(self) -> Dict[str, Dict[str, Any]]:
        """获取所有连接状态"""
        status = {}
        for name, db in self.connections.items():
            status[name] = {
                "type": db.database_type.value,
                "connected": db.is_connected(),
                "ping": db.ping() if db.is_connected() else False,
                "connection_info": db.get_connection_info()
            }
        return status
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        status = self.get_connection_status()
        
        total_connections = len(status)
        healthy_connections = sum(1 for s in status.values() if s["connected"] and s["ping"])
        
        return {
            "overall_health": "healthy" if healthy_connections == total_connections else "degraded",
            "total_connections": total_connections,
            "healthy_connections": healthy_connections,
            "unhealthy_connections": total_connections - healthy_connections,
            "connections": status
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = {
            "total_connections": len(self.connections),
            "connection_types": {},
            "connection_details": {}
        }
        
        # 统计连接类型
        for name, db in self.connections.items():
            db_type = db.database_type.value
            stats["connection_types"][db_type] = stats["connection_types"].get(db_type, 0) + 1
            
            # 获取详细统计
            try:
                stats["connection_details"][name] = db.get_statistics()
            except Exception as e:
                logger.error(f"Error getting statistics for {name}: {e}")
                stats["connection_details"][name] = {"error": str(e)}
        
        return stats
    
    def execute_on_all(self, operation: str, **kwargs) -> Dict[str, Any]:
        """
        在所有连接上执行操作
        
        Args:
            operation: 操作名称 (connect, disconnect, ping, get_statistics)
            **kwargs: 操作参数
            
        Returns:
            执行结果字典
        """
        results = {}
        
        for name, db in self.connections.items():
            try:
                if hasattr(db, operation):
                    method = getattr(db, operation)
                    results[name] = method(**kwargs)
                else:
                    results[name] = {"error": f"Operation {operation} not supported"}
            except Exception as e:
                results[name] = {"error": str(e)}
        
        return results


# 全局数据库管理器实例
_global_db_manager: Optional[DatabaseManager] = None


def get_database_manager() -> DatabaseManager:
    """获取全局数据库管理器实例"""
    global _global_db_manager
    if _global_db_manager is None:
        _global_db_manager = DatabaseManager()
    return _global_db_manager


def create_database_tool(db_type: str, config: Union[Dict[str, Any], DatabaseConfig]) -> Optional[DatabaseInterface]:
    """
    创建数据库工具实例（便捷函数）
    
    Args:
        db_type: 数据库类型
        config: 数据库配置
        
    Returns:
        数据库工具实例或None
    """
    return DatabaseFactory.create_database_tool(db_type, config)


def add_database(name: str, db_type: str, config: Union[Dict[str, Any], DatabaseConfig]) -> bool:
    """
    添加数据库连接到全局管理器（便捷函数）
    
    Args:
        name: 连接名称
        db_type: 数据库类型
        config: 数据库配置
        
    Returns:
        是否添加成功
    """
    return get_database_manager().add_database(name, db_type, config)


def get_database(name: str) -> Optional[DatabaseInterface]:
    """
    从全局管理器获取数据库工具实例（便捷函数）
    
    Args:
        name: 连接名称
        
    Returns:
        数据库工具实例或None
    """
    return get_database_manager().get_database(name)


def connect_database(name: str) -> bool:
    """
    连接到指定数据库（便捷函数）
    
    Args:
        name: 连接名称
        
    Returns:
        是否连接成功
    """
    return get_database_manager().connect(name)


def disconnect_database(name: str) -> bool:
    """
    断开指定数据库连接（便捷函数）
    
    Args:
        name: 连接名称
        
    Returns:
        是否断开成功
    """
    return get_database_manager().disconnect(name)


# 类型特定的便捷函数
def get_graph_database(name: str) -> Optional[GraphDatabaseInterface]:
    """获取图数据库实例"""
    db = get_database(name)
    return db if isinstance(db, GraphDatabaseInterface) else None


def get_timeseries_database(name: str) -> Optional[TimeSeriesDatabaseInterface]:
    """获取时序数据库实例"""
    db = get_database(name)
    return db if isinstance(db, TimeSeriesDatabaseInterface) else None


def get_vector_database(name: str) -> Optional[VectorDatabaseInterface]:
    """获取向量数据库实例"""
    db = get_database(name)
    return db if isinstance(db, VectorDatabaseInterface) else None


def get_relational_database(name: str) -> Optional[RelationalDatabaseInterface]:
    """获取关系型数据库实例"""
    db = get_database(name)
    return db if isinstance(db, RelationalDatabaseInterface) else None


def get_document_database(name: str) -> Optional[DocumentDatabaseInterface]:
    """获取文档数据库实例"""
    db = get_database(name)
    return db if isinstance(db, DocumentDatabaseInterface) else None
