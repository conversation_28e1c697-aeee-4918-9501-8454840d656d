# AGI Knowledge Graph System - Makefile

.PHONY: help install init demo serve test clean format lint docs

# 默认目标
help:
	@echo "AGI Knowledge Graph System - 可用命令:"
	@echo ""
	@echo "  install     - 安装依赖包"
	@echo "  init        - 初始化系统数据库"
	@echo "  demo        - 运行演示示例"
	@echo "  serve       - 启动API服务器"
	@echo "  test        - 运行测试套件"
	@echo "  format      - 格式化代码"
	@echo "  lint        - 代码质量检查"
	@echo "  clean       - 清理临时文件"
	@echo "  docs        - 生成文档"
	@echo "  setup       - 完整设置 (install + init)"
	@echo "  run         - 快速启动脚本"
	@echo ""

# 安装依赖
install:
	@echo "安装依赖包..."
	pip install -r requirements.txt
	@echo "✅ 依赖安装完成"

# 初始化系统
init:
	@echo "初始化系统..."
	python scripts/init_db.py
	@echo "✅ 系统初始化完成"

# 运行演示
demo:
	@echo "运行演示..."
	python examples/basic_usage.py

# 启动服务器
serve:
	@echo "启动API服务器..."
	python main.py

# 运行测试
test:
	@echo "运行测试套件..."
	pytest tests/ -v

# 代码格式化
format:
	@echo "格式化代码..."
	black .
	isort .
	@echo "✅ 代码格式化完成"

# 代码质量检查
lint:
	@echo "代码质量检查..."
	flake8 core/ examples/ scripts/ tests/ --max-line-length=100
	mypy core/ --ignore-missing-imports
	@echo "✅ 代码检查完成"

# 清理临时文件
clean:
	@echo "清理临时文件..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	find . -type f -name ".coverage" -delete
	find . -type d -name "htmlcov" -exec rm -rf {} +
	find . -type d -name ".pytest_cache" -exec rm -rf {} +
	find . -type d -name ".mypy_cache" -exec rm -rf {} +
	rm -f agi_system.db
	rm -rf logs/
	@echo "✅ 清理完成"

# 生成文档
docs:
	@echo "生成文档..."
	@echo "文档生成功能待实现"

# 完整设置
setup: install init
	@echo "✅ 系统设置完成"

# 快速启动
run:
	@echo "运行快速启动脚本..."
	python run.py

# 开发环境设置
dev-install:
	@echo "安装开发依赖..."
	pip install -r requirements.txt
	pip install pytest pytest-asyncio pytest-cov black isort flake8 mypy
	@echo "✅ 开发环境设置完成"

# 运行CLI工具
cli:
	@echo "启动CLI工具..."
	python cli.py

# 创建示例数据
sample-data:
	@echo "创建示例数据..."
	python cli.py create-sample

# 显示系统状态
status:
	@echo "系统状态:"
	python cli.py status

# 显示系统信息
info:
	@echo "系统信息:"
	python cli.py info

# 构建包
build:
	@echo "构建包..."
	python -m build
	@echo "✅ 包构建完成"

# 安装本地包
install-local:
	@echo "安装本地包..."
	pip install -e .
	@echo "✅ 本地包安装完成"
