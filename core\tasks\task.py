"""
任务管理模块
"""
from typing import Dict, Any, List, Optional, Set
from enum import Enum
from datetime import datetime, timedelta
from pydantic import BaseModel, Field
from uuid import uuid4


class TaskType(str, Enum):
    """任务类型枚举"""
    COGNITIVE = "cognitive"         # 认知任务
    ANALYTICAL = "analytical"       # 分析任务
    CREATIVE = "creative"          # 创造任务
    COMMUNICATION = "communication" # 沟通任务
    EXECUTION = "execution"        # 执行任务
    LEARNING = "learning"          # 学习任务
    MONITORING = "monitoring"      # 监控任务
    MAINTENANCE = "maintenance"    # 维护任务


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"            # 待处理
    READY = "ready"               # 就绪
    RUNNING = "running"           # 运行中
    PAUSED = "paused"             # 暂停
    COMPLETED = "completed"       # 已完成
    FAILED = "failed"             # 失败
    CANCELLED = "cancelled"       # 已取消


class TaskPriority(str, Enum):
    """任务优先级枚举"""
    LOW = "low"                   # 低优先级
    NORMAL = "normal"             # 普通优先级
    HIGH = "high"                 # 高优先级
    URGENT = "urgent"             # 紧急
    CRITICAL = "critical"         # 关键


class Task(BaseModel):
    """任务类"""
    
    id: str = Field(default_factory=lambda: str(uuid4()))
    name: str = Field(..., description="任务名称")
    description: str = Field(..., description="任务描述")
    task_type: TaskType = Field(..., description="任务类型")
    
    # 任务状态
    status: TaskStatus = Field(default=TaskStatus.PENDING, description="任务状态")
    priority: TaskPriority = Field(default=TaskPriority.NORMAL, description="优先级")
    
    # 任务层次
    parent_task_id: Optional[str] = Field(None, description="父任务ID")
    subtask_ids: Set[str] = Field(default_factory=set, description="子任务ID集合")
    
    # 依赖关系
    dependencies: Set[str] = Field(default_factory=set, description="依赖任务ID集合")
    dependents: Set[str] = Field(default_factory=set, description="依赖此任务的任务ID集合")
    
    # 执行参数
    parameters: Dict[str, Any] = Field(default_factory=dict, description="执行参数")
    expected_output: Optional[Dict[str, Any]] = Field(None, description="期望输出")
    actual_output: Optional[Dict[str, Any]] = Field(None, description="实际输出")
    
    # 时间管理
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    scheduled_at: Optional[datetime] = Field(None, description="计划执行时间")
    started_at: Optional[datetime] = Field(None, description="开始执行时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    deadline: Optional[datetime] = Field(None, description="截止时间")
    
    # 资源需求
    required_resources: Dict[str, Any] = Field(default_factory=dict, description="所需资源")
    allocated_resources: Dict[str, Any] = Field(default_factory=dict, description="已分配资源")
    
    # 执行约束
    constraints: List[str] = Field(default_factory=list, description="执行约束")
    preconditions: List[str] = Field(default_factory=list, description="前置条件")
    postconditions: List[str] = Field(default_factory=list, description="后置条件")
    
    # 进度跟踪
    progress: float = Field(default=0.0, ge=0.0, le=1.0, description="完成进度")
    estimated_duration: Optional[timedelta] = Field(None, description="预估执行时间")
    actual_duration: Optional[timedelta] = Field(None, description="实际执行时间")
    
    # 错误处理
    error_message: Optional[str] = Field(None, description="错误信息")
    retry_count: int = Field(default=0, description="重试次数")
    max_retries: int = Field(default=3, description="最大重试次数")
    
    # 元数据
    tags: List[str] = Field(default_factory=list, description="标签")
    category: Optional[str] = Field(None, description="分类")
    assignee: Optional[str] = Field(None, description="执行者")
    
    class Config:
        arbitrary_types_allowed = True
    
    def add_subtask(self, subtask_id: str) -> None:
        """添加子任务"""
        self.subtask_ids.add(subtask_id)
        self.updated_at = datetime.now()
    
    def add_dependency(self, task_id: str) -> None:
        """添加依赖任务"""
        self.dependencies.add(task_id)
        self.updated_at = datetime.now()
    
    def add_dependent(self, task_id: str) -> None:
        """添加依赖此任务的任务"""
        self.dependents.add(task_id)
        self.updated_at = datetime.now()
    
    def can_start(self, completed_tasks: Set[str]) -> bool:
        """检查是否可以开始执行"""
        if self.status != TaskStatus.READY:
            return False
        
        # 检查依赖任务是否都已完成
        for dep_id in self.dependencies:
            if dep_id not in completed_tasks:
                return False
        
        # 检查是否到了计划执行时间
        if self.scheduled_at and datetime.now() < self.scheduled_at:
            return False
        
        return True
    
    def start(self) -> None:
        """开始执行任务"""
        if self.status == TaskStatus.READY:
            self.status = TaskStatus.RUNNING
            self.started_at = datetime.now()
            self.updated_at = datetime.now()
    
    def complete(self, output: Optional[Dict[str, Any]] = None) -> None:
        """完成任务"""
        self.status = TaskStatus.COMPLETED
        self.completed_at = datetime.now()
        self.progress = 1.0
        
        if output:
            self.actual_output = output
        
        if self.started_at:
            self.actual_duration = self.completed_at - self.started_at
        
        self.updated_at = datetime.now()
    
    def fail(self, error_message: str) -> None:
        """任务失败"""
        self.status = TaskStatus.FAILED
        self.error_message = error_message
        self.updated_at = datetime.now()
    
    def pause(self) -> None:
        """暂停任务"""
        if self.status == TaskStatus.RUNNING:
            self.status = TaskStatus.PAUSED
            self.updated_at = datetime.now()
    
    def resume(self) -> None:
        """恢复任务"""
        if self.status == TaskStatus.PAUSED:
            self.status = TaskStatus.RUNNING
            self.updated_at = datetime.now()
    
    def cancel(self) -> None:
        """取消任务"""
        self.status = TaskStatus.CANCELLED
        self.updated_at = datetime.now()
    
    def update_progress(self, progress: float) -> None:
        """更新进度"""
        self.progress = max(0.0, min(1.0, progress))
        self.updated_at = datetime.now()
    
    def is_overdue(self) -> bool:
        """检查是否超期"""
        if not self.deadline:
            return False
        
        if self.status == TaskStatus.COMPLETED:
            return self.completed_at > self.deadline
        else:
            return datetime.now() > self.deadline
    
    def get_priority_score(self) -> int:
        """获取优先级分数"""
        priority_scores = {
            TaskPriority.LOW: 1,
            TaskPriority.NORMAL: 2,
            TaskPriority.HIGH: 3,
            TaskPriority.URGENT: 4,
            TaskPriority.CRITICAL: 5
        }
        
        base_score = priority_scores.get(self.priority, 2)
        
        # 如果接近截止时间，提高优先级
        if self.deadline:
            time_to_deadline = self.deadline - datetime.now()
            if time_to_deadline.total_seconds() < 3600:  # 1小时内
                base_score += 2
            elif time_to_deadline.total_seconds() < 86400:  # 24小时内
                base_score += 1
        
        return base_score
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "task_type": self.task_type.value,
            "status": self.status.value,
            "priority": self.priority.value,
            "parent_task_id": self.parent_task_id,
            "subtask_ids": list(self.subtask_ids),
            "dependencies": list(self.dependencies),
            "dependents": list(self.dependents),
            "parameters": self.parameters,
            "expected_output": self.expected_output,
            "actual_output": self.actual_output,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "scheduled_at": self.scheduled_at.isoformat() if self.scheduled_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "deadline": self.deadline.isoformat() if self.deadline else None,
            "required_resources": self.required_resources,
            "allocated_resources": self.allocated_resources,
            "constraints": self.constraints,
            "preconditions": self.preconditions,
            "postconditions": self.postconditions,
            "progress": self.progress,
            "estimated_duration": str(self.estimated_duration) if self.estimated_duration else None,
            "actual_duration": str(self.actual_duration) if self.actual_duration else None,
            "error_message": self.error_message,
            "retry_count": self.retry_count,
            "max_retries": self.max_retries,
            "tags": self.tags,
            "category": self.category,
            "assignee": self.assignee
        }


class TaskManager:
    """任务管理器"""
    
    def __init__(self):
        self.tasks: Dict[str, Task] = {}
        
        # 索引
        self.status_index: Dict[TaskStatus, Set[str]] = {}
        self.type_index: Dict[TaskType, Set[str]] = {}
        self.priority_index: Dict[TaskPriority, Set[str]] = {}
        self.assignee_index: Dict[str, Set[str]] = {}
        
        # 依赖图
        self.dependency_graph: Dict[str, Set[str]] = {}
    
    def add_task(self, task: Task) -> None:
        """添加任务"""
        self.tasks[task.id] = task
        self._update_indices(task)
        self._update_dependency_graph(task)
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """获取任务"""
        return self.tasks.get(task_id)
    
    def update_task(self, task: Task) -> None:
        """更新任务"""
        if task.id in self.tasks:
            old_task = self.tasks[task.id]
            self._remove_from_indices(old_task)
            
            self.tasks[task.id] = task
            self._update_indices(task)
            self._update_dependency_graph(task)
    
    def remove_task(self, task_id: str) -> bool:
        """移除任务"""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            del self.tasks[task_id]
            self._remove_from_indices(task)
            self._remove_from_dependency_graph(task_id)
            return True
        return False
    
    def get_tasks_by_status(self, status: TaskStatus) -> List[Task]:
        """根据状态获取任务"""
        task_ids = self.status_index.get(status, set())
        return [self.tasks[tid] for tid in task_ids if tid in self.tasks]
    
    def get_ready_tasks(self) -> List[Task]:
        """获取就绪的任务"""
        ready_tasks = []
        completed_task_ids = set(
            tid for tid, task in self.tasks.items() 
            if task.status == TaskStatus.COMPLETED
        )
        
        for task in self.get_tasks_by_status(TaskStatus.READY):
            if task.can_start(completed_task_ids):
                ready_tasks.append(task)
        
        return ready_tasks
    
    def get_overdue_tasks(self) -> List[Task]:
        """获取超期任务"""
        return [task for task in self.tasks.values() if task.is_overdue()]
    
    def get_task_hierarchy(self, task_id: str) -> Dict[str, Any]:
        """获取任务层次结构"""
        task = self.get_task(task_id)
        if not task:
            return {}
        
        hierarchy = {
            "id": task_id,
            "name": task.name,
            "status": task.status.value,
            "subtasks": []
        }
        
        for subtask_id in task.subtask_ids:
            subtask_hierarchy = self.get_task_hierarchy(subtask_id)
            if subtask_hierarchy:
                hierarchy["subtasks"].append(subtask_hierarchy)
        
        return hierarchy
    
    def _update_indices(self, task: Task) -> None:
        """更新索引"""
        # 状态索引
        if task.status not in self.status_index:
            self.status_index[task.status] = set()
        self.status_index[task.status].add(task.id)
        
        # 类型索引
        if task.task_type not in self.type_index:
            self.type_index[task.task_type] = set()
        self.type_index[task.task_type].add(task.id)
        
        # 优先级索引
        if task.priority not in self.priority_index:
            self.priority_index[task.priority] = set()
        self.priority_index[task.priority].add(task.id)
        
        # 执行者索引
        if task.assignee:
            if task.assignee not in self.assignee_index:
                self.assignee_index[task.assignee] = set()
            self.assignee_index[task.assignee].add(task.id)
    
    def _remove_from_indices(self, task: Task) -> None:
        """从索引中移除"""
        # 从状态索引移除
        if task.status in self.status_index:
            self.status_index[task.status].discard(task.id)
        
        # 从类型索引移除
        if task.task_type in self.type_index:
            self.type_index[task.task_type].discard(task.id)
        
        # 从优先级索引移除
        if task.priority in self.priority_index:
            self.priority_index[task.priority].discard(task.id)
        
        # 从执行者索引移除
        if task.assignee and task.assignee in self.assignee_index:
            self.assignee_index[task.assignee].discard(task.id)
    
    def _update_dependency_graph(self, task: Task) -> None:
        """更新依赖图"""
        self.dependency_graph[task.id] = task.dependencies.copy()
    
    def _remove_from_dependency_graph(self, task_id: str) -> None:
        """从依赖图中移除"""
        if task_id in self.dependency_graph:
            del self.dependency_graph[task_id]
        
        # 从其他任务的依赖中移除
        for deps in self.dependency_graph.values():
            deps.discard(task_id)
