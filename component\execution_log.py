"""
执行日志组件 - 基于InfluxDB时序数据库
用于记录和分析AGI系统的执行日志，适合按时间分析进化趋势
"""

import logging
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import json
from influxdb_client import InfluxDBClient, Point, WritePrecision
from influxdb_client.client.write_api import SYNCHRONOUS
from influxdb_client.client.query_api import QueryApi

logger = logging.getLogger(__name__)


@dataclass
class ExecutionEvent:
    """执行事件数据结构"""
    timestamp: datetime
    event_type: str
    component: str
    operation: str
    status: str
    duration_ms: Optional[float] = None
    input_size: Optional[int] = None
    output_size: Optional[int] = None
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class PerformanceMetrics:
    """性能指标数据结构"""
    timestamp: datetime
    component: str
    cpu_usage: float
    memory_usage: float
    disk_io: float
    network_io: float
    response_time: float
    throughput: float
    error_rate: float


@dataclass
class LearningProgress:
    """学习进度数据结构"""
    timestamp: datetime
    model_id: str
    epoch: int
    loss: float
    accuracy: float
    validation_loss: Optional[float] = None
    validation_accuracy: Optional[float] = None
    learning_rate: Optional[float] = None


class ExecutionLogManager:
    """执行日志管理器"""
    
    def __init__(self, url: str, token: str, org: str, bucket: str):
        """
        初始化执行日志管理器
        
        Args:
            url: InfluxDB服务器URL
            token: 访问令牌
            org: 组织名称
            bucket: 存储桶名称
        """
        self.url = url
        self.token = token
        self.org = org
        self.bucket = bucket
        self.client: Optional[InfluxDBClient] = None
        self.write_api = None
        self.query_api = None
        
    def connect(self) -> bool:
        """建立数据库连接"""
        try:
            self.client = InfluxDBClient(
                url=self.url,
                token=self.token,
                org=self.org
            )
            self.write_api = self.client.write_api(write_options=SYNCHRONOUS)
            self.query_api = self.client.query_api()
            
            # 测试连接
            health = self.client.health()
            if health.status == "pass":
                logger.info(f"Successfully connected to InfluxDB at {self.url}")
                return True
            else:
                logger.error(f"InfluxDB health check failed: {health.message}")
                return False
        except Exception as e:
            logger.error(f"Failed to connect to InfluxDB: {e}")
            return False
    
    def disconnect(self):
        """关闭数据库连接"""
        if self.client:
            self.client.close()
            logger.info("Disconnected from InfluxDB")
    
    def log_execution_event(self, event: ExecutionEvent):
        """
        记录执行事件
        
        Args:
            event: 执行事件
        """
        try:
            point = Point("execution_event") \
                .tag("event_type", event.event_type) \
                .tag("component", event.component) \
                .tag("operation", event.operation) \
                .tag("status", event.status) \
                .time(event.timestamp, WritePrecision.MS)
            
            if event.duration_ms is not None:
                point = point.field("duration_ms", event.duration_ms)
            if event.input_size is not None:
                point = point.field("input_size", event.input_size)
            if event.output_size is not None:
                point = point.field("output_size", event.output_size)
            if event.error_message:
                point = point.field("error_message", event.error_message)
            if event.metadata:
                point = point.field("metadata", json.dumps(event.metadata))
            
            self.write_api.write(bucket=self.bucket, org=self.org, record=point)
            logger.debug(f"Logged execution event: {event.event_type}")
            
        except Exception as e:
            logger.error(f"Failed to log execution event: {e}")
    
    def log_performance_metrics(self, metrics: PerformanceMetrics):
        """
        记录性能指标
        
        Args:
            metrics: 性能指标
        """
        try:
            point = Point("performance_metrics") \
                .tag("component", metrics.component) \
                .field("cpu_usage", metrics.cpu_usage) \
                .field("memory_usage", metrics.memory_usage) \
                .field("disk_io", metrics.disk_io) \
                .field("network_io", metrics.network_io) \
                .field("response_time", metrics.response_time) \
                .field("throughput", metrics.throughput) \
                .field("error_rate", metrics.error_rate) \
                .time(metrics.timestamp, WritePrecision.MS)
            
            self.write_api.write(bucket=self.bucket, org=self.org, record=point)
            logger.debug(f"Logged performance metrics for {metrics.component}")
            
        except Exception as e:
            logger.error(f"Failed to log performance metrics: {e}")
    
    def log_learning_progress(self, progress: LearningProgress):
        """
        记录学习进度
        
        Args:
            progress: 学习进度
        """
        try:
            point = Point("learning_progress") \
                .tag("model_id", progress.model_id) \
                .field("epoch", progress.epoch) \
                .field("loss", progress.loss) \
                .field("accuracy", progress.accuracy) \
                .time(progress.timestamp, WritePrecision.MS)
            
            if progress.validation_loss is not None:
                point = point.field("validation_loss", progress.validation_loss)
            if progress.validation_accuracy is not None:
                point = point.field("validation_accuracy", progress.validation_accuracy)
            if progress.learning_rate is not None:
                point = point.field("learning_rate", progress.learning_rate)
            
            self.write_api.write(bucket=self.bucket, org=self.org, record=point)
            logger.debug(f"Logged learning progress for model {progress.model_id}")
            
        except Exception as e:
            logger.error(f"Failed to log learning progress: {e}")
    
    def query_execution_events(self, start_time: datetime, end_time: datetime,
                             component: Optional[str] = None,
                             event_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        查询执行事件
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            component: 组件过滤
            event_type: 事件类型过滤
            
        Returns:
            执行事件列表
        """
        try:
            query = f'''
            from(bucket: "{self.bucket}")
                |> range(start: {start_time.isoformat()}Z, stop: {end_time.isoformat()}Z)
                |> filter(fn: (r) => r._measurement == "execution_event")
            '''
            
            if component:
                query += f'|> filter(fn: (r) => r.component == "{component}")'
            if event_type:
                query += f'|> filter(fn: (r) => r.event_type == "{event_type}")'
            
            query += '|> sort(columns: ["_time"])'
            
            result = self.query_api.query(org=self.org, query=query)
            
            events = []
            for table in result:
                for record in table.records:
                    event_data = {
                        'timestamp': record.get_time(),
                        'component': record.values.get('component'),
                        'event_type': record.values.get('event_type'),
                        'operation': record.values.get('operation'),
                        'status': record.values.get('status'),
                        'field': record.get_field(),
                        'value': record.get_value()
                    }
                    events.append(event_data)
            
            logger.info(f"Queried {len(events)} execution events")
            return events
            
        except Exception as e:
            logger.error(f"Failed to query execution events: {e}")
            return []
    
    def get_performance_trend(self, component: str, metric: str,
                            start_time: datetime, end_time: datetime,
                            aggregation: str = "mean") -> List[Dict[str, Any]]:
        """
        获取性能趋势
        
        Args:
            component: 组件名称
            metric: 指标名称
            start_time: 开始时间
            end_time: 结束时间
            aggregation: 聚合方式 (mean, max, min, sum)
            
        Returns:
            性能趋势数据
        """
        try:
            query = f'''
            from(bucket: "{self.bucket}")
                |> range(start: {start_time.isoformat()}Z, stop: {end_time.isoformat()}Z)
                |> filter(fn: (r) => r._measurement == "performance_metrics")
                |> filter(fn: (r) => r.component == "{component}")
                |> filter(fn: (r) => r._field == "{metric}")
                |> aggregateWindow(every: 1m, fn: {aggregation}, createEmpty: false)
                |> sort(columns: ["_time"])
            '''
            
            result = self.query_api.query(org=self.org, query=query)
            
            trend_data = []
            for table in result:
                for record in table.records:
                    trend_data.append({
                        'timestamp': record.get_time(),
                        'value': record.get_value()
                    })
            
            logger.info(f"Retrieved performance trend for {component}.{metric}")
            return trend_data
            
        except Exception as e:
            logger.error(f"Failed to get performance trend: {e}")
            return []
    
    def get_learning_curve(self, model_id: str) -> Dict[str, List[Dict[str, Any]]]:
        """
        获取学习曲线
        
        Args:
            model_id: 模型ID
            
        Returns:
            学习曲线数据
        """
        try:
            query = f'''
            from(bucket: "{self.bucket}")
                |> range(start: -30d)
                |> filter(fn: (r) => r._measurement == "learning_progress")
                |> filter(fn: (r) => r.model_id == "{model_id}")
                |> sort(columns: ["_time"])
            '''
            
            result = self.query_api.query(org=self.org, query=query)
            
            learning_data = {
                'loss': [],
                'accuracy': [],
                'validation_loss': [],
                'validation_accuracy': []
            }
            
            for table in result:
                for record in table.records:
                    field = record.get_field()
                    if field in learning_data:
                        learning_data[field].append({
                            'timestamp': record.get_time(),
                            'epoch': record.values.get('epoch'),
                            'value': record.get_value()
                        })
            
            logger.info(f"Retrieved learning curve for model {model_id}")
            return learning_data
            
        except Exception as e:
            logger.error(f"Failed to get learning curve: {e}")
            return {}
    
    def get_error_analysis(self, start_time: datetime, end_time: datetime) -> Dict[str, Any]:
        """
        获取错误分析
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            错误分析结果
        """
        try:
            # 错误事件统计
            error_query = f'''
            from(bucket: "{self.bucket}")
                |> range(start: {start_time.isoformat()}Z, stop: {end_time.isoformat()}Z)
                |> filter(fn: (r) => r._measurement == "execution_event")
                |> filter(fn: (r) => r.status == "error" or r.status == "failed")
                |> group(columns: ["component", "event_type"])
                |> count()
            '''
            
            error_result = self.query_api.query(org=self.org, query=error_query)
            
            error_stats = {}
            for table in error_result:
                for record in table.records:
                    component = record.values.get('component')
                    event_type = record.values.get('event_type')
                    count = record.get_value()
                    
                    if component not in error_stats:
                        error_stats[component] = {}
                    error_stats[component][event_type] = count
            
            # 错误率趋势
            error_rate_query = f'''
            from(bucket: "{self.bucket}")
                |> range(start: {start_time.isoformat()}Z, stop: {end_time.isoformat()}Z)
                |> filter(fn: (r) => r._measurement == "performance_metrics")
                |> filter(fn: (r) => r._field == "error_rate")
                |> aggregateWindow(every: 1h, fn: mean, createEmpty: false)
                |> sort(columns: ["_time"])
            '''
            
            rate_result = self.query_api.query(org=self.org, query=error_rate_query)
            
            error_rate_trend = []
            for table in rate_result:
                for record in table.records:
                    error_rate_trend.append({
                        'timestamp': record.get_time(),
                        'component': record.values.get('component'),
                        'error_rate': record.get_value()
                    })
            
            return {
                'error_statistics': error_stats,
                'error_rate_trend': error_rate_trend
            }
            
        except Exception as e:
            logger.error(f"Failed to get error analysis: {e}")
            return {}
    
    def get_system_health(self) -> Dict[str, Any]:
        """
        获取系统健康状态
        
        Returns:
            系统健康状态
        """
        try:
            now = datetime.now()
            one_hour_ago = now - timedelta(hours=1)
            
            # 最近一小时的性能指标
            health_query = f'''
            from(bucket: "{self.bucket}")
                |> range(start: {one_hour_ago.isoformat()}Z)
                |> filter(fn: (r) => r._measurement == "performance_metrics")
                |> group(columns: ["component", "_field"])
                |> mean()
            '''
            
            result = self.query_api.query(org=self.org, query=health_query)
            
            health_data = {}
            for table in result:
                for record in table.records:
                    component = record.values.get('component')
                    field = record.get_field()
                    value = record.get_value()
                    
                    if component not in health_data:
                        health_data[component] = {}
                    health_data[component][field] = value
            
            # 计算健康评分
            for component, metrics in health_data.items():
                cpu_score = max(0, 100 - metrics.get('cpu_usage', 0))
                memory_score = max(0, 100 - metrics.get('memory_usage', 0))
                error_score = max(0, 100 - metrics.get('error_rate', 0) * 100)
                
                health_score = (cpu_score + memory_score + error_score) / 3
                health_data[component]['health_score'] = health_score
            
            return health_data
            
        except Exception as e:
            logger.error(f"Failed to get system health: {e}")
            return {}


class ExecutionLogService:
    """执行日志服务类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化执行日志服务
        
        Args:
            config: 配置信息，包含InfluxDB连接参数
        """
        self.config = config
        self.manager = ExecutionLogManager(
            url=config['influxdb']['url'],
            token=config['influxdb']['token'],
            org=config['influxdb']['org'],
            bucket=config['influxdb']['bucket']
        )
        
    def initialize(self) -> bool:
        """初始化服务"""
        return self.manager.connect()
    
    def shutdown(self):
        """关闭服务"""
        self.manager.disconnect()
    
    def log_event(self, event_data: Dict[str, Any]):
        """
        记录事件
        
        Args:
            event_data: 事件数据
        """
        event = ExecutionEvent(
            timestamp=datetime.fromisoformat(event_data.get('timestamp', datetime.now().isoformat())),
            event_type=event_data['event_type'],
            component=event_data['component'],
            operation=event_data['operation'],
            status=event_data['status'],
            duration_ms=event_data.get('duration_ms'),
            input_size=event_data.get('input_size'),
            output_size=event_data.get('output_size'),
            error_message=event_data.get('error_message'),
            metadata=event_data.get('metadata')
        )
        self.manager.log_execution_event(event)
    
    def log_metrics(self, metrics_data: Dict[str, Any]):
        """
        记录性能指标
        
        Args:
            metrics_data: 指标数据
        """
        metrics = PerformanceMetrics(
            timestamp=datetime.fromisoformat(metrics_data.get('timestamp', datetime.now().isoformat())),
            component=metrics_data['component'],
            cpu_usage=metrics_data['cpu_usage'],
            memory_usage=metrics_data['memory_usage'],
            disk_io=metrics_data['disk_io'],
            network_io=metrics_data['network_io'],
            response_time=metrics_data['response_time'],
            throughput=metrics_data['throughput'],
            error_rate=metrics_data['error_rate']
        )
        self.manager.log_performance_metrics(metrics)
    
    def log_learning(self, learning_data: Dict[str, Any]):
        """
        记录学习进度
        
        Args:
            learning_data: 学习数据
        """
        progress = LearningProgress(
            timestamp=datetime.fromisoformat(learning_data.get('timestamp', datetime.now().isoformat())),
            model_id=learning_data['model_id'],
            epoch=learning_data['epoch'],
            loss=learning_data['loss'],
            accuracy=learning_data['accuracy'],
            validation_loss=learning_data.get('validation_loss'),
            validation_accuracy=learning_data.get('validation_accuracy'),
            learning_rate=learning_data.get('learning_rate')
        )
        self.manager.log_learning_progress(progress)
    
    def query_logs(self, query: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        查询日志
        
        Args:
            query: 查询条件
            
        Returns:
            查询结果
        """
        query_type = query.get('type', 'execution_events')
        start_time = datetime.fromisoformat(query['start_time'])
        end_time = datetime.fromisoformat(query['end_time'])
        
        if query_type == 'execution_events':
            return self.manager.query_execution_events(
                start_time=start_time,
                end_time=end_time,
                component=query.get('component'),
                event_type=query.get('event_type')
            )
        elif query_type == 'performance_trend':
            return self.manager.get_performance_trend(
                component=query['component'],
                metric=query['metric'],
                start_time=start_time,
                end_time=end_time,
                aggregation=query.get('aggregation', 'mean')
            )
        elif query_type == 'learning_curve':
            return self.manager.get_learning_curve(query['model_id'])
        elif query_type == 'error_analysis':
            return self.manager.get_error_analysis(start_time, end_time)
        elif query_type == 'system_health':
            return self.manager.get_system_health()
        else:
            logger.warning(f"Unknown query type: {query_type}")
            return []
