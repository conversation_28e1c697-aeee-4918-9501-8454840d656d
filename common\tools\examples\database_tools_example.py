"""
数据库工具使用示例
演示如何使用各种数据库工具类进行数据操作
"""

import sys
import os
from datetime import datetime
import numpy as np

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database_factory import (
    DatabaseFactory, DatabaseManager, get_database_manager,
    create_database_tool, add_database, get_database
)
from neo4j_tool import Neo4jConfig
from influxdb_tool import InfluxDBConfig
from milvus_tool import MilvusConfig
from postgresql_tool import PostgreSQLConfig
from mongodb_tool import MongoDBConfig


def example_neo4j_operations():
    """Neo4j图数据库操作示例"""
    print("\n=== Neo4j图数据库操作示例 ===")
    
    # 创建配置
    config = Neo4jConfig(
        uri="bolt://localhost:7687",
        username="neo4j",
        password="password",
        database="neo4j"
    )
    
    # 创建工具实例
    neo4j_tool = create_database_tool("neo4j", config)
    if not neo4j_tool:
        print("Failed to create Neo4j tool")
        return
    
    # 连接数据库
    if not neo4j_tool.connect():
        print("Failed to connect to Neo4j")
        return
    
    print("Connected to Neo4j successfully")
    
    try:
        # 创建节点
        result = neo4j_tool.create_node(
            labels=["Person"],
            properties={"name": "Alice", "age": 30, "city": "New York"}
        )
        print(f"Create node result: {result.success}")
        
        # 创建另一个节点
        result2 = neo4j_tool.create_node(
            labels=["Person"],
            properties={"name": "Bob", "age": 25, "city": "San Francisco"}
        )
        
        # 创建关系
        if result.success and result2.success:
            rel_result = neo4j_tool.create_relationship(
                start_node_id=result.result_id,
                end_node_id=result2.result_id,
                relationship_type="KNOWS",
                properties={"since": "2020"}
            )
            print(f"Create relationship result: {rel_result.success}")
        
        # 查找节点
        find_result = neo4j_tool.find_nodes(
            labels=["Person"],
            properties={"city": "New York"}
        )
        print(f"Found {find_result.count} nodes")
        
        # 获取统计信息
        stats = neo4j_tool.get_statistics()
        print(f"Node count: {stats.get('node_count', 0)}")
        print(f"Relationship count: {stats.get('relationship_count', 0)}")
        
    finally:
        neo4j_tool.disconnect()


def example_influxdb_operations():
    """InfluxDB时序数据库操作示例"""
    print("\n=== InfluxDB时序数据库操作示例 ===")
    
    # 创建配置
    config = InfluxDBConfig(
        url="http://localhost:8086",
        token="your-token",
        org="your-org",
        bucket="test-bucket"
    )
    
    # 创建工具实例
    influx_tool = create_database_tool("influxdb", config)
    if not influx_tool:
        print("Failed to create InfluxDB tool")
        return
    
    # 连接数据库
    if not influx_tool.connect():
        print("Failed to connect to InfluxDB")
        return
    
    print("Connected to InfluxDB successfully")
    
    try:
        # 写入数据点
        result = influx_tool.write_point(
            measurement="temperature",
            tags={"location": "room1", "sensor": "sensor1"},
            fields={"value": 23.5, "humidity": 45.2},
            timestamp=datetime.now()
        )
        print(f"Write point result: {result.success}")
        
        # 批量写入数据点
        points = [
            {
                "measurement": "temperature",
                "tags": {"location": "room2", "sensor": "sensor2"},
                "fields": {"value": 24.1, "humidity": 42.8},
                "timestamp": datetime.now()
            },
            {
                "measurement": "temperature", 
                "tags": {"location": "room3", "sensor": "sensor3"},
                "fields": {"value": 22.9, "humidity": 48.1},
                "timestamp": datetime.now()
            }
        ]
        
        batch_result = influx_tool.write_points(points)
        print(f"Batch write result: {batch_result.success}, affected: {batch_result.affected_count}")
        
        # 范围查询
        from datetime import timedelta
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=1)
        
        query_result = influx_tool.query_range(
            measurement="temperature",
            start_time=start_time,
            end_time=end_time,
            tags={"location": "room1"}
        )
        print(f"Query result: {query_result.count} records")
        
        # 获取统计信息
        stats = influx_tool.get_statistics()
        print(f"Measurements: {stats.get('measurement_count', 0)}")
        
    finally:
        influx_tool.disconnect()


def example_milvus_operations():
    """Milvus向量数据库操作示例"""
    print("\n=== Milvus向量数据库操作示例 ===")
    
    # 创建配置
    config = MilvusConfig(
        host="localhost",
        port=19530,
        alias="default"
    )
    
    # 创建工具实例
    milvus_tool = create_database_tool("milvus", config)
    if not milvus_tool:
        print("Failed to create Milvus tool")
        return
    
    # 连接数据库
    if not milvus_tool.connect():
        print("Failed to connect to Milvus")
        return
    
    print("Connected to Milvus successfully")
    
    try:
        # 创建集合
        collection_name = "test_vectors"
        dimension = 128
        
        result = milvus_tool.create_collection(
            collection_name=collection_name,
            dimension=dimension,
            index_type="IVF_FLAT",
            metric_type="L2"
        )
        print(f"Create collection result: {result.success}")
        
        # 插入向量
        vectors = [np.random.random(dimension).tolist() for _ in range(10)]
        ids = [f"vec_{i}" for i in range(10)]
        metadata = [{"category": f"cat_{i%3}", "value": i} for i in range(10)]
        
        insert_result = milvus_tool.insert_vectors(
            collection_name=collection_name,
            vectors=vectors,
            ids=ids,
            metadata=metadata
        )
        print(f"Insert vectors result: {insert_result.success}, count: {insert_result.affected_count}")
        
        # 搜索向量
        query_vector = np.random.random(dimension).tolist()
        search_result = milvus_tool.search_vectors(
            collection_name=collection_name,
            query_vectors=[query_vector],
            top_k=5
        )
        print(f"Search result: {search_result.count} vectors found")
        
        # 获取统计信息
        stats = milvus_tool.get_statistics()
        print(f"Collections: {stats.get('collection_count', 0)}")
        
    finally:
        milvus_tool.disconnect()


def example_postgresql_operations():
    """PostgreSQL关系型数据库操作示例"""
    print("\n=== PostgreSQL关系型数据库操作示例 ===")
    
    # 创建配置
    config = PostgreSQLConfig(
        host="localhost",
        port=5432,
        database="test_db",
        username="postgres",
        password="password"
    )
    
    # 创建工具实例
    pg_tool = create_database_tool("postgresql", config)
    if not pg_tool:
        print("Failed to create PostgreSQL tool")
        return
    
    # 连接数据库
    if not pg_tool.connect():
        print("Failed to connect to PostgreSQL")
        return
    
    print("Connected to PostgreSQL successfully")
    
    try:
        # 创建表
        schema = {
            "id": "SERIAL PRIMARY KEY",
            "name": "VARCHAR(100) NOT NULL",
            "age": "INTEGER",
            "email": "VARCHAR(255) UNIQUE",
            "created_at": "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
        }
        
        result = pg_tool.create_table("users", schema)
        print(f"Create table result: {result.success}")
        
        # 插入记录
        user_data = {
            "name": "John Doe",
            "age": 30,
            "email": "<EMAIL>"
        }
        
        insert_result = pg_tool.insert_record("users", user_data)
        print(f"Insert record result: {insert_result.success}")
        
        # 批量插入记录
        users_data = [
            {"name": "Jane Smith", "age": 25, "email": "<EMAIL>"},
            {"name": "Bob Johnson", "age": 35, "email": "<EMAIL>"}
        ]
        
        batch_result = pg_tool.insert_records("users", users_data)
        print(f"Batch insert result: {batch_result.success}, count: {batch_result.affected_count}")
        
        # 查询记录
        select_result = pg_tool.select_records(
            table_name="users",
            conditions={"age": 30},
            limit=10
        )
        print(f"Select result: {select_result.count} records")
        
        # 更新记录
        update_result = pg_tool.update_record(
            table_name="users",
            data={"age": 31},
            conditions={"name": "John Doe"}
        )
        print(f"Update result: {update_result.success}, affected: {update_result.affected_count}")
        
        # 获取统计信息
        stats = pg_tool.get_statistics()
        print(f"Tables: {stats.get('table_count', 0)}")
        
    finally:
        pg_tool.disconnect()


def example_mongodb_operations():
    """MongoDB文档数据库操作示例"""
    print("\n=== MongoDB文档数据库操作示例 ===")
    
    # 创建配置
    config = MongoDBConfig(
        host="localhost",
        port=27017,
        database="test_db",
        username="admin",
        password="password"
    )
    
    # 创建工具实例
    mongo_tool = create_database_tool("mongodb", config)
    if not mongo_tool:
        print("Failed to create MongoDB tool")
        return
    
    # 连接数据库
    if not mongo_tool.connect():
        print("Failed to connect to MongoDB")
        return
    
    print("Connected to MongoDB successfully")
    
    try:
        # 创建集合
        collection_name = "users"
        result = mongo_tool.create_collection(collection_name)
        print(f"Create collection result: {result.success}")
        
        # 插入文档
        user_doc = {
            "name": "Alice Johnson",
            "age": 28,
            "email": "<EMAIL>",
            "skills": ["Python", "JavaScript", "SQL"],
            "created_at": datetime.now()
        }
        
        insert_result = mongo_tool.insert_document(collection_name, user_doc)
        print(f"Insert document result: {insert_result.success}")
        
        # 批量插入文档
        users_docs = [
            {
                "name": "Charlie Brown",
                "age": 32,
                "email": "<EMAIL>",
                "skills": ["Java", "C++"],
                "created_at": datetime.now()
            },
            {
                "name": "Diana Prince",
                "age": 27,
                "email": "<EMAIL>",
                "skills": ["Python", "Machine Learning"],
                "created_at": datetime.now()
            }
        ]
        
        batch_result = mongo_tool.insert_documents(collection_name, users_docs)
        print(f"Batch insert result: {batch_result.success}, count: {batch_result.affected_count}")
        
        # 查找文档
        find_result = mongo_tool.find_documents(
            collection_name=collection_name,
            query={"age": {"$gte": 25}},
            limit=10
        )
        print(f"Find result: {find_result.count} documents")
        
        # 更新文档
        update_result = mongo_tool.update_document(
            collection_name=collection_name,
            query={"name": "Alice Johnson"},
            update={"$set": {"age": 29}}
        )
        print(f"Update result: {update_result.success}, modified: {update_result.affected_count}")
        
        # 聚合查询
        pipeline = [
            {"$group": {"_id": None, "avg_age": {"$avg": "$age"}, "total": {"$sum": 1}}}
        ]
        
        agg_result = mongo_tool.aggregate(collection_name, pipeline)
        print(f"Aggregation result: {agg_result.count} results")
        
        # 获取统计信息
        stats = mongo_tool.get_statistics()
        print(f"Collections: {stats.get('collection_count', 0)}")
        
    finally:
        mongo_tool.disconnect()


def example_database_manager():
    """数据库管理器使用示例"""
    print("\n=== 数据库管理器使用示例 ===")
    
    # 获取全局管理器
    manager = get_database_manager()
    
    # 添加多个数据库连接
    databases = [
        {
            "name": "graph_db",
            "type": "neo4j",
            "config": {
                "uri": "bolt://localhost:7687",
                "username": "neo4j",
                "password": "password"
            }
        },
        {
            "name": "timeseries_db",
            "type": "influxdb",
            "config": {
                "url": "http://localhost:8086",
                "token": "token",
                "org": "org",
                "bucket": "bucket"
            }
        },
        {
            "name": "vector_db",
            "type": "milvus",
            "config": {
                "host": "localhost",
                "port": 19530
            }
        }
    ]
    
    # 添加数据库连接
    for db_config in databases:
        success = manager.add_database(
            name=db_config["name"],
            db_type=db_config["type"],
            config=db_config["config"]
        )
        print(f"Added {db_config['name']}: {success}")
    
    # 列出所有数据库
    print(f"Databases: {manager.list_databases()}")
    
    # 连接所有数据库
    connect_results = manager.connect_all()
    print(f"Connect results: {connect_results}")
    
    # 健康检查
    health = manager.health_check()
    print(f"Health status: {health['overall_health']}")
    print(f"Healthy connections: {health['healthy_connections']}/{health['total_connections']}")
    
    # 获取统计信息
    stats = manager.get_statistics()
    print(f"Total connections: {stats['total_connections']}")
    print(f"Connection types: {stats['connection_types']}")
    
    # 断开所有连接
    disconnect_results = manager.disconnect_all()
    print(f"Disconnect results: {disconnect_results}")


def main():
    """主函数"""
    print("数据库工具使用示例")
    print("=" * 50)
    
    # 显示支持的数据库类型
    supported_types = DatabaseFactory.get_supported_types()
    print(f"支持的数据库类型: {supported_types}")
    
    # 运行各种示例（注意：需要相应的数据库服务运行）
    try:
        # example_neo4j_operations()
        # example_influxdb_operations()
        # example_milvus_operations()
        # example_postgresql_operations()
        # example_mongodb_operations()
        example_database_manager()
        
    except Exception as e:
        print(f"示例运行出错: {e}")
    
    print("\n示例运行完成")


if __name__ == "__main__":
    main()
