"""
数据库初始化脚本
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from config import settings
from loguru import logger


def create_directories():
    """创建必要的目录"""
    directories = [
        "logs",
        "data",
        "data/knowledge",
        "data/graphs",
        "data/exports"
    ]
    
    for directory in directories:
        dir_path = project_root / directory
        dir_path.mkdir(parents=True, exist_ok=True)
        logger.info(f"Created directory: {dir_path}")


def init_sqlite_database():
    """初始化SQLite数据库"""
    try:
        import sqlite3
        
        # 从DATABASE_URL中提取数据库文件路径
        db_url = settings.database_url
        if db_url.startswith("sqlite:///"):
            db_path = db_url.replace("sqlite:///", "")
            
            # 如果是相对路径，转换为绝对路径
            if not os.path.isabs(db_path):
                db_path = project_root / db_path
            
            # 创建数据库目录
            os.makedirs(os.path.dirname(db_path), exist_ok=True)
            
            # 创建数据库连接
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 创建基础表结构
            create_tables_sql = """
            -- 概念表
            CREATE TABLE IF NOT EXISTS concepts (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                description TEXT,
                concept_type TEXT NOT NULL,
                abstraction_level INTEGER DEFAULT 0,
                domain TEXT,
                definition TEXT,
                properties TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            -- 实体表
            CREATE TABLE IF NOT EXISTS entities (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                description TEXT,
                entity_type TEXT NOT NULL,
                concept_id TEXT,
                location TEXT,
                status TEXT DEFAULT 'active',
                attributes TEXT,
                properties TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            -- 关系表
            CREATE TABLE IF NOT EXISTS relationships (
                id TEXT PRIMARY KEY,
                relationship_type TEXT NOT NULL,
                source_id TEXT NOT NULL,
                target_id TEXT NOT NULL,
                strength REAL DEFAULT 1.0,
                confidence REAL DEFAULT 1.0,
                weight REAL DEFAULT 1.0,
                description TEXT,
                properties TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            -- 知识项表
            CREATE TABLE IF NOT EXISTS knowledge_items (
                id TEXT PRIMARY KEY,
                title TEXT NOT NULL,
                content TEXT NOT NULL,
                knowledge_type TEXT NOT NULL,
                description TEXT,
                category TEXT,
                domain TEXT,
                keywords TEXT,
                tags TEXT,
                confidence REAL DEFAULT 1.0,
                reliability REAL DEFAULT 1.0,
                importance REAL DEFAULT 0.5,
                source TEXT,
                author TEXT,
                access_count INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            -- 规则表
            CREATE TABLE IF NOT EXISTS rules (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                description TEXT,
                rule_type TEXT NOT NULL,
                conditions TEXT,
                actions TEXT,
                priority INTEGER DEFAULT 0,
                is_active BOOLEAN DEFAULT TRUE,
                execution_count INTEGER DEFAULT 0,
                category TEXT,
                tags TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            -- 技能表
            CREATE TABLE IF NOT EXISTS skills (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                description TEXT NOT NULL,
                skill_type TEXT NOT NULL,
                complexity TEXT DEFAULT 'basic',
                steps TEXT,
                category TEXT,
                domain TEXT,
                tags TEXT,
                usage_count INTEGER DEFAULT 0,
                success_count INTEGER DEFAULT 0,
                success_rate REAL DEFAULT 1.0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            -- 创建索引
            CREATE INDEX IF NOT EXISTS idx_concepts_type ON concepts(concept_type);
            CREATE INDEX IF NOT EXISTS idx_concepts_domain ON concepts(domain);
            CREATE INDEX IF NOT EXISTS idx_entities_type ON entities(entity_type);
            CREATE INDEX IF NOT EXISTS idx_entities_concept ON entities(concept_id);
            CREATE INDEX IF NOT EXISTS idx_relationships_source ON relationships(source_id);
            CREATE INDEX IF NOT EXISTS idx_relationships_target ON relationships(target_id);
            CREATE INDEX IF NOT EXISTS idx_relationships_type ON relationships(relationship_type);
            CREATE INDEX IF NOT EXISTS idx_knowledge_type ON knowledge_items(knowledge_type);
            CREATE INDEX IF NOT EXISTS idx_knowledge_category ON knowledge_items(category);
            CREATE INDEX IF NOT EXISTS idx_rules_type ON rules(rule_type);
            CREATE INDEX IF NOT EXISTS idx_skills_type ON skills(skill_type);
            """
            
            # 执行SQL语句
            cursor.executescript(create_tables_sql)
            conn.commit()
            conn.close()
            
            logger.info(f"SQLite database initialized: {db_path}")
            
    except Exception as e:
        logger.error(f"Failed to initialize SQLite database: {e}")
        raise


def init_neo4j_database():
    """初始化Neo4j数据库"""
    try:
        from neo4j import GraphDatabase
        
        # 创建Neo4j连接
        driver = GraphDatabase.driver(
            settings.neo4j_uri,
            auth=(settings.neo4j_user, settings.neo4j_password)
        )
        
        with driver.session() as session:
            # 创建约束和索引
            constraints_and_indices = [
                # 唯一性约束
                "CREATE CONSTRAINT concept_id_unique IF NOT EXISTS FOR (c:Concept) REQUIRE c.id IS UNIQUE",
                "CREATE CONSTRAINT entity_id_unique IF NOT EXISTS FOR (e:Entity) REQUIRE e.id IS UNIQUE",
                "CREATE CONSTRAINT knowledge_id_unique IF NOT EXISTS FOR (k:Knowledge) REQUIRE k.id IS UNIQUE",
                "CREATE CONSTRAINT rule_id_unique IF NOT EXISTS FOR (r:Rule) REQUIRE r.id IS UNIQUE",
                "CREATE CONSTRAINT skill_id_unique IF NOT EXISTS FOR (s:Skill) REQUIRE s.id IS UNIQUE",
                
                # 索引
                "CREATE INDEX concept_type_index IF NOT EXISTS FOR (c:Concept) ON (c.concept_type)",
                "CREATE INDEX concept_domain_index IF NOT EXISTS FOR (c:Concept) ON (c.domain)",
                "CREATE INDEX entity_type_index IF NOT EXISTS FOR (e:Entity) ON (e.entity_type)",
                "CREATE INDEX entity_status_index IF NOT EXISTS FOR (e:Entity) ON (e.status)",
                "CREATE INDEX knowledge_type_index IF NOT EXISTS FOR (k:Knowledge) ON (k.knowledge_type)",
                "CREATE INDEX knowledge_category_index IF NOT EXISTS FOR (k:Knowledge) ON (k.category)",
                "CREATE INDEX rule_type_index IF NOT EXISTS FOR (r:Rule) ON (r.rule_type)",
                "CREATE INDEX skill_type_index IF NOT EXISTS FOR (s:Skill) ON (s.skill_type)"
            ]
            
            for statement in constraints_and_indices:
                try:
                    session.run(statement)
                    logger.info(f"Executed: {statement}")
                except Exception as e:
                    logger.warning(f"Failed to execute {statement}: {e}")
        
        driver.close()
        logger.info("Neo4j database initialized successfully")
        
    except Exception as e:
        logger.warning(f"Failed to initialize Neo4j database: {e}")
        logger.info("Neo4j initialization skipped - make sure Neo4j is running if you plan to use graph features")


def create_sample_data():
    """创建示例数据"""
    try:
        # 这里可以添加创建示例数据的逻辑
        logger.info("Sample data creation completed")
        
    except Exception as e:
        logger.error(f"Failed to create sample data: {e}")


def main():
    """主函数"""
    logger.info("Starting database initialization...")
    
    try:
        # 创建目录
        create_directories()
        
        # 初始化SQLite数据库
        init_sqlite_database()
        
        # 初始化Neo4j数据库（可选）
        init_neo4j_database()
        
        # 创建示例数据
        create_sample_data()
        
        logger.info("Database initialization completed successfully!")
        
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
