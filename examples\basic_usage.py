"""
AGI Knowledge Graph System - 基本使用示例
"""
from datetime import datetime
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.entities.concept import Concept, ConceptType
from core.entities.entity import Entity, EntityType
from core.semantic.relationship import Relationship, RelationshipType
from core.semantic.semantic_network import SemanticNetwork
from core.knowledge.knowledge_base import KnowledgeBase, KnowledgeItem, KnowledgeType
from core.knowledge.rule_engine import RuleEngine, Rule, RuleType, Condition, Action, ConditionOperator
from core.knowledge.skill_library import SkillLibrary, Skill, SkillType, SkillStep


def create_sample_concepts():
    """创建示例概念"""
    print("=== 创建示例概念 ===")
    
    # 创建抽象概念
    animal_concept = Concept(
        name="动物",
        description="生物界中的动物类别",
        concept_type=ConceptType.ABSTRACT,
        abstraction_level=2,
        domain="生物学",
        definition="具有感觉和运动能力的多细胞生物"
    )
    
    # 创建具体概念
    dog_concept = Concept(
        name="狗",
        description="犬科动物",
        concept_type=ConceptType.CONCRETE,
        abstraction_level=1,
        domain="生物学",
        definition="家养的犬科哺乳动物"
    )
    
    # 建立层次关系
    dog_concept.add_parent_concept(animal_concept.id)
    animal_concept.add_child_concept(dog_concept.id)
    
    print(f"创建概念: {animal_concept.name} (ID: {animal_concept.id})")
    print(f"创建概念: {dog_concept.name} (ID: {dog_concept.id})")
    
    return [animal_concept, dog_concept]


def create_sample_entities():
    """创建示例实体"""
    print("\n=== 创建示例实体 ===")
    
    # 创建人物实体
    person_entity = Entity(
        name="张三",
        description="一个普通人",
        entity_type=EntityType.PERSON,
        location="北京",
        status="active"
    )
    person_entity.set_attribute("age", 30)
    person_entity.set_attribute("occupation", "工程师")
    
    # 创建宠物实体
    pet_entity = Entity(
        name="小白",
        description="张三的宠物狗",
        entity_type=EntityType.OBJECT,
        location="北京",
        status="active"
    )
    pet_entity.set_attribute("breed", "金毛")
    pet_entity.set_attribute("age", 3)
    
    print(f"创建实体: {person_entity.name} (ID: {person_entity.id})")
    print(f"创建实体: {pet_entity.name} (ID: {pet_entity.id})")
    
    return [person_entity, pet_entity]


def create_sample_relationships(entities):
    """创建示例关系"""
    print("\n=== 创建示例关系 ===")
    
    person_entity, pet_entity = entities
    
    # 创建拥有关系
    owns_relationship = Relationship(
        relationship_type=RelationshipType.OWNS,
        source_id=person_entity.id,
        target_id=pet_entity.id,
        strength=1.0,
        confidence=1.0,
        description="张三拥有小白"
    )
    
    print(f"创建关系: {person_entity.name} 拥有 {pet_entity.name}")
    
    return [owns_relationship]


def create_sample_knowledge():
    """创建示例知识"""
    print("\n=== 创建示例知识 ===")
    
    # 创建事实性知识
    factual_knowledge = KnowledgeItem(
        title="狗的基本信息",
        content="狗是人类最早驯化的动物之一，通常寿命为10-15年",
        knowledge_type=KnowledgeType.FACTUAL,
        category="动物知识",
        domain="生物学",
        keywords=["狗", "宠物", "寿命"],
        confidence=0.9
    )
    
    # 创建程序性知识
    procedural_knowledge = KnowledgeItem(
        title="如何照顾宠物狗",
        content={
            "steps": [
                "每天喂食2-3次",
                "定期遛狗运动",
                "定期洗澡和梳毛",
                "定期体检和疫苗接种"
            ]
        },
        knowledge_type=KnowledgeType.PROCEDURAL,
        category="宠物护理",
        domain="生活常识",
        keywords=["宠物护理", "狗", "日常照顾"]
    )
    
    print(f"创建知识: {factual_knowledge.title}")
    print(f"创建知识: {procedural_knowledge.title}")
    
    return [factual_knowledge, procedural_knowledge]


def create_sample_rules():
    """创建示例规则"""
    print("\n=== 创建示例规则 ===")
    
    # 创建推理规则
    inference_rule = Rule(
        name="宠物护理提醒",
        description="当有人拥有宠物时，提醒护理要点",
        rule_type=RuleType.INFERENCE,
        conditions=[
            Condition(
                field="relationship_type",
                operator=ConditionOperator.EQUALS,
                value="owns"
            ),
            Condition(
                field="target_entity_type",
                operator=ConditionOperator.EQUALS,
                value="pet"
            )
        ],
        actions=[
            Action(
                type="generate_reminder",
                parameters={
                    "message": "记得定期照顾您的宠物",
                    "category": "pet_care"
                }
            )
        ],
        priority=1
    )
    
    print(f"创建规则: {inference_rule.name}")
    
    return [inference_rule]


def create_sample_skills():
    """创建示例技能"""
    print("\n=== 创建示例技能 ===")
    
    # 创建宠物护理技能
    pet_care_skill = Skill(
        name="宠物日常护理",
        description="执行宠物的日常护理任务",
        skill_type=SkillType.EXECUTION,
        category="宠物护理",
        domain="生活技能"
    )
    
    # 添加技能步骤
    step1 = SkillStep(
        name="检查宠物状态",
        description="检查宠物的健康状态和情绪",
        order=1,
        inputs={"pet_id": "${pet_id}"},
        outputs={"health_status": "good", "mood": "happy"}
    )
    
    step2 = SkillStep(
        name="准备食物",
        description="根据宠物类型和年龄准备适当的食物",
        order=2,
        inputs={"pet_type": "${pet_type}", "pet_age": "${pet_age}"},
        outputs={"food_prepared": True}
    )
    
    step3 = SkillStep(
        name="喂食",
        description="给宠物喂食",
        order=3,
        preconditions=["context['food_prepared'] == True"],
        outputs={"feeding_completed": True}
    )
    
    pet_care_skill.add_step(step1)
    pet_care_skill.add_step(step2)
    pet_care_skill.add_step(step3)
    
    print(f"创建技能: {pet_care_skill.name} (包含 {len(pet_care_skill.steps)} 个步骤)")
    
    return [pet_care_skill]


def demonstrate_semantic_network():
    """演示语义网络功能"""
    print("\n=== 演示语义网络功能 ===")
    
    # 创建语义网络
    semantic_network = SemanticNetwork()
    
    # 添加示例数据
    concepts = create_sample_concepts()
    entities = create_sample_entities()
    relationships = create_sample_relationships(entities)
    
    # 添加到语义网络
    for concept in concepts:
        semantic_network.add_concept(concept)
    
    for entity in entities:
        semantic_network.add_entity(entity)
    
    for relationship in relationships:
        semantic_network.add_relationship(relationship)
    
    # 演示功能
    print(f"\n语义网络摘要:")
    summary = semantic_network.get_knowledge_summary()
    for key, value in summary.items():
        print(f"  {key}: {value}")
    
    # 查找相关节点
    person_entity = entities[0]
    related_nodes = semantic_network.find_related_nodes(person_entity.id)
    print(f"\n与 {person_entity.name} 相关的节点:")
    for node_id, similarity in related_nodes:
        node = semantic_network.get_node(node_id)
        if node:
            print(f"  {node.name} (相似度: {similarity:.2f})")


def demonstrate_knowledge_base():
    """演示知识库功能"""
    print("\n=== 演示知识库功能 ===")
    
    # 创建知识库
    knowledge_base = KnowledgeBase()
    
    # 添加示例知识
    knowledge_items = create_sample_knowledge()
    for item in knowledge_items:
        knowledge_base.add_knowledge(item)
    
    # 演示搜索功能
    print(f"\n知识库统计:")
    stats = knowledge_base.get_knowledge_statistics()
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    # 搜索知识
    search_results = knowledge_base.search_by_keyword("狗")
    print(f"\n搜索关键词 '狗' 的结果:")
    for item in search_results:
        print(f"  {item.title}")


def demonstrate_rule_engine():
    """演示规则引擎功能"""
    print("\n=== 演示规则引擎功能 ===")
    
    # 创建规则引擎
    rule_engine = RuleEngine()
    
    # 添加示例规则
    rules = create_sample_rules()
    for rule in rules:
        rule_engine.add_rule(rule)
    
    # 执行规则
    context = {
        "relationship_type": "owns",
        "target_entity_type": "pet",
        "owner_name": "张三",
        "pet_name": "小白"
    }
    
    results = rule_engine.execute_rules(context)
    print(f"\n规则执行结果:")
    for result in results:
        print(f"  {result}")
    
    # 显示统计信息
    stats = rule_engine.get_execution_statistics()
    print(f"\n规则引擎统计:")
    for key, value in stats.items():
        print(f"  {key}: {value}")


def demonstrate_skill_library():
    """演示技能库功能"""
    print("\n=== 演示技能库功能 ===")
    
    # 创建技能库
    skill_library = SkillLibrary()
    
    # 添加示例技能
    skills = create_sample_skills()
    for skill in skills:
        skill_library.add_skill(skill)
    
    # 执行技能
    context = {
        "pet_id": "pet_001",
        "pet_type": "dog",
        "pet_age": 3
    }
    
    skill = skills[0]
    result = skill.execute(context)
    print(f"\n技能执行结果:")
    print(f"  技能: {result['skill_name']}")
    print(f"  成功: {result['success']}")
    print(f"  执行步骤数: {len(result['steps_executed'])}")
    
    # 显示统计信息
    stats = skill_library.get_library_statistics()
    print(f"\n技能库统计:")
    for key, value in stats.items():
        print(f"  {key}: {value}")


def main():
    """主函数"""
    print("AGI Knowledge Graph System - 基本使用示例")
    print("=" * 50)
    
    try:
        # 演示各个模块
        demonstrate_semantic_network()
        demonstrate_knowledge_base()
        demonstrate_rule_engine()
        demonstrate_skill_library()
        
        print("\n" + "=" * 50)
        print("示例演示完成！")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
