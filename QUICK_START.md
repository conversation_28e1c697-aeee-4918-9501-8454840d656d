# AGI Knowledge Graph System - 快速开始指南

## 🚀 5分钟快速体验

### 方法一：零依赖演示（推荐新手）

```bash
# 1. 克隆或下载项目
git clone <repository-url>
cd Attack-on-Titan

# 2. 直接运行核心功能演示（无需安装任何依赖）
python simple_demo.py
```

这将展示系统的核心功能，包括：
- ✅ 概念和实体创建
- ✅ 语义关系建立
- ✅ 知识库管理
- ✅ 查询和搜索功能

### 方法二：完整功能体验

```bash
# 1. 安装Python依赖
pip install pydantic fastapi uvicorn loguru typer rich

# 2. 运行完整演示
python demo.py

# 3. 启动API服务器
python main.py
```

### 方法三：使用快速启动脚本

```bash
# 运行交互式启动脚本
python run.py
```

脚本会自动：
- 检查Python版本
- 检查和安装依赖
- 初始化系统
- 提供操作选项

## 📋 系统要求

- **Python**: 3.8 或更高版本
- **操作系统**: Windows, macOS, Linux
- **内存**: 最少 512MB
- **磁盘空间**: 最少 100MB

## 🛠️ 安装选项

### 选项1：最小安装（仅核心功能）
```bash
# 无需安装任何依赖，直接运行
python simple_demo.py
```

### 选项2：基础安装
```bash
# 安装基础依赖
pip install pydantic fastapi uvicorn loguru

# 运行演示
python demo.py
```

### 选项3：完整安装
```bash
# 安装所有依赖
pip install -r requirements.txt

# 初始化系统
python scripts/init_db.py

# 运行完整示例
python examples/basic_usage.py
```

### 选项4：开发环境安装
```bash
# 安装开发依赖
pip install -r requirements.txt
pip install pytest black isort flake8 mypy

# 运行测试
pytest tests/

# 代码格式化
black .
isort .
```

## 🎯 主要功能演示

### 1. 创建知识图谱
```python
from core.entities.concept import Concept, ConceptType
from core.entities.entity import Entity, EntityType
from core.semantic.relationship import Relationship, RelationshipType

# 创建概念
animal = Concept(name="动物", concept_type=ConceptType.ABSTRACT)
dog = Concept(name="狗", concept_type=ConceptType.CONCRETE)

# 创建实体
person = Entity(name="张三", entity_type=EntityType.PERSON)
pet = Entity(name="小白", entity_type=EntityType.OBJECT)

# 创建关系
owns = Relationship(
    relationship_type=RelationshipType.OWNS,
    source_id=person.id,
    target_id=pet.id
)
```

### 2. 知识库操作
```python
from core.knowledge.knowledge_base import KnowledgeBase, KnowledgeItem, KnowledgeType

# 创建知识库
kb = KnowledgeBase()

# 添加知识
knowledge = KnowledgeItem(
    title="狗的基本信息",
    content="狗是人类最早驯化的动物之一",
    knowledge_type=KnowledgeType.FACTUAL
)
kb.add_knowledge(knowledge)

# 搜索知识
results = kb.search_by_keyword("狗")
```

### 3. 规则引擎
```python
from core.knowledge.rule_engine import RuleEngine, Rule, Condition, Action

# 创建规则引擎
engine = RuleEngine()

# 创建规则
rule = Rule(
    name="宠物护理提醒",
    conditions=[...],
    actions=[...]
)
engine.add_rule(rule)

# 执行规则
results = engine.execute_rules(context_data)
```

## 🌐 API使用

### 启动API服务器
```bash
# 方法1：直接启动
python main.py

# 方法2：使用CLI工具
python cli.py serve

# 方法3：使用uvicorn
uvicorn main:app --reload
```

### API接口示例
```bash
# 健康检查
curl http://localhost:8000/health

# 系统统计
curl http://localhost:8000/stats

# 处理查询
curl -X POST http://localhost:8000/query \
  -H "Content-Type: application/json" \
  -d '{"question": "什么是人工智能？"}'

# 查看API文档
# 浏览器访问: http://localhost:8000/docs
```

## 🔧 CLI工具使用

```bash
# 查看所有命令
python cli.py --help

# 系统初始化
python cli.py init

# 创建示例数据
python cli.py create-sample

# 查看系统状态
python cli.py status

# 查看系统信息
python cli.py info

# 运行演示
python cli.py demo

# 启动服务器
python cli.py serve

# 运行测试
python cli.py test
```

## 📁 项目结构说明

```
AGI-Knowledge-Graph/
├── core/                    # 核心模块
│   ├── entities/           # 概念和实体层
│   ├── semantic/           # 语义关系层
│   ├── knowledge/          # 知识和技能模块
│   └── tasks/             # 任务管理
├── examples/              # 示例代码
├── scripts/               # 工具脚本
├── tests/                 # 测试文件
├── main.py               # API服务器
├── cli.py                # CLI工具
├── demo.py               # 完整演示
├── simple_demo.py        # 简化演示
└── run.py                # 快速启动
```

## 🐛 常见问题

### Q: 运行时提示缺少模块？
A: 安装依赖：`pip install -r requirements.txt`

### Q: 如何查看详细日志？
A: 设置环境变量：`LOG_LEVEL=DEBUG`

### Q: 如何重置系统？
A: 删除数据库文件：`rm agi_system.db`，然后重新初始化

### Q: 如何添加自定义知识？
A: 参考 `examples/basic_usage.py` 中的示例代码

### Q: 如何扩展新的关系类型？
A: 在 `core/semantic/relationship.py` 中的 `RelationshipType` 枚举中添加

## 📚 学习资源

1. **核心概念**: 阅读 `PROJECT_SUMMARY.md`
2. **API文档**: 启动服务器后访问 `/docs`
3. **示例代码**: 查看 `examples/` 目录
4. **测试用例**: 查看 `tests/` 目录了解用法

## 🤝 获取帮助

- **问题反馈**: 创建 GitHub Issue
- **功能建议**: 创建 Feature Request
- **代码贡献**: 提交 Pull Request

## 🎉 下一步

体验完基本功能后，你可以：

1. **深入学习**: 阅读详细文档和源代码
2. **自定义扩展**: 添加自己的概念、实体和关系
3. **集成应用**: 将系统集成到你的项目中
4. **贡献代码**: 参与项目开发和改进

---

**开始你的AGI知识图谱之旅吧！** 🚀
