"""
AGI数据存储组件使用示例
演示如何使用各种数据存储组件进行知识管理、日志记录、模型存储和元数据管理
"""

import sys
import os
import json
import numpy as np
from datetime import datetime
from typing import Dict, List, Any

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data_storage_manager import DataStorageManager, initialize_storage_manager, get_storage_manager


def example_knowledge_graph_operations():
    """知识图谱操作示例"""
    print("\n=== 知识图谱操作示例 ===")
    
    manager = get_storage_manager()
    if not manager or not manager.is_service_available("knowledge_graph"):
        print("知识图谱服务不可用")
        return
    
    # 添加概念和关系
    knowledge_data = {
        "concepts": [
            {
                "concept_id": "concept_ai_001",
                "concept_name": "人工智能",
                "concept_type": "abstract",
                "description": "模拟人类智能的计算机系统",
                "domain": "computer_science",
                "abstraction_level": 3
            },
            {
                "concept_id": "concept_ml_001", 
                "concept_name": "机器学习",
                "concept_type": "concrete",
                "description": "让计算机从数据中学习的方法",
                "domain": "computer_science",
                "abstraction_level": 2
            }
        ],
        "relationships": [
            {
                "start_id": "concept_ml_001",
                "end_id": "concept_ai_001",
                "type": "is_a",
                "properties": {
                    "strength": 0.9,
                    "confidence": 0.95
                }
            }
        ]
    }
    
    # 添加知识
    success = manager.add_knowledge(knowledge_data)
    print(f"添加知识结果: {success}")
    
    # 语义搜索
    search_query = {
        "type": "semantic_search",
        "text": "人工智能",
        "limit": 5
    }
    
    results = manager.query_knowledge(search_query)
    print(f"搜索结果: {len(results)} 个节点")
    for result in results[:2]:  # 显示前2个结果
        print(f"  - {result.get('properties', {}).get('concept_name', 'Unknown')}")
    
    # 查找相关概念
    related_query = {
        "type": "related_concepts",
        "concept_id": "concept_ai_001",
        "max_depth": 2
    }
    
    related = manager.query_knowledge(related_query)
    print(f"相关概念: {len(related)} 个")


def example_execution_log_operations():
    """执行日志操作示例"""
    print("\n=== 执行日志操作示例 ===")
    
    manager = get_storage_manager()
    if not manager or not manager.is_service_available("execution_log"):
        print("执行日志服务不可用")
        return
    
    # 记录执行事件
    event_data = {
        "timestamp": datetime.now().isoformat(),
        "event_type": "model_training",
        "component": "neural_network",
        "operation": "forward_pass",
        "status": "success",
        "duration_ms": 150.5,
        "input_size": 1000,
        "output_size": 10,
        "metadata": {
            "model_id": "model_001",
            "batch_size": 32,
            "learning_rate": 0.001
        }
    }
    
    manager.log_event(event_data)
    print("记录执行事件完成")
    
    # 记录性能指标
    metrics_data = {
        "timestamp": datetime.now().isoformat(),
        "component": "neural_network",
        "cpu_usage": 75.5,
        "memory_usage": 60.2,
        "disk_io": 10.5,
        "network_io": 5.2,
        "response_time": 150.5,
        "throughput": 100.0,
        "error_rate": 0.01
    }
    
    manager.log_metrics(metrics_data)
    print("记录性能指标完成")
    
    # 记录学习进度
    learning_data = {
        "timestamp": datetime.now().isoformat(),
        "model_id": "model_001",
        "epoch": 10,
        "loss": 0.25,
        "accuracy": 0.85,
        "validation_loss": 0.30,
        "validation_accuracy": 0.82,
        "learning_rate": 0.001
    }
    
    manager.log_learning(learning_data)
    print("记录学习进度完成")
    
    # 查询日志
    query = {
        "type": "execution_events",
        "start_time": (datetime.now().replace(hour=0, minute=0, second=0)).isoformat(),
        "end_time": datetime.now().isoformat(),
        "component": "neural_network"
    }
    
    logs = manager.query_logs(query)
    print(f"查询到 {len(logs)} 条日志记录")


def example_model_parameter_operations():
    """模型参数操作示例"""
    print("\n=== 模型参数操作示例 ===")
    
    manager = get_storage_manager()
    if not manager or not manager.is_service_available("model_parameters"):
        print("模型参数服务不可用")
        return
    
    # 生成示例向量
    vector_dim = 512
    model_vector = np.random.random(vector_dim).tolist()
    
    # 存储模型参数
    model_data = {
        "model_id": "model_001",
        "model_type": "neural_network",
        "vector": model_vector,
        "parameters": {
            "layers": 3,
            "neurons_per_layer": [128, 64, 10],
            "activation": "relu",
            "optimizer": "adam",
            "learning_rate": 0.001,
            "batch_size": 32
        },
        "metadata": {
            "accuracy": 0.85,
            "training_time": 3600,
            "dataset": "mnist",
            "created_by": "user_001"
        }
    }
    
    success = manager.store_model(model_data)
    print(f"存储模型参数结果: {success}")
    
    # 查找相似模型
    query_vector = np.random.random(vector_dim).tolist()
    similar_models = manager.find_similar_models(
        query_vector=query_vector,
        top_k=5,
        filters={"model_type": "neural_network"}
    )
    
    print(f"找到 {len(similar_models)} 个相似模型")
    for model in similar_models[:2]:  # 显示前2个结果
        print(f"  - 模型ID: {model['model_id']}, 相似度: {model['similarity_score']:.3f}")
    
    # 获取特定模型
    model = manager.get_model("model_001")
    if model:
        print(f"获取模型成功: {model['model_id']}")
    else:
        print("模型不存在")


def example_system_metadata_operations():
    """系统元数据操作示例"""
    print("\n=== 系统元数据操作示例 ===")
    
    manager = get_storage_manager()
    if not manager or not manager.is_service_available("system_metadata"):
        print("系统元数据服务不可用")
        return
    
    # 存储元数据
    metadata = {
        "metadata_type": "concept",
        "entity_id": "concept_ai_001",
        "schema_version": "1.0.0",
        "content": {
            "concept_name": "人工智能",
            "concept_type": "abstract",
            "description": "模拟人类智能的计算机系统",
            "domain": "computer_science",
            "properties": {
                "complexity": "high",
                "maturity": "developing"
            }
        },
        "created_by": "user_001",
        "version": "1.0.0",
        "status": "active"
    }
    
    success = manager.store_metadata(metadata)
    print(f"存储元数据结果: {success}")
    
    # 查询元数据
    query = {
        "filters": {
            "metadata_type": "concept",
            "status": "active"
        },
        "limit": 10
    }
    
    results = manager.query_metadata(query)
    print(f"查询到 {len(results)} 条元数据记录")
    
    # 获取版本历史
    if results:
        entity_id = results[0]["entity_id"]
        history = manager.get_version_history(entity_id)
        print(f"版本历史: {len(history)} 条记录")


def example_system_statistics():
    """系统统计示例"""
    print("\n=== 系统统计信息 ===")
    
    manager = get_storage_manager()
    if not manager:
        print("数据存储管理器不可用")
        return
    
    # 获取系统统计
    stats = manager.get_system_statistics()
    print("系统统计信息:")
    print(f"  可用服务: {stats['services']['available']}")
    print(f"  初始化状态: {stats['services']['initialized']}")
    
    # 健康检查
    health = manager.health_check()
    print(f"\n系统健康状态: {health['overall_status']}")
    for service, status in health['services'].items():
        print(f"  {service}: {status}")


def main():
    """主函数"""
    print("AGI数据存储组件使用示例")
    print("=" * 50)
    
    # 配置文件路径
    config_file = os.path.join(os.path.dirname(__file__), "..", "config", "storage_config.json")
    
    # 如果配置文件不存在，使用默认配置
    if not os.path.exists(config_file):
        print("配置文件不存在，使用默认配置")
        config_dict = {
            "knowledge_graph": {
                "enabled": False,  # 默认禁用，避免连接错误
                "neo4j": {
                    "uri": "bolt://localhost:7687",
                    "username": "neo4j",
                    "password": "password"
                }
            },
            "execution_log": {
                "enabled": False,  # 默认禁用
                "influxdb": {
                    "url": "http://localhost:8086",
                    "token": "token",
                    "org": "org",
                    "bucket": "bucket"
                }
            },
            "model_parameters": {
                "enabled": False,  # 默认禁用
                "milvus": {
                    "host": "localhost",
                    "port": "19530"
                }
            },
            "system_metadata": {
                "enabled": False,  # 默认禁用
                "postgresql": {
                    "host": "localhost",
                    "port": 5432,
                    "database": "agi",
                    "username": "user",
                    "password": "password"
                }
            }
        }
        
        # 初始化存储管理器
        success = initialize_storage_manager(config_dict=config_dict)
    else:
        # 初始化存储管理器
        success = initialize_storage_manager(config_file=config_file)
    
    if not success:
        print("初始化存储管理器失败")
        return
    
    print("存储管理器初始化成功")
    
    try:
        # 运行示例
        example_knowledge_graph_operations()
        example_execution_log_operations()
        example_model_parameter_operations()
        example_system_metadata_operations()
        example_system_statistics()
        
    except Exception as e:
        print(f"运行示例时出错: {e}")
    
    finally:
        # 关闭存储管理器
        manager = get_storage_manager()
        if manager:
            manager.shutdown()
        print("\n存储管理器已关闭")


if __name__ == "__main__":
    main()
