"""
数据库基础抽象类
定义通用的数据库操作接口，确保与业务逻辑解耦
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass
from enum import Enum
import logging
from datetime import datetime

logger = logging.getLogger(__name__)


class DatabaseType(Enum):
    """数据库类型枚举"""
    GRAPH = "graph"           # 图数据库
    TIMESERIES = "timeseries" # 时序数据库
    VECTOR = "vector"         # 向量数据库
    RELATIONAL = "relational" # 关系型数据库
    DOCUMENT = "document"     # 文档数据库


class ConnectionStatus(Enum):
    """连接状态枚举"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    ERROR = "error"


@dataclass
class DatabaseConfig:
    """数据库配置基类"""
    host: str
    port: int
    database: str
    username: Optional[str] = None
    password: Optional[str] = None
    timeout: int = 30
    pool_size: int = 10
    ssl_enabled: bool = False
    extra_params: Optional[Dict[str, Any]] = None


@dataclass
class QueryResult:
    """查询结果基类"""
    success: bool
    data: Any = None
    count: int = 0
    error: Optional[str] = None
    execution_time: float = 0.0
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class OperationResult:
    """操作结果基类"""
    success: bool
    affected_count: int = 0
    error: Optional[str] = None
    execution_time: float = 0.0
    result_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class DatabaseInterface(ABC):
    """数据库接口抽象基类"""
    
    def __init__(self, config: DatabaseConfig):
        """
        初始化数据库接口
        
        Args:
            config: 数据库配置
        """
        self.config = config
        self.connection = None
        self.status = ConnectionStatus.DISCONNECTED
        self.last_error = None
        self.connection_time = None
        
    @property
    @abstractmethod
    def database_type(self) -> DatabaseType:
        """返回数据库类型"""
        pass
    
    @abstractmethod
    def connect(self) -> bool:
        """
        建立数据库连接
        
        Returns:
            是否连接成功
        """
        pass
    
    @abstractmethod
    def disconnect(self) -> bool:
        """
        关闭数据库连接
        
        Returns:
            是否关闭成功
        """
        pass
    
    @abstractmethod
    def is_connected(self) -> bool:
        """
        检查连接状态
        
        Returns:
            是否已连接
        """
        pass
    
    @abstractmethod
    def ping(self) -> bool:
        """
        测试连接可用性
        
        Returns:
            连接是否可用
        """
        pass
    
    @abstractmethod
    def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> QueryResult:
        """
        执行查询
        
        Args:
            query: 查询语句
            params: 查询参数
            
        Returns:
            查询结果
        """
        pass
    
    @abstractmethod
    def execute_command(self, command: str, params: Optional[Dict[str, Any]] = None) -> OperationResult:
        """
        执行命令
        
        Args:
            command: 命令语句
            params: 命令参数
            
        Returns:
            操作结果
        """
        pass
    
    def get_connection_info(self) -> Dict[str, Any]:
        """获取连接信息"""
        return {
            "database_type": self.database_type.value,
            "host": self.config.host,
            "port": self.config.port,
            "database": self.config.database,
            "status": self.status.value,
            "connection_time": self.connection_time.isoformat() if self.connection_time else None,
            "last_error": self.last_error
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        return {
            "connection_info": self.get_connection_info(),
            "is_connected": self.is_connected(),
            "ping_result": self.ping() if self.is_connected() else False
        }


class GraphDatabaseInterface(DatabaseInterface):
    """图数据库接口"""
    
    @property
    def database_type(self) -> DatabaseType:
        return DatabaseType.GRAPH
    
    @abstractmethod
    def create_node(self, labels: List[str], properties: Dict[str, Any]) -> OperationResult:
        """创建节点"""
        pass
    
    @abstractmethod
    def create_relationship(self, start_node_id: str, end_node_id: str, 
                          relationship_type: str, properties: Dict[str, Any] = None) -> OperationResult:
        """创建关系"""
        pass
    
    @abstractmethod
    def find_nodes(self, labels: List[str] = None, properties: Dict[str, Any] = None,
                  limit: int = 100) -> QueryResult:
        """查找节点"""
        pass
    
    @abstractmethod
    def find_relationships(self, relationship_type: str = None, 
                         properties: Dict[str, Any] = None, limit: int = 100) -> QueryResult:
        """查找关系"""
        pass
    
    @abstractmethod
    def find_path(self, start_node_id: str, end_node_id: str, 
                 max_depth: int = 5) -> QueryResult:
        """查找路径"""
        pass


class TimeSeriesDatabaseInterface(DatabaseInterface):
    """时序数据库接口"""
    
    @property
    def database_type(self) -> DatabaseType:
        return DatabaseType.TIMESERIES
    
    @abstractmethod
    def write_point(self, measurement: str, tags: Dict[str, str], 
                   fields: Dict[str, Any], timestamp: datetime = None) -> OperationResult:
        """写入数据点"""
        pass
    
    @abstractmethod
    def write_points(self, points: List[Dict[str, Any]]) -> OperationResult:
        """批量写入数据点"""
        pass
    
    @abstractmethod
    def query_range(self, measurement: str, start_time: datetime, end_time: datetime,
                   tags: Dict[str, str] = None, fields: List[str] = None) -> QueryResult:
        """范围查询"""
        pass
    
    @abstractmethod
    def aggregate_query(self, measurement: str, start_time: datetime, end_time: datetime,
                       aggregation: str, interval: str, tags: Dict[str, str] = None) -> QueryResult:
        """聚合查询"""
        pass


class VectorDatabaseInterface(DatabaseInterface):
    """向量数据库接口"""
    
    @property
    def database_type(self) -> DatabaseType:
        return DatabaseType.VECTOR
    
    @abstractmethod
    def create_collection(self, collection_name: str, dimension: int, 
                         index_type: str = "IVF_FLAT", metric_type: str = "L2") -> OperationResult:
        """创建集合"""
        pass
    
    @abstractmethod
    def insert_vectors(self, collection_name: str, vectors: List[List[float]], 
                      ids: List[str] = None, metadata: List[Dict[str, Any]] = None) -> OperationResult:
        """插入向量"""
        pass
    
    @abstractmethod
    def search_vectors(self, collection_name: str, query_vectors: List[List[float]], 
                      top_k: int = 10, filters: Dict[str, Any] = None) -> QueryResult:
        """搜索向量"""
        pass
    
    @abstractmethod
    def delete_vectors(self, collection_name: str, ids: List[str]) -> OperationResult:
        """删除向量"""
        pass


class RelationalDatabaseInterface(DatabaseInterface):
    """关系型数据库接口"""
    
    @property
    def database_type(self) -> DatabaseType:
        return DatabaseType.RELATIONAL
    
    @abstractmethod
    def create_table(self, table_name: str, schema: Dict[str, str]) -> OperationResult:
        """创建表"""
        pass
    
    @abstractmethod
    def insert_record(self, table_name: str, data: Dict[str, Any]) -> OperationResult:
        """插入记录"""
        pass
    
    @abstractmethod
    def insert_records(self, table_name: str, data: List[Dict[str, Any]]) -> OperationResult:
        """批量插入记录"""
        pass
    
    @abstractmethod
    def update_record(self, table_name: str, data: Dict[str, Any], 
                     conditions: Dict[str, Any]) -> OperationResult:
        """更新记录"""
        pass
    
    @abstractmethod
    def delete_record(self, table_name: str, conditions: Dict[str, Any]) -> OperationResult:
        """删除记录"""
        pass
    
    @abstractmethod
    def select_records(self, table_name: str, columns: List[str] = None,
                      conditions: Dict[str, Any] = None, limit: int = 100, 
                      offset: int = 0, order_by: str = None) -> QueryResult:
        """查询记录"""
        pass
    
    @abstractmethod
    def execute_transaction(self, operations: List[Dict[str, Any]]) -> OperationResult:
        """执行事务"""
        pass


class DocumentDatabaseInterface(DatabaseInterface):
    """文档数据库接口"""
    
    @property
    def database_type(self) -> DatabaseType:
        return DatabaseType.DOCUMENT
    
    @abstractmethod
    def create_collection(self, collection_name: str, schema: Dict[str, Any] = None) -> OperationResult:
        """创建集合"""
        pass
    
    @abstractmethod
    def insert_document(self, collection_name: str, document: Dict[str, Any]) -> OperationResult:
        """插入文档"""
        pass
    
    @abstractmethod
    def insert_documents(self, collection_name: str, documents: List[Dict[str, Any]]) -> OperationResult:
        """批量插入文档"""
        pass
    
    @abstractmethod
    def find_documents(self, collection_name: str, query: Dict[str, Any] = None,
                      projection: Dict[str, int] = None, limit: int = 100, 
                      skip: int = 0, sort: Dict[str, int] = None) -> QueryResult:
        """查找文档"""
        pass
    
    @abstractmethod
    def update_document(self, collection_name: str, query: Dict[str, Any], 
                       update: Dict[str, Any], upsert: bool = False) -> OperationResult:
        """更新文档"""
        pass
    
    @abstractmethod
    def delete_document(self, collection_name: str, query: Dict[str, Any]) -> OperationResult:
        """删除文档"""
        pass
    
    @abstractmethod
    def aggregate(self, collection_name: str, pipeline: List[Dict[str, Any]]) -> QueryResult:
        """聚合查询"""
        pass


class DatabaseException(Exception):
    """数据库异常基类"""
    
    def __init__(self, message: str, error_code: str = None, original_error: Exception = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.original_error = original_error
        self.timestamp = datetime.now()


class ConnectionException(DatabaseException):
    """连接异常"""
    pass


class QueryException(DatabaseException):
    """查询异常"""
    pass


class OperationException(DatabaseException):
    """操作异常"""
    pass


class ValidationException(DatabaseException):
    """验证异常"""
    pass
