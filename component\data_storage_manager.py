"""
数据存储管理器 - 统一管理AGI系统的各种数据存储组件
提供统一的接口来管理知识图谱、执行日志、模型参数和系统元数据
"""

import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import json
from datetime import datetime
from .knowledge_graph import KnowledgeGraphService
from .execution_log import ExecutionLogService
from .model_parameters import ModelParameterService
from .system_metadata import SystemMetadataService

logger = logging.getLogger(__name__)


@dataclass
class StorageConfig:
    """存储配置数据结构"""
    storage_type: str
    config: Dict[str, Any]
    enabled: bool = True
    description: str = ""


class DataStorageManager:
    """数据存储管理器"""
    
    def __init__(self, config_file: str = None, config_dict: Dict[str, Any] = None):
        """
        初始化数据存储管理器
        
        Args:
            config_file: 配置文件路径
            config_dict: 配置字典
        """
        self.config = self._load_config(config_file, config_dict)
        self.services = {}
        self.initialized = False
        
    def _load_config(self, config_file: str = None, 
                    config_dict: Dict[str, Any] = None) -> Dict[str, Any]:
        """加载配置"""
        if config_dict:
            return config_dict
        elif config_file:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            # 默认配置
            return {
                "knowledge_graph": {
                    "enabled": True,
                    "neo4j": {
                        "uri": "bolt://localhost:7687",
                        "username": "neo4j",
                        "password": "password",
                        "database": "neo4j"
                    }
                },
                "execution_log": {
                    "enabled": True,
                    "influxdb": {
                        "url": "http://localhost:8086",
                        "token": "your-token",
                        "org": "your-org",
                        "bucket": "agi-logs"
                    }
                },
                "model_parameters": {
                    "enabled": True,
                    "milvus": {
                        "host": "localhost",
                        "port": "19530",
                        "alias": "default",
                        "default_collection": "model_parameters",
                        "vector_dim": 512,
                        "index_type": "IVF_FLAT",
                        "metric_type": "L2"
                    }
                },
                "system_metadata": {
                    "enabled": True,
                    "postgresql": {
                        "host": "localhost",
                        "port": 5432,
                        "database": "agi_metadata",
                        "username": "postgres",
                        "password": "password",
                        "min_conn": 1,
                        "max_conn": 20
                    }
                }
            }
    
    def initialize(self) -> bool:
        """初始化所有存储服务"""
        try:
            success_count = 0
            total_count = 0
            
            # 初始化知识图谱服务
            if self.config.get("knowledge_graph", {}).get("enabled", False):
                total_count += 1
                try:
                    kg_service = KnowledgeGraphService(self.config["knowledge_graph"])
                    if kg_service.initialize():
                        self.services["knowledge_graph"] = kg_service
                        success_count += 1
                        logger.info("Knowledge graph service initialized successfully")
                    else:
                        logger.error("Failed to initialize knowledge graph service")
                except Exception as e:
                    logger.error(f"Error initializing knowledge graph service: {e}")
            
            # 初始化执行日志服务
            if self.config.get("execution_log", {}).get("enabled", False):
                total_count += 1
                try:
                    log_service = ExecutionLogService(self.config["execution_log"])
                    if log_service.initialize():
                        self.services["execution_log"] = log_service
                        success_count += 1
                        logger.info("Execution log service initialized successfully")
                    else:
                        logger.error("Failed to initialize execution log service")
                except Exception as e:
                    logger.error(f"Error initializing execution log service: {e}")
            
            # 初始化模型参数服务
            if self.config.get("model_parameters", {}).get("enabled", False):
                total_count += 1
                try:
                    model_service = ModelParameterService(self.config["model_parameters"])
                    if model_service.initialize():
                        self.services["model_parameters"] = model_service
                        success_count += 1
                        logger.info("Model parameter service initialized successfully")
                    else:
                        logger.error("Failed to initialize model parameter service")
                except Exception as e:
                    logger.error(f"Error initializing model parameter service: {e}")
            
            # 初始化系统元数据服务
            if self.config.get("system_metadata", {}).get("enabled", False):
                total_count += 1
                try:
                    metadata_service = SystemMetadataService(self.config["system_metadata"])
                    if metadata_service.initialize():
                        self.services["system_metadata"] = metadata_service
                        success_count += 1
                        logger.info("System metadata service initialized successfully")
                    else:
                        logger.error("Failed to initialize system metadata service")
                except Exception as e:
                    logger.error(f"Error initializing system metadata service: {e}")
            
            self.initialized = success_count > 0
            logger.info(f"Initialized {success_count}/{total_count} storage services")
            return self.initialized
            
        except Exception as e:
            logger.error(f"Failed to initialize data storage manager: {e}")
            return False
    
    def shutdown(self):
        """关闭所有存储服务"""
        for service_name, service in self.services.items():
            try:
                service.shutdown()
                logger.info(f"Shutdown {service_name} service")
            except Exception as e:
                logger.error(f"Error shutting down {service_name} service: {e}")
        
        self.services.clear()
        self.initialized = False
        logger.info("Data storage manager shutdown completed")
    
    def get_service(self, service_name: str) -> Optional[Any]:
        """
        获取指定的存储服务
        
        Args:
            service_name: 服务名称 (knowledge_graph, execution_log, model_parameters, system_metadata)
            
        Returns:
            服务实例或None
        """
        return self.services.get(service_name)
    
    def is_service_available(self, service_name: str) -> bool:
        """检查服务是否可用"""
        return service_name in self.services
    
    def get_available_services(self) -> List[str]:
        """获取可用服务列表"""
        return list(self.services.keys())
    
    # 知识图谱相关方法
    def add_knowledge(self, knowledge_data: Dict[str, Any]) -> bool:
        """添加知识到知识图谱"""
        if "knowledge_graph" in self.services:
            return self.services["knowledge_graph"].add_knowledge(knowledge_data)
        logger.warning("Knowledge graph service not available")
        return False
    
    def query_knowledge(self, query: Dict[str, Any]) -> List[Dict[str, Any]]:
        """查询知识图谱"""
        if "knowledge_graph" in self.services:
            return self.services["knowledge_graph"].query_knowledge(query)
        logger.warning("Knowledge graph service not available")
        return []
    
    # 执行日志相关方法
    def log_event(self, event_data: Dict[str, Any]):
        """记录执行事件"""
        if "execution_log" in self.services:
            self.services["execution_log"].log_event(event_data)
        else:
            logger.warning("Execution log service not available")
    
    def log_metrics(self, metrics_data: Dict[str, Any]):
        """记录性能指标"""
        if "execution_log" in self.services:
            self.services["execution_log"].log_metrics(metrics_data)
        else:
            logger.warning("Execution log service not available")
    
    def log_learning(self, learning_data: Dict[str, Any]):
        """记录学习进度"""
        if "execution_log" in self.services:
            self.services["execution_log"].log_learning(learning_data)
        else:
            logger.warning("Execution log service not available")
    
    def query_logs(self, query: Dict[str, Any]) -> List[Dict[str, Any]]:
        """查询执行日志"""
        if "execution_log" in self.services:
            return self.services["execution_log"].query_logs(query)
        logger.warning("Execution log service not available")
        return []
    
    # 模型参数相关方法
    def store_model(self, model_data: Dict[str, Any]) -> bool:
        """存储模型参数"""
        if "model_parameters" in self.services:
            return self.services["model_parameters"].store_model(model_data)
        logger.warning("Model parameter service not available")
        return False
    
    def find_similar_models(self, query_vector: List[float], 
                          top_k: int = 10, filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """查找相似模型"""
        if "model_parameters" in self.services:
            return self.services["model_parameters"].find_similar_models(
                query_vector, top_k, filters
            )
        logger.warning("Model parameter service not available")
        return []
    
    def get_model(self, model_id: str) -> Optional[Dict[str, Any]]:
        """获取模型参数"""
        if "model_parameters" in self.services:
            return self.services["model_parameters"].get_model(model_id)
        logger.warning("Model parameter service not available")
        return None
    
    # 系统元数据相关方法
    def store_metadata(self, metadata: Dict[str, Any]) -> bool:
        """存储系统元数据"""
        if "system_metadata" in self.services:
            return self.services["system_metadata"].store_metadata(metadata)
        logger.warning("System metadata service not available")
        return False
    
    def get_metadata(self, record_id: str) -> Optional[Dict[str, Any]]:
        """获取系统元数据"""
        if "system_metadata" in self.services:
            return self.services["system_metadata"].get_metadata(record_id)
        logger.warning("System metadata service not available")
        return None
    
    def query_metadata(self, query: Dict[str, Any]) -> List[Dict[str, Any]]:
        """查询系统元数据"""
        if "system_metadata" in self.services:
            return self.services["system_metadata"].query_metadata(query)
        logger.warning("System metadata service not available")
        return []
    
    def get_version_history(self, entity_id: str) -> List[Dict[str, Any]]:
        """获取版本历史"""
        if "system_metadata" in self.services:
            return self.services["system_metadata"].get_version_history(entity_id)
        logger.warning("System metadata service not available")
        return []
    
    # 统计和监控方法
    def get_system_statistics(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        stats = {
            "services": {
                "available": self.get_available_services(),
                "total_configured": len(self.config),
                "initialized": self.initialized
            }
        }
        
        # 收集各服务的统计信息
        for service_name, service in self.services.items():
            try:
                if hasattr(service, 'get_statistics'):
                    stats[service_name] = service.get_statistics()
                else:
                    stats[service_name] = {"status": "available"}
            except Exception as e:
                logger.error(f"Error getting statistics from {service_name}: {e}")
                stats[service_name] = {"status": "error", "error": str(e)}
        
        return stats
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        health_status = {
            "overall_status": "healthy" if self.initialized else "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "services": {}
        }
        
        for service_name in self.get_available_services():
            try:
                # 简单的健康检查 - 尝试获取统计信息
                service = self.services[service_name]
                if hasattr(service, 'get_statistics'):
                    service.get_statistics()
                health_status["services"][service_name] = "healthy"
            except Exception as e:
                health_status["services"][service_name] = f"unhealthy: {str(e)}"
                health_status["overall_status"] = "degraded"
        
        return health_status
    
    def backup_configuration(self) -> Dict[str, Any]:
        """备份配置"""
        return {
            "timestamp": datetime.now().isoformat(),
            "config": self.config,
            "services": self.get_available_services()
        }
    
    def restore_configuration(self, backup_config: Dict[str, Any]) -> bool:
        """恢复配置"""
        try:
            # 关闭当前服务
            self.shutdown()
            
            # 恢复配置
            self.config = backup_config["config"]
            
            # 重新初始化
            return self.initialize()
            
        except Exception as e:
            logger.error(f"Failed to restore configuration: {e}")
            return False


# 全局数据存储管理器实例
_storage_manager: Optional[DataStorageManager] = None


def get_storage_manager() -> Optional[DataStorageManager]:
    """获取全局数据存储管理器实例"""
    return _storage_manager


def initialize_storage_manager(config_file: str = None, 
                             config_dict: Dict[str, Any] = None) -> bool:
    """
    初始化全局数据存储管理器
    
    Args:
        config_file: 配置文件路径
        config_dict: 配置字典
        
    Returns:
        是否初始化成功
    """
    global _storage_manager
    
    try:
        _storage_manager = DataStorageManager(config_file, config_dict)
        return _storage_manager.initialize()
    except Exception as e:
        logger.error(f"Failed to initialize storage manager: {e}")
        return False


def shutdown_storage_manager():
    """关闭全局数据存储管理器"""
    global _storage_manager
    
    if _storage_manager:
        _storage_manager.shutdown()
        _storage_manager = None


# 便捷函数
def add_knowledge(knowledge_data: Dict[str, Any]) -> bool:
    """添加知识（便捷函数）"""
    manager = get_storage_manager()
    return manager.add_knowledge(knowledge_data) if manager else False


def log_event(event_data: Dict[str, Any]):
    """记录事件（便捷函数）"""
    manager = get_storage_manager()
    if manager:
        manager.log_event(event_data)


def store_model(model_data: Dict[str, Any]) -> bool:
    """存储模型（便捷函数）"""
    manager = get_storage_manager()
    return manager.store_model(model_data) if manager else False


def store_metadata(metadata: Dict[str, Any]) -> bool:
    """存储元数据（便捷函数）"""
    manager = get_storage_manager()
    return manager.store_metadata(metadata) if manager else False
