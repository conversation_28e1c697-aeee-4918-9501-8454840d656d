"""
关系类定义 - 描述实体与实体之间的逻辑关系
"""
from typing import Dict, Any, List, Optional, Set, Tuple
from enum import Enum
from datetime import datetime
from pydantic import BaseModel, Field
from uuid import uuid4


class RelationshipType(str, Enum):
    """关系类型枚举"""
    # 层次关系
    IS_A = "is_a"                    # 是一个
    PART_OF = "part_of"              # 部分属于
    CONTAINS = "contains"            # 包含
    
    # 属性关系
    HAS_PROPERTY = "has_property"    # 具有属性
    HAS_VALUE = "has_value"          # 具有值
    
    # 因果关系
    CAUSES = "causes"                # 导致
    CAUSED_BY = "caused_by"          # 被导致
    ENABLES = "enables"              # 使能够
    PREVENTS = "prevents"            # 阻止
    
    # 时间关系
    BEFORE = "before"                # 在...之前
    AFTER = "after"                  # 在...之后
    DURING = "during"                # 在...期间
    SIMULTANEOUS = "simultaneous"    # 同时
    
    # 空间关系
    LOCATED_AT = "located_at"        # 位于
    NEAR = "near"                    # 靠近
    INSIDE = "inside"                # 在...内部
    OUTSIDE = "outside"              # 在...外部
    
    # 社会关系
    KNOWS = "knows"                  # 认识
    WORKS_FOR = "works_for"          # 为...工作
    OWNS = "owns"                    # 拥有
    USES = "uses"                    # 使用
    
    # 逻辑关系
    IMPLIES = "implies"              # 暗示
    CONTRADICTS = "contradicts"      # 矛盾
    SUPPORTS = "supports"            # 支持
    DEPENDS_ON = "depends_on"        # 依赖于
    
    # 相似关系
    SIMILAR_TO = "similar_to"        # 相似于
    DIFFERENT_FROM = "different_from" # 不同于
    EQUIVALENT_TO = "equivalent_to"   # 等价于
    
    # 自定义关系
    CUSTOM = "custom"                # 自定义


class Relationship(BaseModel):
    """关系类 - 表示两个节点之间的关系"""
    
    id: str = Field(default_factory=lambda: str(uuid4()))
    relationship_type: RelationshipType = Field(..., description="关系类型")
    source_id: str = Field(..., description="源节点ID")
    target_id: str = Field(..., description="目标节点ID")
    
    # 关系属性
    strength: float = Field(default=1.0, ge=0.0, le=1.0, description="关系强度")
    confidence: float = Field(default=1.0, ge=0.0, le=1.0, description="置信度")
    weight: float = Field(default=1.0, description="权重")
    
    # 元数据
    properties: Dict[str, Any] = Field(default_factory=dict, description="关系属性")
    description: Optional[str] = Field(None, description="关系描述")
    source: Optional[str] = Field(None, description="数据来源")
    
    # 时间信息
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    valid_from: Optional[datetime] = Field(None, description="有效开始时间")
    valid_to: Optional[datetime] = Field(None, description="有效结束时间")
    
    # 状态
    is_active: bool = Field(default=True, description="是否活跃")
    is_bidirectional: bool = Field(default=False, description="是否双向关系")
    
    class Config:
        arbitrary_types_allowed = True
    
    def is_valid_at(self, check_time: datetime) -> bool:
        """检查关系在指定时间是否有效"""
        if not self.is_active:
            return False
        
        if self.valid_from and check_time < self.valid_from:
            return False
        
        if self.valid_to and check_time > self.valid_to:
            return False
        
        return True
    
    def get_reverse_type(self) -> Optional[RelationshipType]:
        """获取反向关系类型"""
        reverse_mapping = {
            RelationshipType.IS_A: None,  # 没有直接反向
            RelationshipType.PART_OF: RelationshipType.CONTAINS,
            RelationshipType.CONTAINS: RelationshipType.PART_OF,
            RelationshipType.CAUSES: RelationshipType.CAUSED_BY,
            RelationshipType.CAUSED_BY: RelationshipType.CAUSES,
            RelationshipType.BEFORE: RelationshipType.AFTER,
            RelationshipType.AFTER: RelationshipType.BEFORE,
            RelationshipType.INSIDE: RelationshipType.OUTSIDE,
            RelationshipType.OUTSIDE: RelationshipType.INSIDE,
            RelationshipType.OWNS: None,  # 拥有关系通常不对称
            RelationshipType.USES: None,  # 使用关系通常不对称
        }
        
        return reverse_mapping.get(self.relationship_type)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "relationship_type": self.relationship_type.value,
            "source_id": self.source_id,
            "target_id": self.target_id,
            "strength": self.strength,
            "confidence": self.confidence,
            "weight": self.weight,
            "properties": self.properties,
            "description": self.description,
            "source": self.source,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "valid_from": self.valid_from.isoformat() if self.valid_from else None,
            "valid_to": self.valid_to.isoformat() if self.valid_to else None,
            "is_active": self.is_active,
            "is_bidirectional": self.is_bidirectional
        }


class RelationshipManager:
    """关系管理器"""
    
    def __init__(self):
        self.relationships: Dict[str, Relationship] = {}
        self.source_index: Dict[str, Set[str]] = {}  # source_id -> relationship_ids
        self.target_index: Dict[str, Set[str]] = {}  # target_id -> relationship_ids
        self.type_index: Dict[RelationshipType, Set[str]] = {}  # type -> relationship_ids
    
    def add_relationship(self, relationship: Relationship) -> None:
        """添加关系"""
        self.relationships[relationship.id] = relationship
        self._update_indices(relationship)
        
        # 如果是双向关系，自动创建反向关系
        if relationship.is_bidirectional:
            reverse_type = relationship.get_reverse_type()
            if reverse_type:
                reverse_rel = Relationship(
                    relationship_type=reverse_type,
                    source_id=relationship.target_id,
                    target_id=relationship.source_id,
                    strength=relationship.strength,
                    confidence=relationship.confidence,
                    weight=relationship.weight,
                    is_bidirectional=False  # 避免无限递归
                )
                self.add_relationship(reverse_rel)
    
    def get_relationship(self, relationship_id: str) -> Optional[Relationship]:
        """获取关系"""
        return self.relationships.get(relationship_id)
    
    def get_relationships_from_source(self, source_id: str, 
                                    relationship_type: Optional[RelationshipType] = None) -> List[Relationship]:
        """获取从指定源节点出发的关系"""
        relationship_ids = self.source_index.get(source_id, set())
        relationships = [self.relationships[rid] for rid in relationship_ids 
                        if rid in self.relationships]
        
        if relationship_type:
            relationships = [r for r in relationships 
                           if r.relationship_type == relationship_type]
        
        return relationships
    
    def get_relationships_to_target(self, target_id: str,
                                  relationship_type: Optional[RelationshipType] = None) -> List[Relationship]:
        """获取指向指定目标节点的关系"""
        relationship_ids = self.target_index.get(target_id, set())
        relationships = [self.relationships[rid] for rid in relationship_ids 
                        if rid in self.relationships]
        
        if relationship_type:
            relationships = [r for r in relationships 
                           if r.relationship_type == relationship_type]
        
        return relationships
    
    def get_all_relationships_for_node(self, node_id: str) -> List[Relationship]:
        """获取节点的所有关系（作为源或目标）"""
        outgoing = self.get_relationships_from_source(node_id)
        incoming = self.get_relationships_to_target(node_id)
        
        # 去重
        all_relationships = {}
        for rel in outgoing + incoming:
            all_relationships[rel.id] = rel
        
        return list(all_relationships.values())
    
    def find_path(self, source_id: str, target_id: str, 
                 max_depth: int = 3) -> List[List[Relationship]]:
        """查找两个节点之间的路径"""
        paths = []
        visited = set()
        
        def dfs(current_id: str, path: List[Relationship], depth: int):
            if depth > max_depth:
                return
            
            if current_id == target_id and path:
                paths.append(path.copy())
                return
            
            if current_id in visited:
                return
            
            visited.add(current_id)
            
            # 获取从当前节点出发的所有关系
            outgoing_rels = self.get_relationships_from_source(current_id)
            
            for rel in outgoing_rels:
                if rel.is_active and rel.target_id not in visited:
                    path.append(rel)
                    dfs(rel.target_id, path, depth + 1)
                    path.pop()
            
            visited.remove(current_id)
        
        dfs(source_id, [], 0)
        return paths
    
    def get_neighbors(self, node_id: str, max_distance: int = 1) -> Set[str]:
        """获取节点的邻居节点"""
        neighbors = set()
        visited = set()
        queue = [(node_id, 0)]
        
        while queue:
            current_id, distance = queue.pop(0)
            
            if current_id in visited or distance > max_distance:
                continue
            
            visited.add(current_id)
            
            if distance > 0:  # 不包括自己
                neighbors.add(current_id)
            
            if distance < max_distance:
                # 添加出边邻居
                for rel in self.get_relationships_from_source(current_id):
                    if rel.is_active:
                        queue.append((rel.target_id, distance + 1))
                
                # 添加入边邻居
                for rel in self.get_relationships_to_target(current_id):
                    if rel.is_active:
                        queue.append((rel.source_id, distance + 1))
        
        return neighbors
    
    def _update_indices(self, relationship: Relationship) -> None:
        """更新索引"""
        # 更新源索引
        if relationship.source_id not in self.source_index:
            self.source_index[relationship.source_id] = set()
        self.source_index[relationship.source_id].add(relationship.id)
        
        # 更新目标索引
        if relationship.target_id not in self.target_index:
            self.target_index[relationship.target_id] = set()
        self.target_index[relationship.target_id].add(relationship.id)
        
        # 更新类型索引
        if relationship.relationship_type not in self.type_index:
            self.type_index[relationship.relationship_type] = set()
        self.type_index[relationship.relationship_type].add(relationship.id)
