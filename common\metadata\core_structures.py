"""
可进化AGI系统核心数据结构
实现"语义自描述 + 演化型元数据结构"体系
使用"属性图+元知识图"统一表示一切
"""

from typing import Dict, List, Optional, Any, Union, Set
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import uuid
import json


class EvolutionType(Enum):
    """演化类型枚举"""
    CREATION = "creation"           # 创建
    MODIFICATION = "modification"   # 修改
    EXTENSION = "extension"         # 扩展
    DEPRECATION = "deprecation"     # 废弃
    MERGE = "merge"                # 合并
    SPLIT = "split"                # 分裂
    TRANSFORMATION = "transformation" # 转换


class SemanticType(Enum):
    """语义类型枚举"""
    ENTITY = "entity"               # 实体
    CONCEPT = "concept"             # 概念
    RELATION = "relation"           # 关系
    ATTRIBUTE = "attribute"         # 属性
    TYPE = "type"                   # 类型
    SCHEMA = "schema"               # 模式
    RULE = "rule"                   # 规则
    PATTERN = "pattern"             # 模式
    PROCESS = "process"             # 过程
    EVENT = "event"                 # 事件


class ConstraintType(Enum):
    """约束类型枚举"""
    REQUIRED = "required"           # 必需
    OPTIONAL = "optional"           # 可选
    UNIQUE = "unique"               # 唯一
    RANGE = "range"                 # 范围
    FORMAT = "format"               # 格式
    DEPENDENCY = "dependency"       # 依赖
    CARDINALITY = "cardinality"     # 基数
    TEMPORAL = "temporal"           # 时间


@dataclass
class SemanticDescriptor:
    """语义描述符 - 自描述语义信息"""
    semantic_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    semantic_type: SemanticType = SemanticType.ENTITY
    name: str = ""
    description: str = ""
    namespace: str = "default"
    version: str = "1.0.0"
    created_at: datetime = field(default_factory=datetime.now)
    created_by: str = "system"
    
    # 语义标签和分类
    tags: Set[str] = field(default_factory=set)
    categories: Set[str] = field(default_factory=set)
    
    # 语义关系
    parent_types: Set[str] = field(default_factory=set)
    child_types: Set[str] = field(default_factory=set)
    related_types: Set[str] = field(default_factory=set)
    
    # 自定义属性
    custom_properties: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "semantic_id": self.semantic_id,
            "semantic_type": self.semantic_type.value,
            "name": self.name,
            "description": self.description,
            "namespace": self.namespace,
            "version": self.version,
            "created_at": self.created_at.isoformat(),
            "created_by": self.created_by,
            "tags": list(self.tags),
            "categories": list(self.categories),
            "parent_types": list(self.parent_types),
            "child_types": list(self.child_types),
            "related_types": list(self.related_types),
            "custom_properties": self.custom_properties
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SemanticDescriptor':
        """从字典创建"""
        descriptor = cls()
        descriptor.semantic_id = data.get("semantic_id", descriptor.semantic_id)
        descriptor.semantic_type = SemanticType(data.get("semantic_type", "entity"))
        descriptor.name = data.get("name", "")
        descriptor.description = data.get("description", "")
        descriptor.namespace = data.get("namespace", "default")
        descriptor.version = data.get("version", "1.0.0")
        descriptor.created_by = data.get("created_by", "system")
        
        if "created_at" in data:
            descriptor.created_at = datetime.fromisoformat(data["created_at"])
        
        descriptor.tags = set(data.get("tags", []))
        descriptor.categories = set(data.get("categories", []))
        descriptor.parent_types = set(data.get("parent_types", []))
        descriptor.child_types = set(data.get("child_types", []))
        descriptor.related_types = set(data.get("related_types", []))
        descriptor.custom_properties = data.get("custom_properties", {})
        
        return descriptor


@dataclass
class EvolutionRecord:
    """演化记录 - 记录系统演化历史"""
    evolution_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    evolution_type: EvolutionType = EvolutionType.CREATION
    target_id: str = ""
    target_type: str = ""
    
    # 演化信息
    timestamp: datetime = field(default_factory=datetime.now)
    operator: str = "system"
    reason: str = ""
    description: str = ""
    
    # 变更详情
    before_state: Optional[Dict[str, Any]] = None
    after_state: Optional[Dict[str, Any]] = None
    changes: Dict[str, Any] = field(default_factory=dict)
    
    # 影响分析
    affected_entities: Set[str] = field(default_factory=set)
    impact_level: str = "low"  # low, medium, high, critical
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "evolution_id": self.evolution_id,
            "evolution_type": self.evolution_type.value,
            "target_id": self.target_id,
            "target_type": self.target_type,
            "timestamp": self.timestamp.isoformat(),
            "operator": self.operator,
            "reason": self.reason,
            "description": self.description,
            "before_state": self.before_state,
            "after_state": self.after_state,
            "changes": self.changes,
            "affected_entities": list(self.affected_entities),
            "impact_level": self.impact_level,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EvolutionRecord':
        """从字典创建"""
        record = cls()
        record.evolution_id = data.get("evolution_id", record.evolution_id)
        record.evolution_type = EvolutionType(data.get("evolution_type", "creation"))
        record.target_id = data.get("target_id", "")
        record.target_type = data.get("target_type", "")
        record.operator = data.get("operator", "system")
        record.reason = data.get("reason", "")
        record.description = data.get("description", "")
        record.before_state = data.get("before_state")
        record.after_state = data.get("after_state")
        record.changes = data.get("changes", {})
        record.affected_entities = set(data.get("affected_entities", []))
        record.impact_level = data.get("impact_level", "low")
        record.metadata = data.get("metadata", {})
        
        if "timestamp" in data:
            record.timestamp = datetime.fromisoformat(data["timestamp"])
        
        return record


@dataclass
class AttributeConstraint:
    """属性约束定义"""
    constraint_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    constraint_type: ConstraintType = ConstraintType.OPTIONAL
    name: str = ""
    description: str = ""
    
    # 约束参数
    parameters: Dict[str, Any] = field(default_factory=dict)
    
    # 验证规则
    validation_rules: List[str] = field(default_factory=list)
    error_messages: Dict[str, str] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "constraint_id": self.constraint_id,
            "constraint_type": self.constraint_type.value,
            "name": self.name,
            "description": self.description,
            "parameters": self.parameters,
            "validation_rules": self.validation_rules,
            "error_messages": self.error_messages
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AttributeConstraint':
        """从字典创建"""
        constraint = cls()
        constraint.constraint_id = data.get("constraint_id", constraint.constraint_id)
        constraint.constraint_type = ConstraintType(data.get("constraint_type", "optional"))
        constraint.name = data.get("name", "")
        constraint.description = data.get("description", "")
        constraint.parameters = data.get("parameters", {})
        constraint.validation_rules = data.get("validation_rules", [])
        constraint.error_messages = data.get("error_messages", {})
        return constraint


@dataclass
class AttributeSpecification:
    """属性规范 - 定义属性的结构和约束"""
    attribute_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    data_type: str = "string"  # string, number, boolean, object, array, etc.
    description: str = ""
    
    # 语义描述
    semantic_descriptor: Optional[SemanticDescriptor] = None
    
    # 约束定义
    constraints: List[AttributeConstraint] = field(default_factory=list)
    
    # 默认值和示例
    default_value: Any = None
    example_values: List[Any] = field(default_factory=list)
    
    # 演化信息
    version: str = "1.0.0"
    deprecated: bool = False
    replacement_attribute: Optional[str] = None
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "attribute_id": self.attribute_id,
            "name": self.name,
            "data_type": self.data_type,
            "description": self.description,
            "semantic_descriptor": self.semantic_descriptor.to_dict() if self.semantic_descriptor else None,
            "constraints": [c.to_dict() for c in self.constraints],
            "default_value": self.default_value,
            "example_values": self.example_values,
            "version": self.version,
            "deprecated": self.deprecated,
            "replacement_attribute": self.replacement_attribute,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AttributeSpecification':
        """从字典创建"""
        spec = cls()
        spec.attribute_id = data.get("attribute_id", spec.attribute_id)
        spec.name = data.get("name", "")
        spec.data_type = data.get("data_type", "string")
        spec.description = data.get("description", "")
        
        if data.get("semantic_descriptor"):
            spec.semantic_descriptor = SemanticDescriptor.from_dict(data["semantic_descriptor"])
        
        spec.constraints = [AttributeConstraint.from_dict(c) for c in data.get("constraints", [])]
        spec.default_value = data.get("default_value")
        spec.example_values = data.get("example_values", [])
        spec.version = data.get("version", "1.0.0")
        spec.deprecated = data.get("deprecated", False)
        spec.replacement_attribute = data.get("replacement_attribute")
        spec.metadata = data.get("metadata", {})
        
        return spec


@dataclass
class RelationSpecification:
    """关系规范 - 定义关系的结构和约束"""
    relation_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    
    # 关系类型
    relation_type: str = "association"  # association, composition, aggregation, inheritance, etc.
    directionality: str = "bidirectional"  # unidirectional, bidirectional
    
    # 语义描述
    semantic_descriptor: Optional[SemanticDescriptor] = None
    
    # 端点约束
    source_constraints: List[AttributeConstraint] = field(default_factory=list)
    target_constraints: List[AttributeConstraint] = field(default_factory=list)
    
    # 关系属性
    relation_attributes: List[AttributeSpecification] = field(default_factory=list)
    
    # 演化信息
    version: str = "1.0.0"
    deprecated: bool = False
    replacement_relation: Optional[str] = None
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "relation_id": self.relation_id,
            "name": self.name,
            "description": self.description,
            "relation_type": self.relation_type,
            "directionality": self.directionality,
            "semantic_descriptor": self.semantic_descriptor.to_dict() if self.semantic_descriptor else None,
            "source_constraints": [c.to_dict() for c in self.source_constraints],
            "target_constraints": [c.to_dict() for c in self.target_constraints],
            "relation_attributes": [a.to_dict() for a in self.relation_attributes],
            "version": self.version,
            "deprecated": self.deprecated,
            "replacement_relation": self.replacement_relation,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RelationSpecification':
        """从字典创建"""
        spec = cls()
        spec.relation_id = data.get("relation_id", spec.relation_id)
        spec.name = data.get("name", "")
        spec.description = data.get("description", "")
        spec.relation_type = data.get("relation_type", "association")
        spec.directionality = data.get("directionality", "bidirectional")
        
        if data.get("semantic_descriptor"):
            spec.semantic_descriptor = SemanticDescriptor.from_dict(data["semantic_descriptor"])
        
        spec.source_constraints = [AttributeConstraint.from_dict(c) for c in data.get("source_constraints", [])]
        spec.target_constraints = [AttributeConstraint.from_dict(c) for c in data.get("target_constraints", [])]
        spec.relation_attributes = [AttributeSpecification.from_dict(a) for a in data.get("relation_attributes", [])]
        spec.version = data.get("version", "1.0.0")
        spec.deprecated = data.get("deprecated", False)
        spec.replacement_relation = data.get("replacement_relation")
        spec.metadata = data.get("metadata", {})
        
        return spec


@dataclass
class TypeSchema:
    """类型模式 - 定义实体类型的完整结构"""
    schema_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    
    # 语义描述
    semantic_descriptor: Optional[SemanticDescriptor] = None
    
    # 继承关系
    parent_schemas: Set[str] = field(default_factory=set)
    child_schemas: Set[str] = field(default_factory=set)
    
    # 属性定义
    attributes: Dict[str, AttributeSpecification] = field(default_factory=dict)
    
    # 关系定义
    relations: Dict[str, RelationSpecification] = field(default_factory=dict)
    
    # 约束和规则
    schema_constraints: List[AttributeConstraint] = field(default_factory=list)
    validation_rules: List[str] = field(default_factory=list)
    
    # 演化信息
    version: str = "1.0.0"
    evolution_history: List[str] = field(default_factory=list)  # evolution_record_ids
    deprecated: bool = False
    replacement_schema: Optional[str] = None
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "schema_id": self.schema_id,
            "name": self.name,
            "description": self.description,
            "semantic_descriptor": self.semantic_descriptor.to_dict() if self.semantic_descriptor else None,
            "parent_schemas": list(self.parent_schemas),
            "child_schemas": list(self.child_schemas),
            "attributes": {k: v.to_dict() for k, v in self.attributes.items()},
            "relations": {k: v.to_dict() for k, v in self.relations.items()},
            "schema_constraints": [c.to_dict() for c in self.schema_constraints],
            "validation_rules": self.validation_rules,
            "version": self.version,
            "evolution_history": self.evolution_history,
            "deprecated": self.deprecated,
            "replacement_schema": self.replacement_schema,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TypeSchema':
        """从字典创建"""
        schema = cls()
        schema.schema_id = data.get("schema_id", schema.schema_id)
        schema.name = data.get("name", "")
        schema.description = data.get("description", "")
        
        if data.get("semantic_descriptor"):
            schema.semantic_descriptor = SemanticDescriptor.from_dict(data["semantic_descriptor"])
        
        schema.parent_schemas = set(data.get("parent_schemas", []))
        schema.child_schemas = set(data.get("child_schemas", []))
        
        schema.attributes = {
            k: AttributeSpecification.from_dict(v) 
            for k, v in data.get("attributes", {}).items()
        }
        
        schema.relations = {
            k: RelationSpecification.from_dict(v) 
            for k, v in data.get("relations", {}).items()
        }
        
        schema.schema_constraints = [AttributeConstraint.from_dict(c) for c in data.get("schema_constraints", [])]
        schema.validation_rules = data.get("validation_rules", [])
        schema.version = data.get("version", "1.0.0")
        schema.evolution_history = data.get("evolution_history", [])
        schema.deprecated = data.get("deprecated", False)
        schema.replacement_schema = data.get("replacement_schema")
        schema.metadata = data.get("metadata", {})
        
        return schema
