"""
PostgreSQL关系型数据库工具类
提供通用的PostgreSQL操作接口，与业务逻辑解耦
"""

import time
import threading
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import psycopg2
from psycopg2.extras import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>
from psycopg2.pool import ThreadedCon<PERSON>ionPool
from psycopg2 import sql

from .database_base import (
    RelationalDatabaseInterface, DatabaseConfig, QueryResult, OperationResult,
    ConnectionStatus, ConnectionException, QueryException, OperationException
)

import logging
logger = logging.getLogger(__name__)


class PostgreSQLConfig(DatabaseConfig):
    """PostgreSQL配置类"""
    
    def __init__(self, host: str, port: int, database: str, username: str, password: str,
                 min_conn: int = 1, max_conn: int = 20, connection_timeout: int = 30,
                 command_timeout: int = 60, **kwargs):
        super().__init__(
            host=host,
            port=port,
            database=database,
            username=username,
            password=password,
            timeout=connection_timeout,
            **kwargs
        )
        
        self.min_conn = min_conn
        self.max_conn = max_conn
        self.command_timeout = command_timeout


class PostgreSQLTool(RelationalDatabaseInterface):
    """PostgreSQL工具类"""
    
    def __init__(self, config: PostgreSQLConfig):
        """
        初始化PostgreSQL工具
        
        Args:
            config: PostgreSQL配置
        """
        super().__init__(config)
        self.config: PostgreSQLConfig = config
        self.connection_pool: Optional[ThreadedConnectionPool] = None
        self.lock = threading.Lock()
        
    def connect(self) -> bool:
        """建立连接池"""
        try:
            self.status = ConnectionStatus.CONNECTING
            
            self.connection_pool = ThreadedConnectionPool(
                minconn=self.config.min_conn,
                maxconn=self.config.max_conn,
                host=self.config.host,
                port=self.config.port,
                database=self.config.database,
                user=self.config.username,
                password=self.config.password,
                cursor_factory=RealDictCursor,
                connect_timeout=self.config.timeout
            )
            
            # 测试连接
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
            
            self.status = ConnectionStatus.CONNECTED
            self.connection_time = datetime.now()
            self.last_error = None
            
            logger.info(f"Connected to PostgreSQL at {self.config.host}:{self.config.port}")
            return True
            
        except Exception as e:
            self.status = ConnectionStatus.ERROR
            self.last_error = str(e)
            logger.error(f"Failed to connect to PostgreSQL: {e}")
            return False
    
    def disconnect(self) -> bool:
        """关闭连接池"""
        try:
            if self.connection_pool:
                self.connection_pool.closeall()
                self.connection_pool = None
            
            self.status = ConnectionStatus.DISCONNECTED
            logger.info("Disconnected from PostgreSQL")
            return True
            
        except Exception as e:
            logger.error(f"Error disconnecting from PostgreSQL: {e}")
            return False
    
    def is_connected(self) -> bool:
        """检查连接状态"""
        return (self.status == ConnectionStatus.CONNECTED and 
                self.connection_pool is not None)
    
    def ping(self) -> bool:
        """测试连接"""
        if not self.is_connected():
            return False
        
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
            return True
        except Exception:
            return False
    
    def _get_connection(self):
        """获取数据库连接"""
        if not self.connection_pool:
            raise ConnectionException("Connection pool not initialized")
        return self.connection_pool.getconn()
    
    def _put_connection(self, conn):
        """归还数据库连接"""
        if self.connection_pool:
            self.connection_pool.putconn(conn)
    
    def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> QueryResult:
        """执行查询"""
        if not self.is_connected():
            return QueryResult(
                success=False,
                error="Not connected to database"
            )
        
        start_time = time.time()
        conn = None
        
        try:
            conn = self._get_connection()
            with conn.cursor() as cursor:
                cursor.execute(query, params or {})
                
                if cursor.description:  # SELECT查询
                    records = cursor.fetchall()
                    records = [dict(record) for record in records]
                else:  # 非SELECT查询
                    records = []
                
                execution_time = time.time() - start_time
                
                return QueryResult(
                    success=True,
                    data=records,
                    count=len(records),
                    execution_time=execution_time
                )
                
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Query execution failed: {e}")
            
            return QueryResult(
                success=False,
                error=str(e),
                execution_time=execution_time
            )
        finally:
            if conn:
                self._put_connection(conn)
    
    def execute_command(self, command: str, params: Optional[Dict[str, Any]] = None) -> OperationResult:
        """执行命令"""
        if not self.is_connected():
            return OperationResult(
                success=False,
                error="Not connected to database"
            )
        
        start_time = time.time()
        conn = None
        
        try:
            conn = self._get_connection()
            with conn.cursor() as cursor:
                cursor.execute(command, params or {})
                affected_count = cursor.rowcount
                conn.commit()
                
                execution_time = time.time() - start_time
                
                return OperationResult(
                    success=True,
                    affected_count=affected_count,
                    execution_time=execution_time
                )
                
        except Exception as e:
            if conn:
                conn.rollback()
            
            execution_time = time.time() - start_time
            logger.error(f"Command execution failed: {e}")
            
            return OperationResult(
                success=False,
                error=str(e),
                execution_time=execution_time
            )
        finally:
            if conn:
                self._put_connection(conn)
    
    def create_table(self, table_name: str, schema: Dict[str, str]) -> OperationResult:
        """创建表"""
        columns = []
        for column_name, column_type in schema.items():
            columns.append(f"{column_name} {column_type}")
        
        query = f"CREATE TABLE IF NOT EXISTS {table_name} ({', '.join(columns)})"
        return self.execute_command(query)
    
    def insert_record(self, table_name: str, data: Dict[str, Any]) -> OperationResult:
        """插入记录"""
        columns = list(data.keys())
        placeholders = [f"%({col})s" for col in columns]
        
        query = f"""
        INSERT INTO {table_name} ({', '.join(columns)}) 
        VALUES ({', '.join(placeholders)})
        """
        
        return self.execute_command(query, data)
    
    def insert_records(self, table_name: str, data: List[Dict[str, Any]]) -> OperationResult:
        """批量插入记录"""
        if not data:
            return OperationResult(success=True, affected_count=0)
        
        if not self.is_connected():
            return OperationResult(
                success=False,
                error="Not connected to database"
            )
        
        start_time = time.time()
        conn = None
        
        try:
            conn = self._get_connection()
            with conn.cursor() as cursor:
                columns = list(data[0].keys())
                placeholders = [f"%({col})s" for col in columns]
                
                query = f"""
                INSERT INTO {table_name} ({', '.join(columns)}) 
                VALUES ({', '.join(placeholders)})
                """
                
                cursor.executemany(query, data)
                affected_count = cursor.rowcount
                conn.commit()
                
                execution_time = time.time() - start_time
                
                return OperationResult(
                    success=True,
                    affected_count=affected_count,
                    execution_time=execution_time
                )
                
        except Exception as e:
            if conn:
                conn.rollback()
            
            execution_time = time.time() - start_time
            logger.error(f"Batch insert failed: {e}")
            
            return OperationResult(
                success=False,
                error=str(e),
                execution_time=execution_time
            )
        finally:
            if conn:
                self._put_connection(conn)
    
    def update_record(self, table_name: str, data: Dict[str, Any], 
                     conditions: Dict[str, Any]) -> OperationResult:
        """更新记录"""
        set_clauses = [f"{col} = %({col})s" for col in data.keys()]
        where_clauses = [f"{col} = %(where_{col})s" for col in conditions.keys()]
        
        query = f"""
        UPDATE {table_name} 
        SET {', '.join(set_clauses)} 
        WHERE {' AND '.join(where_clauses)}
        """
        
        params = data.copy()
        for col, value in conditions.items():
            params[f"where_{col}"] = value
        
        return self.execute_command(query, params)
    
    def delete_record(self, table_name: str, conditions: Dict[str, Any]) -> OperationResult:
        """删除记录"""
        where_clauses = [f"{col} = %({col})s" for col in conditions.keys()]
        
        query = f"DELETE FROM {table_name} WHERE {' AND '.join(where_clauses)}"
        
        return self.execute_command(query, conditions)
    
    def select_records(self, table_name: str, columns: List[str] = None,
                      conditions: Dict[str, Any] = None, limit: int = 100, 
                      offset: int = 0, order_by: str = None) -> QueryResult:
        """查询记录"""
        # 构建SELECT子句
        if columns:
            select_clause = ", ".join(columns)
        else:
            select_clause = "*"
        
        query = f"SELECT {select_clause} FROM {table_name}"
        params = {}
        
        # 构建WHERE子句
        if conditions:
            where_clauses = [f"{col} = %({col})s" for col in conditions.keys()]
            query += f" WHERE {' AND '.join(where_clauses)}"
            params.update(conditions)
        
        # 构建ORDER BY子句
        if order_by:
            query += f" ORDER BY {order_by}"
        
        # 构建LIMIT和OFFSET子句
        query += f" LIMIT {limit} OFFSET {offset}"
        
        return self.execute_query(query, params)
    
    def execute_transaction(self, operations: List[Dict[str, Any]]) -> OperationResult:
        """执行事务"""
        if not self.is_connected():
            return OperationResult(
                success=False,
                error="Not connected to database"
            )
        
        start_time = time.time()
        conn = None
        total_affected = 0
        
        try:
            conn = self._get_connection()
            with conn.cursor() as cursor:
                for operation in operations:
                    op_type = operation.get('type')
                    table_name = operation.get('table')
                    data = operation.get('data', {})
                    conditions = operation.get('conditions', {})
                    
                    if op_type == 'insert':
                        columns = list(data.keys())
                        placeholders = [f"%({col})s" for col in columns]
                        query = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"
                        cursor.execute(query, data)
                        
                    elif op_type == 'update':
                        set_clauses = [f"{col} = %({col})s" for col in data.keys()]
                        where_clauses = [f"{col} = %(where_{col})s" for col in conditions.keys()]
                        query = f"UPDATE {table_name} SET {', '.join(set_clauses)} WHERE {' AND '.join(where_clauses)}"
                        params = data.copy()
                        for col, value in conditions.items():
                            params[f"where_{col}"] = value
                        cursor.execute(query, params)
                        
                    elif op_type == 'delete':
                        where_clauses = [f"{col} = %({col})s" for col in conditions.keys()]
                        query = f"DELETE FROM {table_name} WHERE {' AND '.join(where_clauses)}"
                        cursor.execute(query, conditions)
                    
                    total_affected += cursor.rowcount
                
                conn.commit()
                execution_time = time.time() - start_time
                
                return OperationResult(
                    success=True,
                    affected_count=total_affected,
                    execution_time=execution_time
                )
                
        except Exception as e:
            if conn:
                conn.rollback()
            
            execution_time = time.time() - start_time
            logger.error(f"Transaction failed: {e}")
            
            return OperationResult(
                success=False,
                error=str(e),
                execution_time=execution_time
            )
        finally:
            if conn:
                self._put_connection(conn)
    
    def get_table_info(self, table_name: str) -> Dict[str, Any]:
        """获取表信息"""
        query = """
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns
        WHERE table_name = %(table_name)s
        ORDER BY ordinal_position
        """
        
        result = self.execute_query(query, {"table_name": table_name})
        if result.success:
            return {
                "table_name": table_name,
                "columns": result.data
            }
        return {}
    
    def list_tables(self) -> List[str]:
        """列出所有表"""
        query = """
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
        ORDER BY table_name
        """
        
        result = self.execute_query(query)
        if result.success:
            return [record['table_name'] for record in result.data]
        return []
    
    def get_table_count(self, table_name: str) -> int:
        """获取表记录数"""
        query = f"SELECT COUNT(*) as count FROM {table_name}"
        result = self.execute_query(query)
        if result.success and result.data:
            return result.data[0]['count']
        return 0
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        base_stats = super().get_statistics()
        
        if self.is_connected():
            try:
                tables = self.list_tables()
                table_info = {}
                
                for table_name in tables[:10]:  # 只获取前10个表的信息
                    count = self.get_table_count(table_name)
                    table_info[table_name] = {"record_count": count}
                
                # 获取数据库大小
                size_query = "SELECT pg_size_pretty(pg_database_size(current_database())) as size"
                size_result = self.execute_query(size_query)
                db_size = size_result.data[0]['size'] if size_result.success and size_result.data else "Unknown"
                
                base_stats.update({
                    "database_name": self.config.database,
                    "table_count": len(tables),
                    "tables": table_info,
                    "database_size": db_size
                })
            except Exception as e:
                logger.error(f"Error getting PostgreSQL statistics: {e}")
        
        return base_stats
