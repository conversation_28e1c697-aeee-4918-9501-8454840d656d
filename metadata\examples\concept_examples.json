{"title": "AGI概念层元数据示例", "description": "展示概念层元数据标准的实际应用示例", "version": "1.0.0", "examples": [{"example_name": "抽象概念示例 - 人工智能", "description": "展示如何定义一个抽象的技术概念", "metadata": {"metadata_info": {"schema_version": "1.0.0", "created_at": "2024-01-01T10:00:00Z", "updated_at": "2024-01-01T10:00:00Z", "created_by": "expert_001", "last_modified_by": "expert_001"}, "concept_definition": {"concept_id": "concept_12345678-1234-1234-1234-123456789abc", "concept_name": "人工智能", "concept_type": "abstract", "definition": "人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。", "description": "人工智能涵盖了机器学习、深度学习、自然语言处理、计算机视觉等多个子领域，目标是让机器能够模拟人类的认知功能。", "aliases": ["AI", "Artificial Intelligence", "机器智能"], "keywords": ["人工智能", "机器学习", "智能系统", "认知计算"]}, "hierarchical_structure": {"abstraction_level": 3, "parent_concepts": ["concept_computer_science_001"], "child_concepts": ["concept_machine_learning_001", "concept_deep_learning_001", "concept_nlp_001", "concept_computer_vision_001"], "hierarchy_path": ["concept_technology_root", "concept_computer_science_001", "concept_12345678-1234-1234-1234-123456789abc"]}, "semantic_properties": {"domain": "computer_science", "semantic_tags": ["技术", "计算", "智能", "自动化"], "properties": {"complexity": {"value": "high", "data_type": "string", "description": "概念的复杂程度", "required": false}, "maturity": {"value": "developing", "data_type": "string", "description": "技术成熟度", "required": false}, "application_areas": {"value": ["医疗", "金融", "教育", "交通", "制造"], "data_type": "array", "description": "主要应用领域", "required": false}}}, "quality_metrics": {"confidence": 0.95, "completeness": 0.9, "consistency": 0.95, "relevance": 0.98, "usage_frequency": 1250, "last_accessed": "2024-01-01T09:30:00Z"}, "provenance_info": {"source": "expert_knowledge", "source_reference": "IEEE AI Standards Committee Definition", "author": "Dr. <PERSON>", "version": "1.0.0", "change_log": [{"version": "1.0.0", "timestamp": "2024-01-01T10:00:00Z", "author": "expert_001", "changes": "初始创建概念定义", "change_type": "create"}]}}}, {"example_name": "具体概念示例 - 神经网络", "description": "展示如何定义一个具体的技术概念", "metadata": {"metadata_info": {"schema_version": "1.0.0", "created_at": "2024-01-01T11:00:00Z", "updated_at": "2024-01-01T11:30:00Z", "created_by": "expert_002", "last_modified_by": "expert_002"}, "concept_definition": {"concept_id": "concept_87654321-4321-4321-4321-cba987654321", "concept_name": "神经网络", "concept_type": "concrete", "definition": "神经网络是一种受生物神经系统启发的计算模型，由相互连接的节点（神经元）组成，能够学习和识别模式。", "description": "神经网络通过调整连接权重来学习输入和输出之间的映射关系，广泛应用于模式识别、分类、回归等任务。", "aliases": ["Neural Network", "NN", "人工神经网络"], "keywords": ["神经网络", "深度学习", "机器学习", "模式识别", "权重", "激活函数"]}, "hierarchical_structure": {"abstraction_level": 2, "parent_concepts": ["concept_machine_learning_001"], "child_concepts": ["concept_cnn_001", "concept_rnn_001", "concept_transformer_001"], "sibling_concepts": ["concept_svm_001", "concept_decision_tree_001", "concept_random_forest_001"]}, "semantic_properties": {"domain": "computer_science", "semantic_tags": ["算法", "模型", "学习", "预测"], "properties": {"learning_type": {"value": "supervised_unsupervised", "data_type": "string", "description": "支持的学习类型"}, "architecture_types": {"value": ["feedforward", "recurrent", "convolutional"], "data_type": "array", "description": "主要架构类型"}, "computational_complexity": {"value": "high", "data_type": "string", "description": "计算复杂度"}}}, "quality_metrics": {"confidence": 0.98, "completeness": 0.95, "consistency": 0.97, "relevance": 0.96, "usage_frequency": 890, "last_accessed": "2024-01-01T11:15:00Z"}, "provenance_info": {"source": "automatic_extraction", "source_reference": "Deep Learning Textbook by <PERSON><PERSON><PERSON> et al.", "author": "AI System", "version": "1.1.0", "change_log": [{"version": "1.0.0", "timestamp": "2024-01-01T11:00:00Z", "author": "expert_002", "changes": "创建神经网络概念", "change_type": "create"}, {"version": "1.1.0", "timestamp": "2024-01-01T11:30:00Z", "author": "expert_002", "changes": "添加架构类型属性", "change_type": "update"}]}}}, {"example_name": "领域特定概念示例 - 医学诊断", "description": "展示如何定义医学领域的概念", "metadata": {"metadata_info": {"schema_version": "1.0.0", "created_at": "2024-01-01T14:00:00Z", "created_by": "medical_expert_001", "last_modified_by": "medical_expert_001"}, "concept_definition": {"concept_id": "concept_medical_diag_001", "concept_name": "医学诊断", "concept_type": "process", "definition": "医学诊断是医生通过收集患者症状、体征和检查结果来确定疾病或健康状况的过程。", "description": "诊断过程包括病史采集、体格检查、实验室检查、影像学检查等步骤，最终形成诊断结论和治疗方案。", "aliases": ["临床诊断", "疾病诊断", "Medical Diagnosis"], "keywords": ["诊断", "症状", "体征", "检查", "疾病", "医疗"]}, "hierarchical_structure": {"abstraction_level": 2, "parent_concepts": ["concept_medical_practice_001"], "child_concepts": ["concept_differential_diagnosis_001", "concept_imaging_diagnosis_001", "concept_laboratory_diagnosis_001"]}, "semantic_properties": {"domain": "medicine", "semantic_tags": ["医疗", "诊断", "临床", "疾病"], "properties": {"accuracy_requirement": {"value": "high", "data_type": "string", "description": "准确性要求级别"}, "time_sensitivity": {"value": "critical", "data_type": "string", "description": "时间敏感性"}, "regulatory_compliance": {"value": ["FDA", "NMPA", "CE"], "data_type": "array", "description": "需要遵循的监管标准"}}}, "quality_metrics": {"confidence": 0.99, "completeness": 0.98, "consistency": 0.99, "relevance": 0.97, "usage_frequency": 456}, "provenance_info": {"source": "expert_knowledge", "source_reference": "WHO International Classification of Diseases", "author": "Dr. <PERSON>", "version": "1.0.0"}, "validation_rules": {"structural_constraints": [{"rule_id": "MED_001", "rule_type": "domain_specific", "description": "医学概念必须有权威来源引用", "condition": "source_reference IS NOT NULL", "severity": "error"}]}}}, {"example_name": "动作概念示例 - 机器学习训练", "description": "展示如何定义动作类型的概念", "metadata": {"metadata_info": {"schema_version": "1.0.0", "created_at": "2024-01-01T15:00:00Z", "created_by": "ml_engineer_001"}, "concept_definition": {"concept_id": "concept_ml_training_001", "concept_name": "机器学习训练", "concept_type": "action", "definition": "机器学习训练是使用训练数据集来调整模型参数，使模型能够学习数据中的模式和规律的过程。", "description": "训练过程包括数据预处理、模型初始化、前向传播、损失计算、反向传播和参数更新等步骤。", "aliases": ["模型训练", "Model Training", "学习过程"], "keywords": ["训练", "学习", "参数", "优化", "数据集", "模型"]}, "hierarchical_structure": {"abstraction_level": 1, "parent_concepts": ["concept_machine_learning_001"], "child_concepts": ["concept_supervised_training_001", "concept_unsupervised_training_001", "concept_reinforcement_training_001"]}, "semantic_properties": {"domain": "computer_science", "semantic_tags": ["过程", "算法", "优化", "学习"], "properties": {"input_requirements": {"value": ["training_data", "model_architecture", "hyperparameters"], "data_type": "array", "description": "训练所需的输入"}, "output_results": {"value": ["trained_model", "training_metrics", "validation_results"], "data_type": "array", "description": "训练产生的输出"}, "computational_resources": {"value": "high", "data_type": "string", "description": "所需计算资源级别"}}}, "quality_metrics": {"confidence": 0.96, "completeness": 0.92, "consistency": 0.94, "relevance": 0.95, "usage_frequency": 678}, "provenance_info": {"source": "manual_input", "author": "ML Team", "version": "1.0.0"}}}, {"example_name": "扩展字段示例 - 自定义属性", "description": "展示如何使用扩展字段添加自定义属性", "metadata": {"metadata_info": {"schema_version": "1.0.0", "created_at": "2024-01-01T16:00:00Z", "created_by": "domain_expert_001"}, "concept_definition": {"concept_id": "concept_custom_example_001", "concept_name": "量子计算", "concept_type": "abstract", "definition": "量子计算是利用量子力学现象进行信息处理的计算范式。", "description": "量子计算机使用量子比特作为基本信息单位，能够利用叠加和纠缠等量子特性实现某些问题的指数级加速。", "keywords": ["量子计算", "量子比特", "叠加", "纠缠", "量子算法"]}, "hierarchical_structure": {"abstraction_level": 3, "parent_concepts": ["concept_computer_science_001"]}, "semantic_properties": {"domain": "computer_science"}, "quality_metrics": {"confidence": 0.88, "completeness": 0.85, "consistency": 0.9, "relevance": 0.92}, "provenance_info": {"source": "expert_knowledge", "version": "1.0.0"}, "extensions": {"ext_quantum_properties": {"coherence_time": "100 microseconds", "error_rate": "0.1%", "gate_fidelity": "99.9%"}, "ext_research_status": {"maturity_level": "experimental", "key_players": ["IBM", "Google", "Microsoft", "IonQ"], "breakthrough_year": 2019, "commercial_availability": "limited"}, "ext_complexity_metrics": {"quantum_volume": 64, "qubit_count": 127, "connectivity": "heavy_hex"}, "ext_application_domains": {"cryptography": "high_potential", "optimization": "medium_potential", "simulation": "high_potential", "machine_learning": "research_stage"}}}}], "usage_notes": ["这些示例展示了不同类型概念的元数据结构", "注意不同概念类型在hierarchical_structure中的差异", "quality_metrics应根据数据来源和验证程度设置", "extensions字段允许添加领域特定的自定义属性", "所有ID字段必须遵循UUID格式规范", "时间戳必须使用ISO 8601格式"], "validation_checklist": ["✓ 所有必需字段都已填写", "✓ ID格式符合规范", "✓ 时间戳格式正确", "✓ 枚举值在允许范围内", "✓ 层次关系逻辑正确", "✓ 质量指标在有效范围内", "✓ 扩展字段命名符合规范"]}