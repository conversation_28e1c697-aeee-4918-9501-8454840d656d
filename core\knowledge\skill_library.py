"""
技能库模块 - 具体任务执行步骤的技能库
"""
from typing import Dict, Any, List, Optional, Set, Union, Callable
from enum import Enum
from datetime import datetime
from pydantic import BaseModel, Field
from uuid import uuid4


class SkillType(str, Enum):
    """技能类型枚举"""
    COGNITIVE = "cognitive"         # 认知技能
    ANALYTICAL = "analytical"       # 分析技能
    CREATIVE = "creative"          # 创造技能
    COMMUNICATION = "communication" # 沟通技能
    PROBLEM_SOLVING = "problem_solving"  # 问题解决技能
    DECISION_MAKING = "decision_making"  # 决策技能
    LEARNING = "learning"          # 学习技能
    EXECUTION = "execution"        # 执行技能


class SkillComplexity(str, Enum):
    """技能复杂度枚举"""
    BASIC = "basic"               # 基础
    INTERMEDIATE = "intermediate"  # 中级
    ADVANCED = "advanced"         # 高级
    EXPERT = "expert"            # 专家级


class SkillStep(BaseModel):
    """技能步骤"""
    
    id: str = Field(default_factory=lambda: str(uuid4()))
    name: str = Field(..., description="步骤名称")
    description: str = Field(..., description="步骤描述")
    order: int = Field(..., description="执行顺序")
    
    # 步骤类型
    step_type: str = Field(default="action", description="步骤类型")
    
    # 输入输出
    inputs: Dict[str, Any] = Field(default_factory=dict, description="输入参数")
    outputs: Dict[str, Any] = Field(default_factory=dict, description="输出结果")
    
    # 执行条件
    preconditions: List[str] = Field(default_factory=list, description="前置条件")
    postconditions: List[str] = Field(default_factory=list, description="后置条件")
    
    # 执行参数
    parameters: Dict[str, Any] = Field(default_factory=dict, description="执行参数")
    timeout: Optional[int] = Field(None, description="超时时间（秒）")
    
    # 错误处理
    error_handling: Dict[str, Any] = Field(default_factory=dict, description="错误处理策略")
    retry_count: int = Field(default=0, description="重试次数")
    
    # 可选步骤
    is_optional: bool = Field(default=False, description="是否可选")
    skip_conditions: List[str] = Field(default_factory=list, description="跳过条件")
    
    def can_execute(self, context: Dict[str, Any]) -> bool:
        """检查是否可以执行"""
        # 检查前置条件
        for condition in self.preconditions:
            if not self._evaluate_condition(condition, context):
                return False
        
        # 检查跳过条件
        for condition in self.skip_conditions:
            if self._evaluate_condition(condition, context):
                return False
        
        return True
    
    def _evaluate_condition(self, condition: str, context: Dict[str, Any]) -> bool:
        """评估条件（简单实现）"""
        try:
            # 这里可以实现更复杂的条件评估逻辑
            return eval(condition, {"context": context})
        except Exception:
            return False


class Skill(BaseModel):
    """技能"""
    
    id: str = Field(default_factory=lambda: str(uuid4()))
    name: str = Field(..., description="技能名称")
    description: str = Field(..., description="技能描述")
    skill_type: SkillType = Field(..., description="技能类型")
    complexity: SkillComplexity = Field(default=SkillComplexity.BASIC, description="复杂度")
    
    # 技能步骤
    steps: List[SkillStep] = Field(default_factory=list, description="执行步骤")
    
    # 技能属性
    version: str = Field(default="1.0.0", description="版本号")
    category: Optional[str] = Field(None, description="分类")
    domain: Optional[str] = Field(None, description="领域")
    tags: List[str] = Field(default_factory=list, description="标签")
    
    # 依赖关系
    prerequisites: Set[str] = Field(default_factory=set, description="前置技能ID")
    dependencies: Set[str] = Field(default_factory=set, description="依赖技能ID")
    
    # 输入输出规范
    input_schema: Dict[str, Any] = Field(default_factory=dict, description="输入模式")
    output_schema: Dict[str, Any] = Field(default_factory=dict, description="输出模式")
    
    # 性能指标
    estimated_duration: Optional[int] = Field(None, description="预估执行时间（秒）")
    success_rate: float = Field(default=1.0, ge=0.0, le=1.0, description="成功率")
    
    # 使用统计
    usage_count: int = Field(default=0, description="使用次数")
    success_count: int = Field(default=0, description="成功次数")
    last_used: Optional[datetime] = Field(None, description="最后使用时间")
    
    # 元数据
    author: Optional[str] = Field(None, description="作者")
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    
    class Config:
        arbitrary_types_allowed = True
    
    def add_step(self, step: SkillStep) -> None:
        """添加步骤"""
        self.steps.append(step)
        self.steps.sort(key=lambda s: s.order)
        self.updated_at = datetime.now()
    
    def remove_step(self, step_id: str) -> bool:
        """移除步骤"""
        for i, step in enumerate(self.steps):
            if step.id == step_id:
                del self.steps[i]
                self.updated_at = datetime.now()
                return True
        return False
    
    def get_step(self, step_id: str) -> Optional[SkillStep]:
        """获取步骤"""
        for step in self.steps:
            if step.id == step_id:
                return step
        return None
    
    def validate_skill(self) -> List[str]:
        """验证技能完整性"""
        errors = []
        
        if not self.steps:
            errors.append("Skill has no steps")
        
        # 检查步骤顺序
        orders = [step.order for step in self.steps]
        if len(orders) != len(set(orders)):
            errors.append("Duplicate step orders found")
        
        # 检查步骤依赖
        for step in self.steps:
            for condition in step.preconditions:
                if not condition:
                    errors.append(f"Empty precondition in step: {step.name}")
        
        return errors
    
    def execute(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """执行技能"""
        execution_result = {
            "skill_id": self.id,
            "skill_name": self.name,
            "started_at": datetime.now().isoformat(),
            "steps_executed": [],
            "success": False,
            "error": None,
            "outputs": {}
        }
        
        try:
            # 更新使用统计
            self.usage_count += 1
            self.last_used = datetime.now()
            
            # 执行步骤
            for step in self.steps:
                if step.can_execute(context):
                    step_result = self._execute_step(step, context)
                    execution_result["steps_executed"].append(step_result)
                    
                    if not step_result.get("success", False) and not step.is_optional:
                        execution_result["error"] = f"Step '{step.name}' failed"
                        break
                    
                    # 更新上下文
                    context.update(step_result.get("outputs", {}))
                elif not step.is_optional:
                    execution_result["error"] = f"Step '{step.name}' cannot be executed"
                    break
            
            # 检查是否成功完成
            if not execution_result["error"]:
                execution_result["success"] = True
                execution_result["outputs"] = context
                self.success_count += 1
            
        except Exception as e:
            execution_result["error"] = str(e)
        
        execution_result["completed_at"] = datetime.now().isoformat()
        
        # 更新成功率
        if self.usage_count > 0:
            self.success_rate = self.success_count / self.usage_count
        
        return execution_result
    
    def _execute_step(self, step: SkillStep, context: Dict[str, Any]) -> Dict[str, Any]:
        """执行单个步骤"""
        step_result = {
            "step_id": step.id,
            "step_name": step.name,
            "started_at": datetime.now().isoformat(),
            "success": False,
            "outputs": {},
            "error": None
        }
        
        try:
            # 这里可以根据步骤类型执行不同的逻辑
            # 目前是简单的模拟执行
            
            # 模拟处理输入
            processed_inputs = {}
            for key, value in step.inputs.items():
                if isinstance(value, str) and value.startswith("${"):
                    # 从上下文中获取值
                    context_key = value[2:-1]
                    processed_inputs[key] = context.get(context_key, value)
                else:
                    processed_inputs[key] = value
            
            # 模拟执行逻辑
            step_outputs = step.outputs.copy()
            step_outputs.update(processed_inputs)
            
            step_result["success"] = True
            step_result["outputs"] = step_outputs
            
        except Exception as e:
            step_result["error"] = str(e)
        
        step_result["completed_at"] = datetime.now().isoformat()
        return step_result


class SkillLibrary:
    """技能库"""
    
    def __init__(self):
        self.skills: Dict[str, Skill] = {}
        
        # 索引
        self.type_index: Dict[SkillType, Set[str]] = {}
        self.complexity_index: Dict[SkillComplexity, Set[str]] = {}
        self.category_index: Dict[str, Set[str]] = {}
        self.domain_index: Dict[str, Set[str]] = {}
        self.tag_index: Dict[str, Set[str]] = {}
        
        # 依赖图
        self.dependency_graph: Dict[str, Set[str]] = {}
    
    def add_skill(self, skill: Skill) -> None:
        """添加技能"""
        validation_errors = skill.validate_skill()
        if validation_errors:
            raise ValueError(f"Skill validation failed: {validation_errors}")
        
        self.skills[skill.id] = skill
        self._update_indices(skill)
        self._update_dependency_graph(skill)
    
    def get_skill(self, skill_id: str) -> Optional[Skill]:
        """获取技能"""
        return self.skills.get(skill_id)
    
    def remove_skill(self, skill_id: str) -> bool:
        """移除技能"""
        if skill_id in self.skills:
            skill = self.skills[skill_id]
            del self.skills[skill_id]
            self._remove_from_indices(skill)
            self._remove_from_dependency_graph(skill_id)
            return True
        return False
    
    def search_skills(self, skill_type: Optional[SkillType] = None,
                     complexity: Optional[SkillComplexity] = None,
                     category: Optional[str] = None,
                     domain: Optional[str] = None,
                     tag: Optional[str] = None) -> List[Skill]:
        """搜索技能"""
        result_sets = []
        
        if skill_type:
            result_sets.append(self.type_index.get(skill_type, set()))
        
        if complexity:
            result_sets.append(self.complexity_index.get(complexity, set()))
        
        if category:
            result_sets.append(self.category_index.get(category, set()))
        
        if domain:
            result_sets.append(self.domain_index.get(domain, set()))
        
        if tag:
            result_sets.append(self.tag_index.get(tag, set()))
        
        if not result_sets:
            skill_ids = set(self.skills.keys())
        else:
            skill_ids = result_sets[0]
            for result_set in result_sets[1:]:
                skill_ids = skill_ids.intersection(result_set)
        
        return [self.skills[sid] for sid in skill_ids if sid in self.skills]
    
    def get_skill_dependencies(self, skill_id: str) -> List[str]:
        """获取技能依赖"""
        return list(self.dependency_graph.get(skill_id, set()))
    
    def get_execution_order(self, skill_ids: List[str]) -> List[str]:
        """获取技能执行顺序（拓扑排序）"""
        # 简单的拓扑排序实现
        in_degree = {skill_id: 0 for skill_id in skill_ids}
        
        # 计算入度
        for skill_id in skill_ids:
            dependencies = self.get_skill_dependencies(skill_id)
            for dep in dependencies:
                if dep in in_degree:
                    in_degree[skill_id] += 1
        
        # 拓扑排序
        queue = [skill_id for skill_id, degree in in_degree.items() if degree == 0]
        result = []
        
        while queue:
            current = queue.pop(0)
            result.append(current)
            
            # 更新依赖当前技能的其他技能的入度
            for skill_id in skill_ids:
                if current in self.get_skill_dependencies(skill_id):
                    in_degree[skill_id] -= 1
                    if in_degree[skill_id] == 0:
                        queue.append(skill_id)
        
        return result
    
    def get_library_statistics(self) -> Dict[str, Any]:
        """获取技能库统计信息"""
        total_skills = len(self.skills)
        
        if total_skills == 0:
            return {"total_skills": 0}
        
        # 按类型统计
        type_stats = {}
        for skill_type in SkillType:
            count = len(self.type_index.get(skill_type, set()))
            type_stats[skill_type.value] = count
        
        # 按复杂度统计
        complexity_stats = {}
        for complexity in SkillComplexity:
            count = len(self.complexity_index.get(complexity, set()))
            complexity_stats[complexity.value] = count
        
        # 使用统计
        usage_counts = [skill.usage_count for skill in self.skills.values()]
        success_rates = [skill.success_rate for skill in self.skills.values()]
        
        return {
            "total_skills": total_skills,
            "type_distribution": type_stats,
            "complexity_distribution": complexity_stats,
            "total_categories": len(self.category_index),
            "total_domains": len(self.domain_index),
            "total_tags": len(self.tag_index),
            "average_usage": sum(usage_counts) / len(usage_counts) if usage_counts else 0,
            "average_success_rate": sum(success_rates) / len(success_rates) if success_rates else 0,
            "most_used_skill": max(self.skills.values(), key=lambda s: s.usage_count).name if self.skills else None
        }
    
    def _update_indices(self, skill: Skill) -> None:
        """更新索引"""
        # 类型索引
        if skill.skill_type not in self.type_index:
            self.type_index[skill.skill_type] = set()
        self.type_index[skill.skill_type].add(skill.id)
        
        # 复杂度索引
        if skill.complexity not in self.complexity_index:
            self.complexity_index[skill.complexity] = set()
        self.complexity_index[skill.complexity].add(skill.id)
        
        # 分类索引
        if skill.category:
            if skill.category not in self.category_index:
                self.category_index[skill.category] = set()
            self.category_index[skill.category].add(skill.id)
        
        # 领域索引
        if skill.domain:
            if skill.domain not in self.domain_index:
                self.domain_index[skill.domain] = set()
            self.domain_index[skill.domain].add(skill.id)
        
        # 标签索引
        for tag in skill.tags:
            if tag not in self.tag_index:
                self.tag_index[tag] = set()
            self.tag_index[tag].add(skill.id)
    
    def _remove_from_indices(self, skill: Skill) -> None:
        """从索引中移除"""
        # 从类型索引移除
        if skill.skill_type in self.type_index:
            self.type_index[skill.skill_type].discard(skill.id)
        
        # 从复杂度索引移除
        if skill.complexity in self.complexity_index:
            self.complexity_index[skill.complexity].discard(skill.id)
        
        # 从分类索引移除
        if skill.category and skill.category in self.category_index:
            self.category_index[skill.category].discard(skill.id)
        
        # 从领域索引移除
        if skill.domain and skill.domain in self.domain_index:
            self.domain_index[skill.domain].discard(skill.id)
        
        # 从标签索引移除
        for tag in skill.tags:
            if tag in self.tag_index:
                self.tag_index[tag].discard(skill.id)
    
    def _update_dependency_graph(self, skill: Skill) -> None:
        """更新依赖图"""
        self.dependency_graph[skill.id] = skill.dependencies.union(skill.prerequisites)
    
    def _remove_from_dependency_graph(self, skill_id: str) -> None:
        """从依赖图中移除"""
        if skill_id in self.dependency_graph:
            del self.dependency_graph[skill_id]
        
        # 从其他技能的依赖中移除
        for deps in self.dependency_graph.values():
            deps.discard(skill_id)
