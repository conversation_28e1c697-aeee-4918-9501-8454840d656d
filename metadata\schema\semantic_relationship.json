{"$schema": "http://json-schema.org/draft-07/schema#", "title": "AGI语义关系层元数据标准", "description": "定义AGI系统中语义关系层的元数据结构和标准", "version": "1.0.0", "type": "object", "properties": {"metadata_info": {"type": "object", "description": "元数据基本信息", "properties": {"schema_version": {"type": "string", "description": "元数据模式版本", "pattern": "^\\d+\\.\\d+\\.\\d+$"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "created_by": {"type": "string"}, "last_modified_by": {"type": "string"}}, "required": ["schema_version", "created_at", "created_by"]}, "relationship_definition": {"type": "object", "description": "关系定义信息", "properties": {"relationship_id": {"type": "string", "description": "关系唯一标识符", "pattern": "^rel_[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$"}, "relationship_name": {"type": "string", "description": "关系名称", "minLength": 1, "maxLength": 255}, "relationship_type": {"type": "string", "description": "关系类型", "enum": ["is_a", "part_of", "contains", "has_property", "has_value", "causes", "caused_by", "enables", "prevents", "before", "after", "during", "simultaneous", "located_at", "near", "inside", "outside", "knows", "works_for", "owns", "uses", "implies", "contradicts", "supports", "depends_on", "similar_to", "different_from", "equivalent_to", "custom"]}, "custom_type_name": {"type": "string", "description": "自定义关系类型名称（当relationship_type为custom时使用）", "maxLength": 100}, "description": {"type": "string", "description": "关系描述", "maxLength": 1000}, "formal_definition": {"type": "string", "description": "关系的形式化定义", "maxLength": 2000}}, "required": ["relationship_id", "relationship_name", "relationship_type"]}, "relationship_structure": {"type": "object", "description": "关系结构信息", "properties": {"source_node": {"type": "object", "description": "源节点信息", "properties": {"node_id": {"type": "string", "description": "节点ID"}, "node_type": {"type": "string", "enum": ["concept", "entity", "knowledge", "task", "goal"]}, "node_name": {"type": "string", "description": "节点名称"}, "role": {"type": "string", "description": "在关系中的角色", "maxLength": 100}}, "required": ["node_id", "node_type"]}, "target_node": {"type": "object", "description": "目标节点信息", "properties": {"node_id": {"type": "string", "description": "节点ID"}, "node_type": {"type": "string", "enum": ["concept", "entity", "knowledge", "task", "goal"]}, "node_name": {"type": "string", "description": "节点名称"}, "role": {"type": "string", "description": "在关系中的角色", "maxLength": 100}}, "required": ["node_id", "node_type"]}, "directionality": {"type": "string", "description": "关系方向性", "enum": ["directed", "undirected", "bidirectional"]}, "cardinality": {"type": "object", "description": "关系基数", "properties": {"source_cardinality": {"type": "string", "description": "源节点基数", "enum": ["one", "many", "zero_or_one", "zero_or_many", "one_or_many"]}, "target_cardinality": {"type": "string", "description": "目标节点基数", "enum": ["one", "many", "zero_or_one", "zero_or_many", "one_or_many"]}}}}, "required": ["source_node", "target_node", "directionality"]}, "relationship_attributes": {"type": "object", "description": "关系属性", "properties": {"strength": {"type": "number", "description": "关系强度", "minimum": 0.0, "maximum": 1.0, "default": 1.0}, "confidence": {"type": "number", "description": "关系置信度", "minimum": 0.0, "maximum": 1.0, "default": 1.0}, "weight": {"type": "number", "description": "关系权重", "minimum": 0.0, "default": 1.0}, "probability": {"type": "number", "description": "关系概率", "minimum": 0.0, "maximum": 1.0}, "importance": {"type": "number", "description": "关系重要性", "minimum": 0.0, "maximum": 1.0}, "stability": {"type": "number", "description": "关系稳定性", "minimum": 0.0, "maximum": 1.0}}}, "temporal_properties": {"type": "object", "description": "时间属性", "properties": {"is_temporal": {"type": "boolean", "description": "是否为时间相关的关系", "default": false}, "valid_from": {"type": "string", "format": "date-time", "description": "关系有效开始时间"}, "valid_to": {"type": "string", "format": "date-time", "description": "关系有效结束时间"}, "duration": {"type": "string", "description": "关系持续时间（ISO 8601格式）", "pattern": "^P(?:\\d+Y)?(?:\\d+M)?(?:\\d+D)?(?:T(?:\\d+H)?(?:\\d+M)?(?:\\d+(?:\\.\\d+)?S)?)?$"}, "temporal_type": {"type": "string", "description": "时间类型", "enum": ["instant", "interval", "recurring", "conditional"]}, "frequency": {"type": "string", "description": "重复频率（对于recurring类型）", "enum": ["once", "daily", "weekly", "monthly", "yearly", "custom"]}}}, "contextual_properties": {"type": "object", "description": "上下文属性", "properties": {"context_dependent": {"type": "boolean", "description": "是否依赖上下文", "default": false}, "context_conditions": {"type": "array", "description": "上下文条件", "items": {"type": "object", "properties": {"condition_id": {"type": "string"}, "condition_type": {"type": "string", "enum": ["environmental", "temporal", "social", "cognitive", "physical"]}, "condition_expression": {"type": "string", "description": "条件表达式"}, "required": {"type": "boolean", "description": "是否为必需条件", "default": true}}, "required": ["condition_id", "condition_type", "condition_expression"]}}, "scope": {"type": "string", "description": "关系适用范围", "enum": ["global", "domain_specific", "context_specific", "local"]}, "domain_constraints": {"type": "array", "description": "领域约束", "items": {"type": "string"}}}}, "relationship_constraints": {"type": "object", "description": "关系约束", "properties": {"type_constraints": {"type": "object", "description": "类型约束", "properties": {"allowed_source_types": {"type": "array", "description": "允许的源节点类型", "items": {"type": "string"}}, "allowed_target_types": {"type": "array", "description": "允许的目标节点类型", "items": {"type": "string"}}, "forbidden_combinations": {"type": "array", "description": "禁止的类型组合", "items": {"type": "object", "properties": {"source_type": {"type": "string"}, "target_type": {"type": "string"}, "reason": {"type": "string"}}}}}}, "logical_constraints": {"type": "array", "description": "逻辑约束", "items": {"type": "object", "properties": {"constraint_id": {"type": "string"}, "constraint_type": {"type": "string", "enum": ["mutual_exclusion", "implication", "equivalence", "transitivity", "symmetry", "reflexivity"]}, "description": {"type": "string"}, "formal_expression": {"type": "string", "description": "形式化表达式"}, "severity": {"type": "string", "enum": ["error", "warning", "info"]}}, "required": ["constraint_id", "constraint_type", "formal_expression"]}}}}, "inverse_relationship": {"type": "object", "description": "逆关系信息", "properties": {"has_inverse": {"type": "boolean", "description": "是否存在逆关系", "default": false}, "inverse_relationship_id": {"type": "string", "description": "逆关系ID", "pattern": "^rel_[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$"}, "inverse_relationship_type": {"type": "string", "description": "逆关系类型"}, "symmetry_type": {"type": "string", "description": "对称性类型", "enum": ["symmetric", "asymmetric", "antisymmetric"]}}}, "quality_metrics": {"type": "object", "description": "质量度量指标", "properties": {"accuracy": {"type": "number", "description": "关系准确性", "minimum": 0.0, "maximum": 1.0}, "completeness": {"type": "number", "description": "关系完整性", "minimum": 0.0, "maximum": 1.0}, "consistency": {"type": "number", "description": "关系一致性", "minimum": 0.0, "maximum": 1.0}, "relevance": {"type": "number", "description": "关系相关性", "minimum": 0.0, "maximum": 1.0}, "usage_frequency": {"type": "integer", "description": "关系使用频率", "minimum": 0}, "validation_status": {"type": "string", "description": "验证状态", "enum": ["validated", "pending", "rejected", "needs_review"]}}}, "provenance_info": {"type": "object", "description": "数据溯源信息", "properties": {"source": {"type": "string", "enum": ["manual_annotation", "automatic_extraction", "machine_learning", "rule_inference", "expert_knowledge", "external_import", "user_feedback", "system_inference"]}, "extraction_method": {"type": "string", "description": "提取方法", "maxLength": 200}, "confidence_score": {"type": "number", "description": "提取置信度", "minimum": 0.0, "maximum": 1.0}, "source_reference": {"type": "string", "description": "来源引用", "maxLength": 500}, "author": {"type": "string", "maxLength": 100}, "version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$"}, "change_log": {"type": "array", "items": {"type": "object", "properties": {"version": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}, "author": {"type": "string"}, "changes": {"type": "string"}, "change_type": {"type": "string", "enum": ["create", "update", "delete", "merge", "validate", "invalidate"]}}}}}, "required": ["source", "version"]}, "extensions": {"type": "object", "description": "扩展字段", "patternProperties": {"^ext_[a-zA-Z_][a-zA-Z0-9_]*$": {"description": "扩展字段，字段名必须以ext_开头"}}}}, "required": ["metadata_info", "relationship_definition", "relationship_structure", "relationship_attributes", "quality_metrics", "provenance_info"], "additionalProperties": false}