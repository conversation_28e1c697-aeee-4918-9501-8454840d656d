{"$schema": "http://json-schema.org/draft-07/schema#", "title": "AGI系统管理层元数据标准", "description": "定义AGI系统中系统管理层的元数据结构和标准", "version": "1.0.0", "type": "object", "properties": {"metadata_info": {"type": "object", "description": "元数据基本信息", "properties": {"schema_version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "created_by": {"type": "string"}, "last_modified_by": {"type": "string"}}, "required": ["schema_version", "created_at", "created_by"]}, "system_configuration": {"type": "object", "description": "系统配置", "properties": {"system_id": {"type": "string", "description": "系统唯一标识符"}, "system_name": {"type": "string", "description": "系统名称"}, "system_version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$", "description": "系统版本"}, "deployment_environment": {"type": "string", "enum": ["development", "testing", "staging", "production", "disaster_recovery"]}, "architecture_type": {"type": "string", "enum": ["monolithic", "microservices", "serverless", "hybrid", "distributed"]}, "scalability_model": {"type": "string", "enum": ["vertical", "horizontal", "auto_scaling", "elastic"]}, "high_availability": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "redundancy_level": {"type": "string", "enum": ["none", "active_passive", "active_active", "multi_region"]}, "failover_time": {"type": "number", "description": "故障转移时间（秒）"}, "recovery_time_objective": {"type": "number", "description": "恢复时间目标（秒）"}, "recovery_point_objective": {"type": "number", "description": "恢复点目标（秒）"}}}}, "required": ["system_id", "system_name", "system_version", "deployment_environment"]}, "resource_management": {"type": "object", "description": "资源管理", "properties": {"computational_resources": {"type": "object", "description": "计算资源", "properties": {"cpu_allocation": {"type": "object", "properties": {"total_cores": {"type": "integer", "minimum": 1}, "allocated_cores": {"type": "integer", "minimum": 0}, "utilization_threshold": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "scaling_policy": {"type": "string", "enum": ["manual", "automatic", "scheduled", "predictive"]}}}, "memory_allocation": {"type": "object", "properties": {"total_memory_gb": {"type": "number", "minimum": 0.1}, "allocated_memory_gb": {"type": "number", "minimum": 0.0}, "memory_type": {"type": "string", "enum": ["ram", "virtual", "swap", "persistent"]}, "garbage_collection": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "strategy": {"type": "string", "enum": ["generational", "concurrent", "parallel", "incremental"]}}}}}, "storage_allocation": {"type": "object", "properties": {"total_storage_gb": {"type": "number", "minimum": 0.1}, "allocated_storage_gb": {"type": "number", "minimum": 0.0}, "storage_type": {"type": "string", "enum": ["ssd", "hdd", "nvme", "network", "cloud"]}, "backup_strategy": {"type": "string", "enum": ["full", "incremental", "differential", "continuous"]}, "retention_policy": {"type": "string", "description": "数据保留策略"}}}, "gpu_allocation": {"type": "object", "properties": {"gpu_count": {"type": "integer", "minimum": 0}, "gpu_type": {"type": "string"}, "memory_per_gpu_gb": {"type": "number", "minimum": 0.0}, "utilization_monitoring": {"type": "boolean"}}}}}, "network_resources": {"type": "object", "description": "网络资源", "properties": {"bandwidth_allocation": {"type": "object", "properties": {"total_bandwidth_mbps": {"type": "number", "minimum": 0.1}, "allocated_bandwidth_mbps": {"type": "number", "minimum": 0.0}, "qos_policies": {"type": "array", "items": {"type": "object", "properties": {"policy_name": {"type": "string"}, "priority": {"type": "integer", "minimum": 1}, "bandwidth_limit": {"type": "number"}}}}}}, "load_balancing": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "algorithm": {"type": "string", "enum": ["round_robin", "least_connections", "weighted", "ip_hash", "geographic"]}, "health_checks": {"type": "boolean"}, "session_persistence": {"type": "boolean"}}}, "cdn_configuration": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "provider": {"type": "string"}, "cache_policies": {"type": "array", "items": {"type": "string"}}, "geographic_distribution": {"type": "array", "items": {"type": "string"}}}}}}, "database_resources": {"type": "object", "description": "数据库资源", "properties": {"database_instances": {"type": "array", "items": {"type": "object", "properties": {"instance_id": {"type": "string"}, "database_type": {"type": "string", "enum": ["relational", "nosql", "graph", "time_series", "in_memory"]}, "engine": {"type": "string"}, "version": {"type": "string"}, "connection_pool": {"type": "object", "properties": {"max_connections": {"type": "integer"}, "min_connections": {"type": "integer"}, "timeout_seconds": {"type": "integer"}}}, "replication": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "replication_type": {"type": "string", "enum": ["master_slave", "master_master", "cluster"]}, "replica_count": {"type": "integer"}}}}}}}}}}, "performance_monitoring": {"type": "object", "description": "性能监控", "properties": {"monitoring_configuration": {"type": "object", "description": "监控配置", "properties": {"monitoring_enabled": {"type": "boolean"}, "monitoring_interval": {"type": "integer", "description": "监控间隔（秒）", "minimum": 1}, "data_retention_days": {"type": "integer", "minimum": 1}, "alerting_enabled": {"type": "boolean"}}}, "performance_metrics": {"type": "object", "description": "性能指标", "properties": {"system_metrics": {"type": "object", "properties": {"cpu_utilization": {"type": "object", "properties": {"current": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "average": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "peak": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "threshold": {"type": "number", "minimum": 0.0, "maximum": 1.0}}}, "memory_utilization": {"type": "object", "properties": {"current": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "average": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "peak": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "threshold": {"type": "number", "minimum": 0.0, "maximum": 1.0}}}, "disk_utilization": {"type": "object", "properties": {"current": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "growth_rate": {"type": "number"}, "threshold": {"type": "number", "minimum": 0.0, "maximum": 1.0}}}, "network_utilization": {"type": "object", "properties": {"inbound_mbps": {"type": "number", "minimum": 0.0}, "outbound_mbps": {"type": "number", "minimum": 0.0}, "packet_loss_rate": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "latency_ms": {"type": "number", "minimum": 0.0}}}}}, "application_metrics": {"type": "object", "properties": {"response_time": {"type": "object", "properties": {"average_ms": {"type": "number", "minimum": 0.0}, "p95_ms": {"type": "number", "minimum": 0.0}, "p99_ms": {"type": "number", "minimum": 0.0}, "sla_threshold_ms": {"type": "number", "minimum": 0.0}}}, "throughput": {"type": "object", "properties": {"requests_per_second": {"type": "number", "minimum": 0.0}, "transactions_per_second": {"type": "number", "minimum": 0.0}, "peak_throughput": {"type": "number", "minimum": 0.0}}}, "error_rates": {"type": "object", "properties": {"error_rate": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "timeout_rate": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "failure_rate": {"type": "number", "minimum": 0.0, "maximum": 1.0}}}, "availability": {"type": "object", "properties": {"uptime_percentage": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "sla_target": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "downtime_minutes": {"type": "number", "minimum": 0.0}}}}}}}, "alerting_rules": {"type": "array", "description": "告警规则", "items": {"type": "object", "properties": {"rule_id": {"type": "string"}, "rule_name": {"type": "string"}, "metric": {"type": "string"}, "condition": {"type": "string", "enum": ["greater_than", "less_than", "equals", "not_equals", "contains"]}, "threshold": {"type": "number"}, "severity": {"type": "string", "enum": ["info", "warning", "error", "critical"]}, "notification_channels": {"type": "array", "items": {"type": "string"}}, "escalation_policy": {"type": "string"}}, "required": ["rule_id", "rule_name", "metric", "condition", "threshold", "severity"]}}}}, "deployment_management": {"type": "object", "description": "部署管理", "properties": {"deployment_strategy": {"type": "string", "enum": ["blue_green", "rolling", "canary", "recreate", "a_b_testing"]}, "ci_cd_pipeline": {"type": "object", "description": "CI/CD流水线", "properties": {"pipeline_enabled": {"type": "boolean"}, "build_automation": {"type": "boolean"}, "test_automation": {"type": "boolean"}, "deployment_automation": {"type": "boolean"}, "rollback_capability": {"type": "boolean"}, "approval_gates": {"type": "array", "items": {"type": "object", "properties": {"stage": {"type": "string"}, "approval_required": {"type": "boolean"}, "approvers": {"type": "array", "items": {"type": "string"}}}}}}}, "environment_management": {"type": "object", "description": "环境管理", "properties": {"environment_isolation": {"type": "boolean"}, "configuration_management": {"type": "string", "enum": ["manual", "automated", "infrastructure_as_code"]}, "secret_management": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "secret_store": {"type": "string", "enum": ["vault", "kubernetes_secrets", "cloud_kms", "custom"]}, "rotation_policy": {"type": "string"}}}}}, "version_control": {"type": "object", "description": "版本控制", "properties": {"version_strategy": {"type": "string", "enum": ["semantic", "timestamp", "build_number", "git_hash"]}, "branching_strategy": {"type": "string", "enum": ["git_flow", "github_flow", "gitlab_flow", "trunk_based"]}, "release_management": {"type": "object", "properties": {"release_frequency": {"type": "string", "enum": ["continuous", "daily", "weekly", "monthly", "quarterly"]}, "hotfix_process": {"type": "boolean"}, "feature_flags": {"type": "boolean"}}}}}}}, "maintenance_operations": {"type": "object", "description": "维护操作", "properties": {"scheduled_maintenance": {"type": "array", "description": "计划维护", "items": {"type": "object", "properties": {"maintenance_id": {"type": "string"}, "maintenance_type": {"type": "string", "enum": ["security_update", "feature_update", "bug_fix", "performance_optimization", "infrastructure_upgrade"]}, "scheduled_time": {"type": "string", "format": "date-time"}, "estimated_duration": {"type": "string", "description": "预估持续时间（ISO 8601格式）"}, "impact_level": {"type": "string", "enum": ["no_impact", "low", "medium", "high", "service_interruption"]}, "notification_required": {"type": "boolean"}, "rollback_plan": {"type": "string"}}, "required": ["maintenance_id", "maintenance_type", "scheduled_time"]}}, "backup_operations": {"type": "object", "description": "备份操作", "properties": {"backup_enabled": {"type": "boolean"}, "backup_frequency": {"type": "string", "enum": ["real_time", "hourly", "daily", "weekly", "monthly"]}, "backup_types": {"type": "array", "items": {"type": "string", "enum": ["full", "incremental", "differential", "snapshot"]}}, "backup_retention": {"type": "string", "description": "备份保留期限"}, "backup_verification": {"type": "boolean"}, "disaster_recovery": {"type": "object", "properties": {"dr_enabled": {"type": "boolean"}, "dr_site_location": {"type": "string"}, "rto_minutes": {"type": "integer", "description": "恢复时间目标（分钟）"}, "rpo_minutes": {"type": "integer", "description": "恢复点目标（分钟）"}}}}}, "health_checks": {"type": "object", "description": "健康检查", "properties": {"health_check_enabled": {"type": "boolean"}, "check_frequency": {"type": "integer", "description": "检查频率（秒）"}, "health_endpoints": {"type": "array", "items": {"type": "object", "properties": {"endpoint": {"type": "string"}, "expected_status": {"type": "integer"}, "timeout_seconds": {"type": "integer"}}}}, "dependency_checks": {"type": "array", "items": {"type": "object", "properties": {"dependency_name": {"type": "string"}, "check_type": {"type": "string", "enum": ["ping", "http", "tcp", "database", "api"]}, "critical": {"type": "boolean"}}}}}}}}, "capacity_planning": {"type": "object", "description": "容量规划", "properties": {"current_capacity": {"type": "object", "description": "当前容量", "properties": {"cpu_capacity": {"type": "number", "description": "CPU容量利用率"}, "memory_capacity": {"type": "number", "description": "内存容量利用率"}, "storage_capacity": {"type": "number", "description": "存储容量利用率"}, "network_capacity": {"type": "number", "description": "网络容量利用率"}}}, "growth_projections": {"type": "object", "description": "增长预测", "properties": {"user_growth_rate": {"type": "number", "description": "用户增长率（年化）"}, "data_growth_rate": {"type": "number", "description": "数据增长率（年化）"}, "transaction_growth_rate": {"type": "number", "description": "交易增长率（年化）"}, "capacity_forecast": {"type": "array", "items": {"type": "object", "properties": {"forecast_date": {"type": "string", "format": "date"}, "projected_capacity": {"type": "number"}, "confidence_level": {"type": "number", "minimum": 0.0, "maximum": 1.0}}}}}}, "scaling_triggers": {"type": "array", "description": "扩容触发器", "items": {"type": "object", "properties": {"trigger_name": {"type": "string"}, "metric": {"type": "string"}, "threshold": {"type": "number"}, "action": {"type": "string", "enum": ["scale_up", "scale_down", "scale_out", "scale_in"]}, "cooldown_period": {"type": "integer", "description": "冷却期（秒）"}}}}}}, "compliance_governance": {"type": "object", "description": "合规治理", "properties": {"governance_framework": {"type": "string", "enum": ["ITIL", "COBIT", "ISO_20000", "custom"]}, "change_management": {"type": "object", "properties": {"change_approval_required": {"type": "boolean"}, "change_categories": {"type": "array", "items": {"type": "string", "enum": ["standard", "normal", "emergency", "major"]}}, "approval_workflow": {"type": "array", "items": {"type": "object", "properties": {"stage": {"type": "string"}, "approver_role": {"type": "string"}, "required": {"type": "boolean"}}}}}}, "documentation_requirements": {"type": "object", "properties": {"architecture_documentation": {"type": "boolean"}, "operational_procedures": {"type": "boolean"}, "security_documentation": {"type": "boolean"}, "compliance_reports": {"type": "boolean"}}}}}, "provenance_info": {"type": "object", "description": "数据溯源信息", "properties": {"system_owner": {"type": "string", "description": "系统所有者"}, "technical_contact": {"type": "string", "description": "技术联系人"}, "business_contact": {"type": "string", "description": "业务联系人"}, "last_review_date": {"type": "string", "format": "date", "description": "最后审查日期"}, "next_review_date": {"type": "string", "format": "date", "description": "下次审查日期"}, "version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$"}}, "required": ["system_owner", "technical_contact", "version"]}, "extensions": {"type": "object", "description": "扩展字段", "patternProperties": {"^ext_[a-zA-Z_][a-zA-Z0-9_]*$": {"description": "扩展字段，字段名必须以ext_开头"}}}}, "required": ["metadata_info", "system_configuration", "resource_management", "performance_monitoring", "provenance_info"], "additionalProperties": false}