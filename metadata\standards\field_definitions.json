{"$schema": "http://json-schema.org/draft-07/schema#", "title": "AGI元数据字段定义文档", "description": "定义AGI系统中所有元数据字段的详细规范", "version": "1.0.0", "field_definitions": {"common_fields": {"id_fields": {"concept_id": {"description": "概念的唯一标识符", "data_type": "string", "pattern": "^concept_[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$", "required": true, "immutable": true, "example": "concept_12345678-1234-1234-1234-123456789abc", "validation_rules": ["Must be unique across all concepts", "Must follow UUID v4 format with concept_ prefix"]}, "entity_id": {"description": "实体的唯一标识符", "data_type": "string", "pattern": "^entity_[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$", "required": true, "immutable": true, "example": "entity_8765**************-4321-cba987654321"}, "relationship_id": {"description": "关系的唯一标识符", "data_type": "string", "pattern": "^rel_[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$", "required": true, "immutable": true, "example": "rel_abcdef12-3456-7890-abcd-ef1234567890"}, "knowledge_id": {"description": "知识项的唯一标识符", "data_type": "string", "pattern": "^knowledge_[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$", "required": true, "immutable": true, "example": "knowledge_fedcba98-7654-3210-fedc-ba9876543210"}}, "temporal_fields": {"created_at": {"description": "创建时间戳", "data_type": "string", "format": "date-time", "required": true, "immutable": true, "example": "2024-01-01T12:00:00Z", "validation_rules": ["Must be in ISO 8601 format", "Must be in UTC timezone", "Cannot be in the future"]}, "updated_at": {"description": "最后更新时间戳", "data_type": "string", "format": "date-time", "required": false, "immutable": false, "example": "2024-01-02T15:30:00Z", "validation_rules": ["Must be in ISO 8601 format", "Must be >= created_at", "Automatically updated on modification"]}, "valid_from": {"description": "有效开始时间", "data_type": "string", "format": "date-time", "required": false, "immutable": false, "example": "2024-01-01T00:00:00Z"}, "valid_to": {"description": "有效结束时间", "data_type": "string", "format": "date-time", "required": false, "immutable": false, "example": "2024-12-31T23:59:59Z", "validation_rules": ["Must be > valid_from if both are specified"]}}, "provenance_fields": {"created_by": {"description": "创建者标识", "data_type": "string", "required": true, "immutable": true, "max_length": 100, "example": "user_123", "validation_rules": ["Must be a valid user identifier"]}, "last_modified_by": {"description": "最后修改者标识", "data_type": "string", "required": false, "immutable": false, "max_length": 100, "example": "user_456"}, "source": {"description": "数据来源", "data_type": "string", "required": true, "immutable": false, "allowed_values": ["manual_input", "automatic_extraction", "machine_learning", "expert_knowledge", "external_import", "user_feedback", "system_inference"], "example": "manual_input"}, "version": {"description": "版本号", "data_type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$", "required": true, "immutable": false, "example": "1.0.0", "validation_rules": ["Must follow semantic versioning format", "Version must increment on changes"]}}, "quality_fields": {"confidence": {"description": "置信度", "data_type": "number", "minimum": 0.0, "maximum": 1.0, "required": false, "immutable": false, "default": 1.0, "example": 0.85, "validation_rules": ["Must be between 0.0 and 1.0 inclusive"]}, "accuracy": {"description": "准确性", "data_type": "number", "minimum": 0.0, "maximum": 1.0, "required": false, "immutable": false, "example": 0.92}, "completeness": {"description": "完整性", "data_type": "number", "minimum": 0.0, "maximum": 1.0, "required": false, "immutable": false, "example": 0.78}, "consistency": {"description": "一致性", "data_type": "number", "minimum": 0.0, "maximum": 1.0, "required": false, "immutable": false, "example": 0.95}, "relevance": {"description": "相关性", "data_type": "number", "minimum": 0.0, "maximum": 1.0, "required": false, "immutable": false, "example": 0.88}}}, "concept_specific_fields": {"concept_name": {"description": "概念名称", "data_type": "string", "required": true, "immutable": false, "min_length": 1, "max_length": 255, "example": "人工智能", "validation_rules": ["Must be unique within the same domain", "Cannot contain special characters except spaces, hyphens, and underscores"]}, "concept_type": {"description": "概念类型", "data_type": "string", "required": true, "immutable": false, "allowed_values": ["abstract", "concrete", "relation", "attribute", "action", "state", "event", "process"], "example": "abstract"}, "abstraction_level": {"description": "抽象层级", "data_type": "integer", "required": false, "immutable": false, "minimum": 0, "maximum": 10, "default": 0, "example": 3, "validation_rules": ["Higher numbers indicate more abstract concepts", "Must be consistent with parent-child relationships"]}, "domain": {"description": "所属领域", "data_type": "string", "required": false, "immutable": false, "allowed_values": ["general", "science", "technology", "medicine", "biology", "physics", "chemistry", "mathematics", "computer_science", "psychology", "philosophy", "linguistics", "economics", "sociology", "history", "geography", "arts", "literature", "music", "sports", "business", "law", "education", "custom"], "example": "computer_science"}}, "relationship_specific_fields": {"relationship_type": {"description": "关系类型", "data_type": "string", "required": true, "immutable": false, "allowed_values": ["is_a", "part_of", "contains", "has_property", "has_value", "causes", "caused_by", "enables", "prevents", "before", "after", "during", "simultaneous", "located_at", "near", "inside", "outside", "knows", "works_for", "owns", "uses", "implies", "contradicts", "supports", "depends_on", "similar_to", "different_from", "equivalent_to", "custom"], "example": "is_a"}, "source_id": {"description": "源节点ID", "data_type": "string", "required": true, "immutable": false, "example": "concept_12345678-1234-1234-1234-123456789abc", "validation_rules": ["Must reference an existing node", "Cannot be the same as target_id for non-reflexive relationships"]}, "target_id": {"description": "目标节点ID", "data_type": "string", "required": true, "immutable": false, "example": "entity_8765**************-4321-cba987654321", "validation_rules": ["Must reference an existing node", "Cannot be the same as source_id for non-reflexive relationships"]}, "strength": {"description": "关系强度", "data_type": "number", "required": false, "immutable": false, "minimum": 0.0, "maximum": 1.0, "default": 1.0, "example": 0.8, "validation_rules": ["Higher values indicate stronger relationships"]}, "weight": {"description": "关系权重", "data_type": "number", "required": false, "immutable": false, "minimum": 0.0, "default": 1.0, "example": 2.5, "validation_rules": ["Used for graph algorithms and pathfinding"]}, "directionality": {"description": "关系方向性", "data_type": "string", "required": true, "immutable": false, "allowed_values": ["directed", "undirected", "bidirectional"], "example": "directed"}}, "knowledge_specific_fields": {"title": {"description": "知识标题", "data_type": "string", "required": true, "immutable": false, "min_length": 1, "max_length": 500, "example": "机器学习基础概念"}, "knowledge_type": {"description": "知识类型", "data_type": "string", "required": true, "immutable": false, "allowed_values": ["factual", "procedural", "declarative", "conceptual", "metacognitive", "experiential", "conditional", "causal", "temporal", "spatial"], "example": "factual"}, "content": {"description": "知识内容", "data_type": "any", "required": true, "immutable": false, "example": "机器学习是人工智能的一个分支...", "validation_rules": ["Content format must match content_format field", "Must not be empty"]}, "content_format": {"description": "内容格式", "data_type": "string", "required": true, "immutable": false, "allowed_values": ["text", "structured_data", "rule_based", "mathematical", "logical", "multimedia", "code", "diagram", "table", "graph"], "example": "text"}, "keywords": {"description": "关键词列表", "data_type": "array", "items_type": "string", "required": false, "immutable": false, "max_items": 50, "item_max_length": 50, "example": ["机器学习", "人工智能", "算法"], "validation_rules": ["Keywords must be unique within the array", "Keywords should be relevant to the content"]}, "category": {"description": "知识分类", "data_type": "string", "required": false, "immutable": false, "max_length": 100, "example": "技术文档"}}, "security_fields": {"classification_level": {"description": "安全分类级别", "data_type": "string", "required": true, "immutable": false, "allowed_values": ["public", "internal", "confidential", "restricted", "top_secret"], "example": "internal"}, "access_level": {"description": "访问级别", "data_type": "string", "required": true, "immutable": false, "allowed_values": ["none", "limited", "standard", "elevated", "full"], "example": "standard"}, "permissions": {"description": "权限列表", "data_type": "array", "items_type": "string", "required": true, "immutable": false, "allowed_item_values": ["read", "write", "execute", "delete", "admin", "audit"], "example": ["read", "write"]}}, "extension_fields": {"extensions": {"description": "扩展字段容器", "data_type": "object", "required": false, "immutable": false, "pattern_properties": {"^ext_[a-zA-Z_][a-zA-Z0-9_]*$": {"description": "扩展字段，字段名必须以ext_开头"}}, "validation_rules": ["Extension field names must start with 'ext_'", "Extension fields allow custom attributes without breaking schema"]}}}, "data_types": {"string": {"description": "文本字符串", "validation": "UTF-8 encoded text", "examples": ["hello", "世界", "123"]}, "number": {"description": "数值类型", "validation": "IEEE 754 double precision", "examples": [1, 3.14, -2.5, 0]}, "integer": {"description": "整数类型", "validation": "Whole numbers only", "examples": [1, -5, 0, 1000]}, "boolean": {"description": "布尔类型", "validation": "true or false only", "examples": [true, false]}, "array": {"description": "数组类型", "validation": "Ordered list of items", "examples": [["a", "b", "c"], [1, 2, 3]]}, "object": {"description": "对象类型", "validation": "Key-value pairs", "examples": [{"key": "value"}, {"name": "<PERSON>", "age": 30}]}, "date-time": {"description": "日期时间类型", "validation": "ISO 8601 format", "examples": ["2024-01-01T12:00:00Z", "2024-12-31T23:59:59.999Z"]}, "date": {"description": "日期类型", "validation": "ISO 8601 date format", "examples": ["2024-01-01", "2024-12-31"]}, "uri": {"description": "URI类型", "validation": "Valid URI format", "examples": ["https://example.com", "ftp://files.example.com/file.txt"]}}, "validation_rules": {"general_rules": ["All required fields must be present", "Field values must match specified data types", "String fields must not exceed maximum length", "Numeric fields must be within specified ranges", "Array fields must not exceed maximum item count", "Enum fields must use only allowed values"], "cross_field_validation": ["updated_at must be >= created_at", "valid_to must be > valid_from", "source_id and target_id must reference existing nodes", "Version numbers must increment on changes"], "business_rules": ["Concept names must be unique within domain", "Relationship types must be semantically valid", "Security classifications must be consistent", "Extension fields must follow naming conventions"]}, "naming_conventions": {"field_names": {"format": "snake_case", "pattern": "^[a-z][a-z0-9_]*$", "examples": ["concept_name", "created_at", "access_level"]}, "id_fields": {"format": "prefix_uuid", "pattern": "^[a-z]+_[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$", "examples": ["concept_12345678-1234-1234-1234-123456789abc"]}, "extension_fields": {"format": "ext_snake_case", "pattern": "^ext_[a-zA-Z_][a-zA-Z0-9_]*$", "examples": ["ext_custom_field", "ext_domain_specific_attr"]}}}