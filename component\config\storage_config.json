{"description": "AGI系统数据存储配置文件", "version": "1.0.0", "last_updated": "2024-01-01T00:00:00Z", "knowledge_graph": {"enabled": true, "description": "知识图谱存储 - 用于高效处理关联查询", "neo4j": {"uri": "bolt://localhost:7687", "username": "neo4j", "password": "your_neo4j_password", "database": "agi_knowledge", "connection_timeout": 30, "max_retry_time": 30, "encrypted": false}, "performance": {"batch_size": 1000, "cache_enabled": true, "cache_size": "512MB", "query_timeout": 60}, "backup": {"enabled": true, "schedule": "0 2 * * *", "retention_days": 30, "backup_path": "/backup/neo4j"}}, "execution_log": {"enabled": true, "description": "执行日志存储 - 适合按时间分析进化趋势", "influxdb": {"url": "http://localhost:8086", "token": "your_influxdb_token", "org": "agi_organization", "bucket": "agi_execution_logs", "timeout": 10000, "verify_ssl": false}, "retention_policy": {"execution_events": "30d", "performance_metrics": "90d", "learning_progress": "1y", "error_logs": "180d"}, "aggregation": {"enabled": true, "intervals": ["1m", "5m", "1h", "1d"], "functions": ["mean", "max", "min", "count"]}, "alerting": {"enabled": true, "error_rate_threshold": 0.05, "response_time_threshold": 5000, "cpu_usage_threshold": 0.8, "memory_usage_threshold": 0.9}}, "model_parameters": {"enabled": true, "description": "模型参数存储 - 快速检索相似案例", "milvus": {"host": "localhost", "port": "19530", "alias": "agi_milvus", "default_collection": "agi_model_parameters", "vector_dim": 512, "index_type": "IVF_FLAT", "metric_type": "L2", "connection_timeout": 30}, "index_config": {"nlist": 1024, "nprobe": 10, "m": 16, "nbits": 8}, "search_config": {"default_top_k": 10, "max_top_k": 100, "search_timeout": 30}, "storage": {"segment_size": "512MB", "auto_flush": true, "flush_interval": 1}}, "system_metadata": {"enabled": true, "description": "系统元数据存储 - 保证事务完整性", "postgresql": {"host": "localhost", "port": 5432, "database": "agi_metadata", "username": "agi_user", "password": "your_postgresql_password", "min_conn": 2, "max_conn": 20, "connection_timeout": 30, "command_timeout": 60}, "schema": {"auto_create": true, "migration_enabled": true, "version_tracking": true}, "backup": {"enabled": true, "schedule": "0 3 * * *", "retention_days": 90, "backup_path": "/backup/postgresql", "compression": true}, "performance": {"statement_timeout": "30s", "lock_timeout": "10s", "idle_in_transaction_session_timeout": "60s", "log_min_duration_statement": 1000}}, "monitoring": {"enabled": true, "health_check_interval": 60, "metrics_collection_interval": 30, "log_level": "INFO", "performance_monitoring": {"enabled": true, "cpu_monitoring": true, "memory_monitoring": true, "disk_monitoring": true, "network_monitoring": true}}, "security": {"encryption": {"enabled": true, "algorithm": "AES-256-GCM", "key_rotation_days": 90}, "access_control": {"enabled": true, "authentication_required": true, "authorization_enabled": true, "session_timeout": 3600}, "audit": {"enabled": true, "log_all_operations": true, "sensitive_data_masking": true, "retention_days": 365}}, "disaster_recovery": {"enabled": true, "replication": {"enabled": true, "sync_mode": "async", "replica_locations": ["backup_site_1", "backup_site_2"]}, "failover": {"automatic": true, "timeout": 300, "health_check_interval": 30}, "recovery": {"point_in_time_recovery": true, "backup_verification": true, "recovery_testing_schedule": "monthly"}}, "development": {"debug_mode": false, "test_data_enabled": false, "mock_services": false, "profiling_enabled": false}, "production": {"high_availability": true, "load_balancing": true, "auto_scaling": true, "resource_limits": {"max_memory_usage": "8GB", "max_cpu_usage": "80%", "max_disk_usage": "90%", "max_connections": 1000}}}