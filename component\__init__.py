"""
AGI数据存储组件包
提供多种专业化的存储解决方案来满足AGI系统不同类型数据的存储和查询需求
"""

__version__ = "1.0.0"
__author__ = "AGI Development Team"
__description__ = "AGI系统数据存储组件"

# 导入主要组件
from .data_storage_manager import (
    DataStorageManager,
    initialize_storage_manager,
    get_storage_manager,
    shutdown_storage_manager,
    # 便捷函数
    add_knowledge,
    log_event,
    store_model,
    store_metadata
)

from .knowledge_graph import (
    KnowledgeGraphManager,
    KnowledgeGraphService,
    GraphNode,
    GraphRelationship,
    GraphPath
)

from .execution_log import (
    ExecutionLogManager,
    ExecutionLogService,
    ExecutionEvent,
    PerformanceMetrics,
    LearningProgress
)

from .model_parameters import (
    ModelParameterManager,
    ModelParameterService,
    ModelVector,
    SimilarityResult
)

from .system_metadata import (
    SystemMetadataManager,
    SystemMetadataService,
    MetadataRecord,
    SchemaDefinition
)

# 定义公开的API
__all__ = [
    # 主要管理器
    "DataStorageManager",
    "initialize_storage_manager",
    "get_storage_manager",
    "shutdown_storage_manager",
    
    # 便捷函数
    "add_knowledge",
    "log_event", 
    "store_model",
    "store_metadata",
    
    # 知识图谱组件
    "KnowledgeGraphManager",
    "KnowledgeGraphService",
    "GraphNode",
    "GraphRelationship", 
    "GraphPath",
    
    # 执行日志组件
    "ExecutionLogManager",
    "ExecutionLogService",
    "ExecutionEvent",
    "PerformanceMetrics",
    "LearningProgress",
    
    # 模型参数组件
    "ModelParameterManager",
    "ModelParameterService",
    "ModelVector",
    "SimilarityResult",
    
    # 系统元数据组件
    "SystemMetadataManager",
    "SystemMetadataService",
    "MetadataRecord",
    "SchemaDefinition"
]

# 组件信息
COMPONENTS = {
    "knowledge_graph": {
        "name": "知识图谱组件",
        "description": "基于Neo4j的知识图谱存储，用于高效处理关联查询",
        "storage_type": "图数据库",
        "database": "Neo4j",
        "use_cases": ["概念关系建模", "语义搜索", "推理查询", "知识发现"]
    },
    "execution_log": {
        "name": "执行日志组件", 
        "description": "基于InfluxDB的时序数据存储，适合按时间分析进化趋势",
        "storage_type": "时序数据库",
        "database": "InfluxDB",
        "use_cases": ["性能监控", "日志分析", "趋势预测", "异常检测"]
    },
    "model_parameters": {
        "name": "模型参数组件",
        "description": "基于Milvus的向量数据库，快速检索相似案例",
        "storage_type": "向量数据库", 
        "database": "Milvus",
        "use_cases": ["相似性搜索", "模型检索", "特征匹配", "推荐系统"]
    },
    "system_metadata": {
        "name": "系统元数据组件",
        "description": "基于PostgreSQL的关系型数据库，保证事务完整性",
        "storage_type": "关系型数据库",
        "database": "PostgreSQL", 
        "use_cases": ["元数据管理", "版本控制", "配置管理", "审计日志"]
    }
}

def get_component_info(component_name: str = None):
    """
    获取组件信息
    
    Args:
        component_name: 组件名称，如果为None则返回所有组件信息
        
    Returns:
        组件信息字典
    """
    if component_name:
        return COMPONENTS.get(component_name)
    return COMPONENTS

def list_components():
    """列出所有可用组件"""
    return list(COMPONENTS.keys())

def get_version():
    """获取版本信息"""
    return __version__

def get_dependencies():
    """获取依赖信息"""
    return {
        "neo4j": ">=5.0.0",
        "influxdb-client": ">=1.36.0", 
        "pymilvus": ">=2.3.0",
        "psycopg2-binary": ">=2.9.0",
        "numpy": ">=1.21.0"
    }

# 模块级别的便捷函数
def quick_start(config_file: str = None, config_dict: dict = None):
    """
    快速启动存储管理器
    
    Args:
        config_file: 配置文件路径
        config_dict: 配置字典
        
    Returns:
        是否启动成功
    """
    return initialize_storage_manager(config_file, config_dict)

def quick_shutdown():
    """快速关闭存储管理器"""
    shutdown_storage_manager()

# 打印包信息
def print_package_info():
    """打印包信息"""
    print(f"AGI数据存储组件包 v{__version__}")
    print(f"作者: {__author__}")
    print(f"描述: {__description__}")
    print("\n可用组件:")
    for name, info in COMPONENTS.items():
        print(f"  - {info['name']} ({info['database']})")
        print(f"    {info['description']}")
    print(f"\n依赖包:")
    for pkg, version in get_dependencies().items():
        print(f"  - {pkg} {version}")

# 在导入时显示简要信息
import logging
logger = logging.getLogger(__name__)
logger.info(f"AGI数据存储组件包 v{__version__} 已加载")
logger.info(f"可用组件: {', '.join(list_components())}")
