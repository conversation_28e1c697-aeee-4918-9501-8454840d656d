"""
本体管理模块 - 用于概念的分类和组织
"""
from typing import Dict, Any, List, Optional, Set, Tuple
from datetime import datetime
from pydantic import BaseModel, Field
from uuid import uuid4

from ..entities.concept import Concept, ConceptType


class OntologyClass(BaseModel):
    """本体类"""
    
    id: str = Field(default_factory=lambda: str(uuid4()))
    name: str = Field(..., description="类名")
    description: Optional[str] = Field(None, description="类描述")
    parent_classes: Set[str] = Field(default_factory=set, description="父类ID集合")
    child_classes: Set[str] = Field(default_factory=set, description="子类ID集合")
    properties: Set[str] = Field(default_factory=set, description="属性集合")
    instances: Set[str] = Field(default_factory=set, description="实例ID集合")
    
    # 约束和规则
    constraints: List[Dict[str, Any]] = Field(default_factory=list, description="约束条件")
    axioms: List[str] = Field(default_factory=list, description="公理")
    
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    
    class Config:
        arbitrary_types_allowed = True
    
    def add_parent_class(self, parent_id: str) -> None:
        """添加父类"""
        self.parent_classes.add(parent_id)
        self.updated_at = datetime.now()
    
    def add_child_class(self, child_id: str) -> None:
        """添加子类"""
        self.child_classes.add(child_id)
        self.updated_at = datetime.now()
    
    def add_property(self, property_name: str) -> None:
        """添加属性"""
        self.properties.add(property_name)
        self.updated_at = datetime.now()
    
    def add_instance(self, instance_id: str) -> None:
        """添加实例"""
        self.instances.add(instance_id)
        self.updated_at = datetime.now()


class OntologyProperty(BaseModel):
    """本体属性"""
    
    id: str = Field(default_factory=lambda: str(uuid4()))
    name: str = Field(..., description="属性名")
    description: Optional[str] = Field(None, description="属性描述")
    domain: Set[str] = Field(default_factory=set, description="定义域（类ID集合）")
    range: Set[str] = Field(default_factory=set, description="值域（类ID集合）")
    
    # 属性特征
    is_functional: bool = Field(default=False, description="是否函数属性")
    is_inverse_functional: bool = Field(default=False, description="是否逆函数属性")
    is_transitive: bool = Field(default=False, description="是否传递属性")
    is_symmetric: bool = Field(default=False, description="是否对称属性")
    is_reflexive: bool = Field(default=False, description="是否自反属性")
    
    # 关系属性
    inverse_property: Optional[str] = Field(None, description="逆属性ID")
    sub_properties: Set[str] = Field(default_factory=set, description="子属性ID集合")
    super_properties: Set[str] = Field(default_factory=set, description="父属性ID集合")
    
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)


class Ontology(BaseModel):
    """本体"""
    
    id: str = Field(default_factory=lambda: str(uuid4()))
    name: str = Field(..., description="本体名称")
    description: Optional[str] = Field(None, description="本体描述")
    version: str = Field(default="1.0.0", description="版本号")
    namespace: str = Field(..., description="命名空间")
    
    # 本体组件
    classes: Dict[str, OntologyClass] = Field(default_factory=dict, description="类集合")
    properties: Dict[str, OntologyProperty] = Field(default_factory=dict, description="属性集合")
    individuals: Set[str] = Field(default_factory=set, description="个体ID集合")
    
    # 导入的本体
    imports: Set[str] = Field(default_factory=set, description="导入的本体ID集合")
    
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    
    class Config:
        arbitrary_types_allowed = True
    
    def add_class(self, ontology_class: OntologyClass) -> None:
        """添加类"""
        self.classes[ontology_class.id] = ontology_class
        self.updated_at = datetime.now()
    
    def add_property(self, ontology_property: OntologyProperty) -> None:
        """添加属性"""
        self.properties[ontology_property.id] = ontology_property
        self.updated_at = datetime.now()
    
    def get_class_hierarchy(self, class_id: str) -> Dict[str, Any]:
        """获取类的层次结构"""
        if class_id not in self.classes:
            return {}
        
        ontology_class = self.classes[class_id]
        hierarchy = {
            "id": class_id,
            "name": ontology_class.name,
            "parents": [],
            "children": []
        }
        
        # 获取父类
        for parent_id in ontology_class.parent_classes:
            if parent_id in self.classes:
                parent_hierarchy = self.get_class_hierarchy(parent_id)
                hierarchy["parents"].append(parent_hierarchy)
        
        # 获取子类
        for child_id in ontology_class.child_classes:
            if child_id in self.classes:
                child_hierarchy = self.get_class_hierarchy(child_id)
                hierarchy["children"].append(child_hierarchy)
        
        return hierarchy
    
    def is_subclass_of(self, subclass_id: str, superclass_id: str) -> bool:
        """检查是否为子类关系"""
        if subclass_id == superclass_id:
            return True
        
        if subclass_id not in self.classes:
            return False
        
        subclass = self.classes[subclass_id]
        visited = set()
        queue = list(subclass.parent_classes)
        
        while queue:
            current_id = queue.pop(0)
            if current_id in visited:
                continue
            
            visited.add(current_id)
            
            if current_id == superclass_id:
                return True
            
            if current_id in self.classes:
                current_class = self.classes[current_id]
                queue.extend(current_class.parent_classes)
        
        return False
    
    def get_common_superclass(self, class_id1: str, class_id2: str) -> Optional[str]:
        """获取最近公共父类"""
        if class_id1 not in self.classes or class_id2 not in self.classes:
            return None
        
        # 获取第一个类的所有父类
        ancestors1 = self._get_all_ancestors(class_id1)
        ancestors1.add(class_id1)
        
        # 获取第二个类的所有父类
        ancestors2 = self._get_all_ancestors(class_id2)
        ancestors2.add(class_id2)
        
        # 找到公共祖先
        common_ancestors = ancestors1.intersection(ancestors2)
        
        if not common_ancestors:
            return None
        
        # 找到最具体的公共祖先（最少父类的）
        min_ancestor_count = float('inf')
        closest_ancestor = None
        
        for ancestor_id in common_ancestors:
            ancestor_count = len(self._get_all_ancestors(ancestor_id))
            if ancestor_count < min_ancestor_count:
                min_ancestor_count = ancestor_count
                closest_ancestor = ancestor_id
        
        return closest_ancestor
    
    def _get_all_ancestors(self, class_id: str) -> Set[str]:
        """获取所有祖先类"""
        ancestors = set()
        
        if class_id not in self.classes:
            return ancestors
        
        visited = set()
        queue = list(self.classes[class_id].parent_classes)
        
        while queue:
            current_id = queue.pop(0)
            if current_id in visited:
                continue
            
            visited.add(current_id)
            ancestors.add(current_id)
            
            if current_id in self.classes:
                current_class = self.classes[current_id]
                queue.extend(current_class.parent_classes)
        
        return ancestors


class OntologyManager:
    """本体管理器"""
    
    def __init__(self):
        self.ontologies: Dict[str, Ontology] = {}
        self.namespace_index: Dict[str, str] = {}  # namespace -> ontology_id
    
    def add_ontology(self, ontology: Ontology) -> None:
        """添加本体"""
        self.ontologies[ontology.id] = ontology
        self.namespace_index[ontology.namespace] = ontology.id
    
    def get_ontology(self, ontology_id: str) -> Optional[Ontology]:
        """获取本体"""
        return self.ontologies.get(ontology_id)
    
    def get_ontology_by_namespace(self, namespace: str) -> Optional[Ontology]:
        """根据命名空间获取本体"""
        ontology_id = self.namespace_index.get(namespace)
        if ontology_id:
            return self.ontologies.get(ontology_id)
        return None
    
    def merge_ontologies(self, ontology_ids: List[str], 
                        merged_name: str, merged_namespace: str) -> Optional[Ontology]:
        """合并多个本体"""
        ontologies_to_merge = []
        
        for ontology_id in ontology_ids:
            ontology = self.get_ontology(ontology_id)
            if ontology:
                ontologies_to_merge.append(ontology)
        
        if not ontologies_to_merge:
            return None
        
        # 创建合并后的本体
        merged_ontology = Ontology(
            name=merged_name,
            namespace=merged_namespace,
            description=f"Merged ontology from: {', '.join([o.name for o in ontologies_to_merge])}"
        )
        
        # 合并类
        for ontology in ontologies_to_merge:
            for class_id, ontology_class in ontology.classes.items():
                merged_ontology.add_class(ontology_class)
        
        # 合并属性
        for ontology in ontologies_to_merge:
            for prop_id, ontology_property in ontology.properties.items():
                merged_ontology.add_property(ontology_property)
        
        # 合并个体
        for ontology in ontologies_to_merge:
            merged_ontology.individuals.update(ontology.individuals)
        
        return merged_ontology
    
    def validate_ontology(self, ontology_id: str) -> List[str]:
        """验证本体一致性"""
        ontology = self.get_ontology(ontology_id)
        if not ontology:
            return ["Ontology not found"]
        
        errors = []
        
        # 检查类的循环继承
        for class_id, ontology_class in ontology.classes.items():
            if self._has_circular_inheritance(ontology, class_id):
                errors.append(f"Circular inheritance detected for class: {ontology_class.name}")
        
        # 检查属性的定义域和值域
        for prop_id, ontology_property in ontology.properties.items():
            for domain_class in ontology_property.domain:
                if domain_class not in ontology.classes:
                    errors.append(f"Property {ontology_property.name} references unknown domain class: {domain_class}")
            
            for range_class in ontology_property.range:
                if range_class not in ontology.classes:
                    errors.append(f"Property {ontology_property.name} references unknown range class: {range_class}")
        
        return errors
    
    def _has_circular_inheritance(self, ontology: Ontology, class_id: str) -> bool:
        """检查是否存在循环继承"""
        visited = set()
        stack = [class_id]
        
        while stack:
            current_id = stack.pop()
            
            if current_id in visited:
                return True
            
            visited.add(current_id)
            
            if current_id in ontology.classes:
                current_class = ontology.classes[current_id]
                stack.extend(current_class.parent_classes)
        
        return False
