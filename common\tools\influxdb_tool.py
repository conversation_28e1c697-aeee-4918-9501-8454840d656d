"""
InfluxDB时序数据库工具类
提供通用的InfluxDB操作接口，与业务逻辑解耦
"""

import time
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from influxdb_client import InfluxDBClient, Point, WritePrecision
from influxdb_client.client.write_api import SYNCHRONOUS
from influxdb_client.client.query_api import QueryApi
from influxdb_client.client.exceptions import InfluxDBError

from .database_base import (
    TimeSeriesDatabaseInterface, DatabaseConfig, QueryResult, OperationResult,
    ConnectionStatus, ConnectionException, QueryException, OperationException
)

import logging
logger = logging.getLogger(__name__)


class InfluxDBConfig(DatabaseConfig):
    """InfluxDB配置类"""
    
    def __init__(self, url: str, token: str, org: str, bucket: str,
                 timeout: int = 10000, verify_ssl: bool = True, **kwargs):
        # 解析URL获取host和port
        if "://" in url:
            protocol, address = url.split("://", 1)
            if ":" in address:
                host, port = address.split(":", 1)
                port = int(port)
            else:
                host = address
                port = 8086
        else:
            host = url
            port = 8086
            
        super().__init__(
            host=host,
            port=port,
            database=bucket,  # InfluxDB中bucket相当于database
            **kwargs
        )
        
        self.url = url
        self.token = token
        self.org = org
        self.bucket = bucket
        self.verify_ssl = verify_ssl


class InfluxDBTool(TimeSeriesDatabaseInterface):
    """InfluxDB工具类"""
    
    def __init__(self, config: InfluxDBConfig):
        """
        初始化InfluxDB工具
        
        Args:
            config: InfluxDB配置
        """
        super().__init__(config)
        self.config: InfluxDBConfig = config
        self.client: Optional[InfluxDBClient] = None
        self.write_api = None
        self.query_api = None
        
    def connect(self) -> bool:
        """建立连接"""
        try:
            self.status = ConnectionStatus.CONNECTING
            
            self.client = InfluxDBClient(
                url=self.config.url,
                token=self.config.token,
                org=self.config.org,
                timeout=self.config.timeout,
                verify_ssl=self.config.verify_ssl
            )
            
            # 测试连接
            health = self.client.health()
            if health.status != "pass":
                raise ConnectionException(f"InfluxDB health check failed: {health.message}")
            
            self.write_api = self.client.write_api(write_options=SYNCHRONOUS)
            self.query_api = self.client.query_api()
            
            self.status = ConnectionStatus.CONNECTED
            self.connection_time = datetime.now()
            self.last_error = None
            
            logger.info(f"Connected to InfluxDB at {self.config.url}")
            return True
            
        except Exception as e:
            self.status = ConnectionStatus.ERROR
            self.last_error = str(e)
            logger.error(f"Failed to connect to InfluxDB: {e}")
            return False
    
    def disconnect(self) -> bool:
        """关闭连接"""
        try:
            if self.client:
                self.client.close()
                self.client = None
                self.write_api = None
                self.query_api = None
            
            self.status = ConnectionStatus.DISCONNECTED
            logger.info("Disconnected from InfluxDB")
            return True
            
        except Exception as e:
            logger.error(f"Error disconnecting from InfluxDB: {e}")
            return False
    
    def is_connected(self) -> bool:
        """检查连接状态"""
        return self.status == ConnectionStatus.CONNECTED and self.client is not None
    
    def ping(self) -> bool:
        """测试连接"""
        if not self.is_connected():
            return False
        
        try:
            health = self.client.health()
            return health.status == "pass"
        except Exception:
            return False
    
    def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> QueryResult:
        """执行查询"""
        if not self.is_connected():
            return QueryResult(
                success=False,
                error="Not connected to database"
            )
        
        start_time = time.time()
        
        try:
            # 替换查询中的参数
            if params:
                for key, value in params.items():
                    query = query.replace(f"${key}", str(value))
            
            result = self.query_api.query(org=self.config.org, query=query)
            
            # 转换结果为字典列表
            records = []
            for table in result:
                for record in table.records:
                    record_dict = {
                        'time': record.get_time(),
                        'measurement': record.get_measurement(),
                        'field': record.get_field(),
                        'value': record.get_value()
                    }
                    # 添加标签
                    record_dict.update(record.values)
                    records.append(record_dict)
            
            execution_time = time.time() - start_time
            
            return QueryResult(
                success=True,
                data=records,
                count=len(records),
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Query execution failed: {e}")
            
            return QueryResult(
                success=False,
                error=str(e),
                execution_time=execution_time
            )
    
    def execute_command(self, command: str, params: Optional[Dict[str, Any]] = None) -> OperationResult:
        """执行命令（对于InfluxDB，主要是写入操作）"""
        # InfluxDB的写入操作通过专门的write_api进行
        # 这里主要用于管理操作
        return self.execute_query(command, params)
    
    def write_point(self, measurement: str, tags: Dict[str, str], 
                   fields: Dict[str, Any], timestamp: datetime = None) -> OperationResult:
        """写入数据点"""
        if not self.is_connected():
            return OperationResult(
                success=False,
                error="Not connected to database"
            )
        
        start_time = time.time()
        
        try:
            point = Point(measurement)
            
            # 添加标签
            for tag_key, tag_value in tags.items():
                point = point.tag(tag_key, tag_value)
            
            # 添加字段
            for field_key, field_value in fields.items():
                point = point.field(field_key, field_value)
            
            # 设置时间戳
            if timestamp:
                point = point.time(timestamp, WritePrecision.MS)
            
            self.write_api.write(bucket=self.config.bucket, org=self.config.org, record=point)
            
            execution_time = time.time() - start_time
            
            return OperationResult(
                success=True,
                affected_count=1,
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Write point failed: {e}")
            
            return OperationResult(
                success=False,
                error=str(e),
                execution_time=execution_time
            )
    
    def write_points(self, points: List[Dict[str, Any]]) -> OperationResult:
        """批量写入数据点"""
        if not self.is_connected():
            return OperationResult(
                success=False,
                error="Not connected to database"
            )
        
        start_time = time.time()
        
        try:
            point_objects = []
            
            for point_data in points:
                point = Point(point_data['measurement'])
                
                # 添加标签
                for tag_key, tag_value in point_data.get('tags', {}).items():
                    point = point.tag(tag_key, tag_value)
                
                # 添加字段
                for field_key, field_value in point_data.get('fields', {}).items():
                    point = point.field(field_key, field_value)
                
                # 设置时间戳
                if 'timestamp' in point_data:
                    point = point.time(point_data['timestamp'], WritePrecision.MS)
                
                point_objects.append(point)
            
            self.write_api.write(bucket=self.config.bucket, org=self.config.org, record=point_objects)
            
            execution_time = time.time() - start_time
            
            return OperationResult(
                success=True,
                affected_count=len(points),
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Batch write failed: {e}")
            
            return OperationResult(
                success=False,
                error=str(e),
                execution_time=execution_time
            )
    
    def query_range(self, measurement: str, start_time: datetime, end_time: datetime,
                   tags: Dict[str, str] = None, fields: List[str] = None) -> QueryResult:
        """范围查询"""
        query_parts = [
            f'from(bucket: "{self.config.bucket}")',
            f'|> range(start: {start_time.isoformat()}Z, stop: {end_time.isoformat()}Z)',
            f'|> filter(fn: (r) => r._measurement == "{measurement}")'
        ]
        
        # 添加标签过滤
        if tags:
            for tag_key, tag_value in tags.items():
                query_parts.append(f'|> filter(fn: (r) => r.{tag_key} == "{tag_value}")')
        
        # 添加字段过滤
        if fields:
            field_conditions = " or ".join([f'r._field == "{field}"' for field in fields])
            query_parts.append(f'|> filter(fn: (r) => {field_conditions})')
        
        query_parts.append('|> sort(columns: ["_time"])')
        
        query = " ".join(query_parts)
        return self.execute_query(query)
    
    def aggregate_query(self, measurement: str, start_time: datetime, end_time: datetime,
                       aggregation: str, interval: str, tags: Dict[str, str] = None) -> QueryResult:
        """聚合查询"""
        query_parts = [
            f'from(bucket: "{self.config.bucket}")',
            f'|> range(start: {start_time.isoformat()}Z, stop: {end_time.isoformat()}Z)',
            f'|> filter(fn: (r) => r._measurement == "{measurement}")'
        ]
        
        # 添加标签过滤
        if tags:
            for tag_key, tag_value in tags.items():
                query_parts.append(f'|> filter(fn: (r) => r.{tag_key} == "{tag_value}")')
        
        # 添加聚合
        query_parts.append(f'|> aggregateWindow(every: {interval}, fn: {aggregation}, createEmpty: false)')
        query_parts.append('|> sort(columns: ["_time"])')
        
        query = " ".join(query_parts)
        return self.execute_query(query)
    
    def delete_data(self, measurement: str, start_time: datetime, end_time: datetime,
                   tags: Dict[str, str] = None) -> OperationResult:
        """删除数据"""
        if not self.is_connected():
            return OperationResult(
                success=False,
                error="Not connected to database"
            )
        
        try:
            # 构建删除条件
            predicate = f'_measurement="{measurement}"'
            
            if tags:
                for tag_key, tag_value in tags.items():
                    predicate += f' AND {tag_key}="{tag_value}"'
            
            delete_api = self.client.delete_api()
            delete_api.delete(
                start=start_time,
                stop=end_time,
                predicate=predicate,
                bucket=self.config.bucket,
                org=self.config.org
            )
            
            return OperationResult(success=True)
            
        except Exception as e:
            logger.error(f"Delete data failed: {e}")
            return OperationResult(success=False, error=str(e))
    
    def get_measurements(self) -> List[str]:
        """获取所有measurement"""
        query = f'''
        import "influxdata/influxdb/schema"
        schema.measurements(bucket: "{self.config.bucket}")
        '''
        
        result = self.execute_query(query)
        if result.success:
            return [record.get('_value', '') for record in result.data]
        return []
    
    def get_tag_keys(self, measurement: str) -> List[str]:
        """获取measurement的所有tag键"""
        query = f'''
        import "influxdata/influxdb/schema"
        schema.tagKeys(
            bucket: "{self.config.bucket}",
            predicate: (r) => r._measurement == "{measurement}"
        )
        '''
        
        result = self.execute_query(query)
        if result.success:
            return [record.get('_value', '') for record in result.data]
        return []
    
    def get_field_keys(self, measurement: str) -> List[str]:
        """获取measurement的所有field键"""
        query = f'''
        import "influxdata/influxdb/schema"
        schema.fieldKeys(
            bucket: "{self.config.bucket}",
            predicate: (r) => r._measurement == "{measurement}"
        )
        '''
        
        result = self.execute_query(query)
        if result.success:
            return [record.get('_value', '') for record in result.data]
        return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        base_stats = super().get_statistics()
        
        if self.is_connected():
            try:
                measurements = self.get_measurements()
                
                base_stats.update({
                    "bucket": self.config.bucket,
                    "org": self.config.org,
                    "measurement_count": len(measurements),
                    "measurements": measurements[:10]  # 只显示前10个
                })
            except Exception as e:
                logger.error(f"Error getting InfluxDB statistics: {e}")
        
        return base_stats
