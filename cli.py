"""
AGI Knowledge Graph System - 命令行工具
"""
import typer
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
import asyncio
from pathlib import Path
import sys

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from config import settings
from examples.basic_usage import (
    create_sample_concepts,
    create_sample_entities, 
    create_sample_relationships,
    create_sample_knowledge,
    create_sample_rules,
    create_sample_skills
)

app = typer.Typer(
    name="agi-cli",
    help="AGI Knowledge Graph System 命令行工具",
    add_completion=False
)
console = Console()


@app.command()
def init():
    """初始化系统数据库和目录"""
    console.print("[bold blue]初始化 AGI Knowledge Graph System...[/bold blue]")
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        task = progress.add_task("正在初始化数据库...", total=None)
        
        try:
            # 导入并运行初始化脚本
            from scripts.init_db import main as init_db_main
            init_db_main()
            
            progress.update(task, description="✅ 数据库初始化完成")
            console.print("[bold green]系统初始化成功！[/bold green]")
            
        except Exception as e:
            progress.update(task, description="❌ 初始化失败")
            console.print(f"[bold red]初始化失败: {e}[/bold red]")
            raise typer.Exit(1)


@app.command()
def status():
    """显示系统状态"""
    console.print("[bold blue]AGI Knowledge Graph System 状态[/bold blue]")
    
    # 创建状态表格
    table = Table(title="系统配置")
    table.add_column("配置项", style="cyan", no_wrap=True)
    table.add_column("值", style="magenta")
    
    table.add_row("应用名称", settings.app_name)
    table.add_row("版本", settings.app_version)
    table.add_row("调试模式", str(settings.debug))
    table.add_row("数据库URL", settings.database_url)
    table.add_row("Neo4j URI", settings.neo4j_uri)
    table.add_row("API地址", f"{settings.api_host}:{settings.api_port}")
    table.add_row("日志级别", settings.log_level)
    
    console.print(table)


@app.command()
def demo():
    """运行演示示例"""
    console.print("[bold blue]运行 AGI 系统演示...[/bold blue]")
    
    try:
        # 运行基本使用示例
        from examples.basic_usage import main as demo_main
        demo_main()
        
        console.print("[bold green]演示完成！[/bold green]")
        
    except Exception as e:
        console.print(f"[bold red]演示运行失败: {e}[/bold red]")
        raise typer.Exit(1)


@app.command()
def serve(
    host: str = typer.Option(None, "--host", "-h", help="服务器主机地址"),
    port: int = typer.Option(None, "--port", "-p", help="服务器端口"),
    reload: bool = typer.Option(False, "--reload", "-r", help="启用自动重载")
):
    """启动API服务器"""
    host = host or settings.api_host
    port = port or settings.api_port
    
    console.print(f"[bold blue]启动 AGI API 服务器...[/bold blue]")
    console.print(f"地址: http://{host}:{port}")
    console.print(f"文档: http://{host}:{port}/docs")
    
    try:
        import uvicorn
        uvicorn.run(
            "main:app",
            host=host,
            port=port,
            reload=reload or settings.debug,
            log_level=settings.log_level.lower()
        )
    except KeyboardInterrupt:
        console.print("\n[yellow]服务器已停止[/yellow]")
    except Exception as e:
        console.print(f"[bold red]服务器启动失败: {e}[/bold red]")
        raise typer.Exit(1)


@app.command()
def test():
    """运行测试套件"""
    console.print("[bold blue]运行测试套件...[/bold blue]")
    
    try:
        import pytest
        exit_code = pytest.main([
            "tests/",
            "-v",
            "--tb=short"
        ])
        
        if exit_code == 0:
            console.print("[bold green]所有测试通过！[/bold green]")
        else:
            console.print("[bold red]部分测试失败[/bold red]")
            raise typer.Exit(exit_code)
            
    except ImportError:
        console.print("[bold red]pytest 未安装，请运行: pip install pytest[/bold red]")
        raise typer.Exit(1)
    except Exception as e:
        console.print(f"[bold red]测试运行失败: {e}[/bold red]")
        raise typer.Exit(1)


@app.command()
def create_sample():
    """创建示例数据"""
    console.print("[bold blue]创建示例数据...[/bold blue]")
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        
        try:
            # 创建概念
            task = progress.add_task("创建示例概念...", total=None)
            concepts = create_sample_concepts()
            progress.update(task, description=f"✅ 创建了 {len(concepts)} 个概念")
            
            # 创建实体
            task = progress.add_task("创建示例实体...", total=None)
            entities = create_sample_entities()
            progress.update(task, description=f"✅ 创建了 {len(entities)} 个实体")
            
            # 创建关系
            task = progress.add_task("创建示例关系...", total=None)
            relationships = create_sample_relationships(entities)
            progress.update(task, description=f"✅ 创建了 {len(relationships)} 个关系")
            
            # 创建知识
            task = progress.add_task("创建示例知识...", total=None)
            knowledge_items = create_sample_knowledge()
            progress.update(task, description=f"✅ 创建了 {len(knowledge_items)} 个知识项")
            
            # 创建规则
            task = progress.add_task("创建示例规则...", total=None)
            rules = create_sample_rules()
            progress.update(task, description=f"✅ 创建了 {len(rules)} 个规则")
            
            # 创建技能
            task = progress.add_task("创建示例技能...", total=None)
            skills = create_sample_skills()
            progress.update(task, description=f"✅ 创建了 {len(skills)} 个技能")
            
            console.print("[bold green]示例数据创建完成！[/bold green]")
            
        except Exception as e:
            console.print(f"[bold red]示例数据创建失败: {e}[/bold red]")
            raise typer.Exit(1)


@app.command()
def info():
    """显示系统信息"""
    console.print(Panel.fit(
        f"""[bold cyan]AGI Knowledge Graph System[/bold cyan]

[bold]版本:[/bold] {settings.app_version}
[bold]描述:[/bold] 基于动态知识图谱的人工通用智能工程项目

[bold]核心模块:[/bold]
• 概念和实体层 - 认知系统中的核心抽象概念
• 语义关系和连接层 - 实体间的逻辑关系描述  
• 知识和技能模块 - 事实性、程序性知识库
• 任务与目标层 - 任务描述和层次结构
• 环境和情境感知 - 环境描述和情境模型
• 反馈和学习机制 - 反馈记录和学习算法
• 逻辑与推理层 - 推理规则和决策树
• 数据和隐私保护层 - 数据访问控制

[bold]技术栈:[/bold]
• Python 3.8+, FastAPI, Neo4j, SQLAlchemy
• NetworkX, OpenAI API, Pydantic

[bold]使用方法:[/bold]
• agi-cli init     - 初始化系统
• agi-cli serve    - 启动API服务
• agi-cli demo     - 运行演示
• agi-cli test     - 运行测试
""",
        title="系统信息",
        border_style="blue"
    ))


@app.command()
def config():
    """显示配置信息"""
    console.print("[bold blue]系统配置[/bold blue]")
    
    config_panel = f"""[bold]应用配置:[/bold]
• 应用名称: {settings.app_name}
• 版本: {settings.app_version}
• 调试模式: {settings.debug}

[bold]数据库配置:[/bold]
• 数据库URL: {settings.database_url}
• Neo4j URI: {settings.neo4j_uri}
• Neo4j用户: {settings.neo4j_user}

[bold]API配置:[/bold]
• 主机: {settings.api_host}
• 端口: {settings.api_port}

[bold]日志配置:[/bold]
• 日志级别: {settings.log_level}
• 日志文件: {settings.log_file}

[bold]知识图谱配置:[/bold]
• 最大图深度: {settings.max_graph_depth}
• 相似度阈值: {settings.similarity_threshold}

[bold]学习配置:[/bold]
• 学习率: {settings.learning_rate}
• 反馈权重: {settings.feedback_weight}

[bold]上下文配置:[/bold]
• 上下文窗口大小: {settings.context_window_size}
• 上下文更新间隔: {settings.context_update_interval}秒
"""
    
    console.print(Panel(config_panel, title="配置详情", border_style="green"))


if __name__ == "__main__":
    app()
