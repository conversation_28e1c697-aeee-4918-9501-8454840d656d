"""
MongoDB文档数据库工具类
提供通用的MongoDB操作接口，与业务逻辑解耦
"""

import time
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError, OperationFailure
from bson import ObjectId

from .database_base import (
    DocumentDatabaseInterface, DatabaseConfig, QueryResult, OperationResult,
    ConnectionStatus, ConnectionException, QueryException, OperationException
)

import logging
logger = logging.getLogger(__name__)


class MongoDBConfig(DatabaseConfig):
    """MongoDB配置类"""
    
    def __init__(self, host: str, port: int, database: str, username: str = None, 
                 password: str = None, auth_source: str = "admin", replica_set: str = None,
                 ssl: bool = False, ssl_cert_reqs: str = None, **kwargs):
        super().__init__(
            host=host,
            port=port,
            database=database,
            username=username,
            password=password,
            **kwargs
        )
        
        self.auth_source = auth_source
        self.replica_set = replica_set
        self.ssl = ssl
        self.ssl_cert_reqs = ssl_cert_reqs


class MongoDBTool(DocumentDatabaseInterface):
    """MongoDB工具类"""
    
    def __init__(self, config: MongoDBConfig):
        """
        初始化MongoDB工具
        
        Args:
            config: MongoDB配置
        """
        super().__init__(config)
        self.config: MongoDBConfig = config
        self.client: Optional[MongoClient] = None
        self.db = None
        
    def connect(self) -> bool:
        """建立连接"""
        try:
            self.status = ConnectionStatus.CONNECTING
            
            # 构建连接URI
            if self.config.username and self.config.password:
                uri = f"mongodb://{self.config.username}:{self.config.password}@{self.config.host}:{self.config.port}/{self.config.database}"
            else:
                uri = f"mongodb://{self.config.host}:{self.config.port}/{self.config.database}"
            
            # 连接参数
            connect_params = {
                "serverSelectionTimeoutMS": self.config.timeout * 1000,
                "connectTimeoutMS": self.config.timeout * 1000,
                "socketTimeoutMS": self.config.timeout * 1000,
            }
            
            if self.config.auth_source:
                connect_params["authSource"] = self.config.auth_source
            if self.config.replica_set:
                connect_params["replicaSet"] = self.config.replica_set
            if self.config.ssl:
                connect_params["ssl"] = self.config.ssl
                if self.config.ssl_cert_reqs:
                    connect_params["ssl_cert_reqs"] = self.config.ssl_cert_reqs
            
            self.client = MongoClient(uri, **connect_params)
            
            # 测试连接
            self.client.admin.command('ping')
            
            self.db = self.client[self.config.database]
            
            self.status = ConnectionStatus.CONNECTED
            self.connection_time = datetime.now()
            self.last_error = None
            
            logger.info(f"Connected to MongoDB at {self.config.host}:{self.config.port}")
            return True
            
        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            self.status = ConnectionStatus.ERROR
            self.last_error = str(e)
            logger.error(f"Failed to connect to MongoDB: {e}")
            return False
        except Exception as e:
            self.status = ConnectionStatus.ERROR
            self.last_error = str(e)
            logger.error(f"Unexpected error connecting to MongoDB: {e}")
            return False
    
    def disconnect(self) -> bool:
        """关闭连接"""
        try:
            if self.client:
                self.client.close()
                self.client = None
                self.db = None
            
            self.status = ConnectionStatus.DISCONNECTED
            logger.info("Disconnected from MongoDB")
            return True
            
        except Exception as e:
            logger.error(f"Error disconnecting from MongoDB: {e}")
            return False
    
    def is_connected(self) -> bool:
        """检查连接状态"""
        return (self.status == ConnectionStatus.CONNECTED and 
                self.client is not None and self.db is not None)
    
    def ping(self) -> bool:
        """测试连接"""
        if not self.is_connected():
            return False
        
        try:
            self.client.admin.command('ping')
            return True
        except Exception:
            return False
    
    def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> QueryResult:
        """执行查询（MongoDB没有SQL查询，这里用于兼容接口）"""
        return QueryResult(
            success=False,
            error="MongoDB does not support SQL queries. Use specific document operations."
        )
    
    def execute_command(self, command: str, params: Optional[Dict[str, Any]] = None) -> OperationResult:
        """执行命令"""
        if not self.is_connected():
            return OperationResult(
                success=False,
                error="Not connected to database"
            )
        
        start_time = time.time()
        
        try:
            # 执行数据库命令
            result = self.db.command(command, **(params or {}))
            
            execution_time = time.time() - start_time
            
            return OperationResult(
                success=True,
                execution_time=execution_time,
                metadata=result
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Command execution failed: {e}")
            
            return OperationResult(
                success=False,
                error=str(e),
                execution_time=execution_time
            )
    
    def create_collection(self, collection_name: str, schema: Dict[str, Any] = None) -> OperationResult:
        """创建集合"""
        if not self.is_connected():
            return OperationResult(
                success=False,
                error="Not connected to database"
            )
        
        start_time = time.time()
        
        try:
            # 检查集合是否已存在
            if collection_name in self.db.list_collection_names():
                return OperationResult(
                    success=False,
                    error=f"Collection {collection_name} already exists"
                )
            
            # 创建集合
            collection = self.db.create_collection(collection_name)
            
            # 如果提供了schema，创建验证器
            if schema:
                self.db.command("collMod", collection_name, validator=schema)
            
            execution_time = time.time() - start_time
            
            return OperationResult(
                success=True,
                execution_time=execution_time,
                result_id=collection_name
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Create collection failed: {e}")
            
            return OperationResult(
                success=False,
                error=str(e),
                execution_time=execution_time
            )
    
    def insert_document(self, collection_name: str, document: Dict[str, Any]) -> OperationResult:
        """插入文档"""
        if not self.is_connected():
            return OperationResult(
                success=False,
                error="Not connected to database"
            )
        
        start_time = time.time()
        
        try:
            collection = self.db[collection_name]
            result = collection.insert_one(document)
            
            execution_time = time.time() - start_time
            
            return OperationResult(
                success=True,
                affected_count=1,
                execution_time=execution_time,
                result_id=str(result.inserted_id)
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Insert document failed: {e}")
            
            return OperationResult(
                success=False,
                error=str(e),
                execution_time=execution_time
            )
    
    def insert_documents(self, collection_name: str, documents: List[Dict[str, Any]]) -> OperationResult:
        """批量插入文档"""
        if not self.is_connected():
            return OperationResult(
                success=False,
                error="Not connected to database"
            )
        
        start_time = time.time()
        
        try:
            collection = self.db[collection_name]
            result = collection.insert_many(documents)
            
            execution_time = time.time() - start_time
            
            return OperationResult(
                success=True,
                affected_count=len(result.inserted_ids),
                execution_time=execution_time,
                metadata={"inserted_ids": [str(id) for id in result.inserted_ids]}
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Batch insert failed: {e}")
            
            return OperationResult(
                success=False,
                error=str(e),
                execution_time=execution_time
            )
    
    def find_documents(self, collection_name: str, query: Dict[str, Any] = None,
                      projection: Dict[str, int] = None, limit: int = 100, 
                      skip: int = 0, sort: Dict[str, int] = None) -> QueryResult:
        """查找文档"""
        if not self.is_connected():
            return QueryResult(
                success=False,
                error="Not connected to database"
            )
        
        start_time = time.time()
        
        try:
            collection = self.db[collection_name]
            
            # 构建查询
            cursor = collection.find(query or {}, projection)
            
            if sort:
                cursor = cursor.sort(list(sort.items()))
            
            cursor = cursor.skip(skip).limit(limit)
            
            # 获取结果
            documents = list(cursor)
            
            # 转换ObjectId为字符串
            for doc in documents:
                if '_id' in doc and isinstance(doc['_id'], ObjectId):
                    doc['_id'] = str(doc['_id'])
            
            execution_time = time.time() - start_time
            
            return QueryResult(
                success=True,
                data=documents,
                count=len(documents),
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Find documents failed: {e}")
            
            return QueryResult(
                success=False,
                error=str(e),
                execution_time=execution_time
            )
    
    def update_document(self, collection_name: str, query: Dict[str, Any], 
                       update: Dict[str, Any], upsert: bool = False) -> OperationResult:
        """更新文档"""
        if not self.is_connected():
            return OperationResult(
                success=False,
                error="Not connected to database"
            )
        
        start_time = time.time()
        
        try:
            collection = self.db[collection_name]
            result = collection.update_many(query, update, upsert=upsert)
            
            execution_time = time.time() - start_time
            
            return OperationResult(
                success=True,
                affected_count=result.modified_count,
                execution_time=execution_time,
                metadata={
                    "matched_count": result.matched_count,
                    "modified_count": result.modified_count,
                    "upserted_id": str(result.upserted_id) if result.upserted_id else None
                }
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Update document failed: {e}")
            
            return OperationResult(
                success=False,
                error=str(e),
                execution_time=execution_time
            )
    
    def delete_document(self, collection_name: str, query: Dict[str, Any]) -> OperationResult:
        """删除文档"""
        if not self.is_connected():
            return OperationResult(
                success=False,
                error="Not connected to database"
            )
        
        start_time = time.time()
        
        try:
            collection = self.db[collection_name]
            result = collection.delete_many(query)
            
            execution_time = time.time() - start_time
            
            return OperationResult(
                success=True,
                affected_count=result.deleted_count,
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Delete document failed: {e}")
            
            return OperationResult(
                success=False,
                error=str(e),
                execution_time=execution_time
            )
    
    def aggregate(self, collection_name: str, pipeline: List[Dict[str, Any]]) -> QueryResult:
        """聚合查询"""
        if not self.is_connected():
            return QueryResult(
                success=False,
                error="Not connected to database"
            )
        
        start_time = time.time()
        
        try:
            collection = self.db[collection_name]
            cursor = collection.aggregate(pipeline)
            
            documents = list(cursor)
            
            # 转换ObjectId为字符串
            for doc in documents:
                if '_id' in doc and isinstance(doc['_id'], ObjectId):
                    doc['_id'] = str(doc['_id'])
            
            execution_time = time.time() - start_time
            
            return QueryResult(
                success=True,
                data=documents,
                count=len(documents),
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Aggregation failed: {e}")
            
            return QueryResult(
                success=False,
                error=str(e),
                execution_time=execution_time
            )
    
    def create_index(self, collection_name: str, keys: Dict[str, int], 
                    unique: bool = False, background: bool = True) -> OperationResult:
        """创建索引"""
        if not self.is_connected():
            return OperationResult(
                success=False,
                error="Not connected to database"
            )
        
        try:
            collection = self.db[collection_name]
            index_name = collection.create_index(
                list(keys.items()),
                unique=unique,
                background=background
            )
            
            return OperationResult(
                success=True,
                result_id=index_name
            )
            
        except Exception as e:
            logger.error(f"Create index failed: {e}")
            return OperationResult(success=False, error=str(e))
    
    def drop_collection(self, collection_name: str) -> OperationResult:
        """删除集合"""
        if not self.is_connected():
            return OperationResult(
                success=False,
                error="Not connected to database"
            )
        
        try:
            self.db.drop_collection(collection_name)
            return OperationResult(success=True)
            
        except Exception as e:
            logger.error(f"Drop collection failed: {e}")
            return OperationResult(success=False, error=str(e))
    
    def get_collection_info(self, collection_name: str) -> Dict[str, Any]:
        """获取集合信息"""
        if not self.is_connected():
            return {}
        
        try:
            collection = self.db[collection_name]
            stats = self.db.command("collStats", collection_name)
            
            return {
                "name": collection_name,
                "count": stats.get("count", 0),
                "size": stats.get("size", 0),
                "avgObjSize": stats.get("avgObjSize", 0),
                "storageSize": stats.get("storageSize", 0),
                "indexes": stats.get("nindexes", 0)
            }
            
        except Exception as e:
            logger.error(f"Get collection info failed: {e}")
            return {}
    
    def list_collections(self) -> List[str]:
        """列出所有集合"""
        if not self.is_connected():
            return []
        
        try:
            return self.db.list_collection_names()
        except Exception as e:
            logger.error(f"List collections failed: {e}")
            return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        base_stats = super().get_statistics()
        
        if self.is_connected():
            try:
                # 获取数据库统计
                db_stats = self.db.command("dbStats")
                collections = self.list_collections()
                
                collection_info = {}
                for collection_name in collections[:10]:  # 只获取前10个集合的信息
                    info = self.get_collection_info(collection_name)
                    if info:
                        collection_info[collection_name] = {
                            "count": info.get("count", 0),
                            "size": info.get("size", 0)
                        }
                
                base_stats.update({
                    "database_name": self.config.database,
                    "collection_count": len(collections),
                    "collections": collection_info,
                    "data_size": db_stats.get("dataSize", 0),
                    "storage_size": db_stats.get("storageSize", 0),
                    "index_size": db_stats.get("indexSize", 0)
                })
            except Exception as e:
                logger.error(f"Error getting MongoDB statistics: {e}")
        
        return base_stats
