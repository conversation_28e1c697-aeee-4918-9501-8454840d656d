"""
系统元数据组件 - 基于PostgreSQL关系型数据库
用于保证事务完整性的系统元数据存储和管理
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
import json
import uuid
from datetime import datetime
import psycopg2
from psycopg2.extras import RealDictCurs<PERSON>, Json
from psycopg2.pool import ThreadedConnectionPool
import threading

logger = logging.getLogger(__name__)


@dataclass
class MetadataRecord:
    """元数据记录数据结构"""
    id: str
    metadata_type: str
    entity_id: str
    schema_version: str
    content: Dict[str, Any]
    created_at: datetime
    updated_at: datetime
    created_by: str
    last_modified_by: str
    version: str
    status: str


@dataclass
class SchemaDefinition:
    """模式定义数据结构"""
    schema_id: str
    schema_name: str
    schema_version: str
    schema_content: Dict[str, Any]
    description: str
    created_at: datetime
    is_active: bool


class SystemMetadataManager:
    """系统元数据管理器"""
    
    def __init__(self, host: str, port: int, database: str, 
                 username: str, password: str, min_conn: int = 1, max_conn: int = 20):
        """
        初始化系统元数据管理器
        
        Args:
            host: 数据库主机
            port: 数据库端口
            database: 数据库名称
            username: 用户名
            password: 密码
            min_conn: 最小连接数
            max_conn: 最大连接数
        """
        self.host = host
        self.port = port
        self.database = database
        self.username = username
        self.password = password
        self.min_conn = min_conn
        self.max_conn = max_conn
        self.connection_pool: Optional[ThreadedConnectionPool] = None
        self.lock = threading.Lock()
        
    def connect(self) -> bool:
        """建立数据库连接池"""
        try:
            self.connection_pool = ThreadedConnectionPool(
                minconn=self.min_conn,
                maxconn=self.max_conn,
                host=self.host,
                port=self.port,
                database=self.database,
                user=self.username,
                password=self.password,
                cursor_factory=RealDictCursor
            )
            
            # 测试连接
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
            
            logger.info(f"Successfully connected to PostgreSQL at {self.host}:{self.port}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to PostgreSQL: {e}")
            return False
    
    def disconnect(self):
        """关闭数据库连接池"""
        if self.connection_pool:
            self.connection_pool.closeall()
            logger.info("Disconnected from PostgreSQL")
    
    def get_connection(self):
        """获取数据库连接"""
        if not self.connection_pool:
            raise Exception("Connection pool not initialized")
        return self.connection_pool.getconn()
    
    def put_connection(self, conn):
        """归还数据库连接"""
        if self.connection_pool:
            self.connection_pool.putconn(conn)
    
    def initialize_schema(self) -> bool:
        """初始化数据库模式"""
        try:
            conn = self.get_connection()
            try:
                with conn.cursor() as cursor:
                    # 创建元数据表
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS metadata_records (
                            id VARCHAR(100) PRIMARY KEY,
                            metadata_type VARCHAR(50) NOT NULL,
                            entity_id VARCHAR(100) NOT NULL,
                            schema_version VARCHAR(20) NOT NULL,
                            content JSONB NOT NULL,
                            created_at TIMESTAMP WITH TIME ZONE NOT NULL,
                            updated_at TIMESTAMP WITH TIME ZONE NOT NULL,
                            created_by VARCHAR(100) NOT NULL,
                            last_modified_by VARCHAR(100) NOT NULL,
                            version VARCHAR(20) NOT NULL,
                            status VARCHAR(20) NOT NULL DEFAULT 'active'
                        )
                    """)
                    
                    # 创建模式定义表
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS schema_definitions (
                            schema_id VARCHAR(100) PRIMARY KEY,
                            schema_name VARCHAR(100) NOT NULL,
                            schema_version VARCHAR(20) NOT NULL,
                            schema_content JSONB NOT NULL,
                            description TEXT,
                            created_at TIMESTAMP WITH TIME ZONE NOT NULL,
                            is_active BOOLEAN NOT NULL DEFAULT true,
                            UNIQUE(schema_name, schema_version)
                        )
                    """)
                    
                    # 创建版本历史表
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS version_history (
                            id SERIAL PRIMARY KEY,
                            entity_id VARCHAR(100) NOT NULL,
                            metadata_type VARCHAR(50) NOT NULL,
                            version VARCHAR(20) NOT NULL,
                            content JSONB NOT NULL,
                            change_type VARCHAR(20) NOT NULL,
                            change_description TEXT,
                            changed_by VARCHAR(100) NOT NULL,
                            changed_at TIMESTAMP WITH TIME ZONE NOT NULL
                        )
                    """)
                    
                    # 创建索引
                    cursor.execute("""
                        CREATE INDEX IF NOT EXISTS idx_metadata_type_entity 
                        ON metadata_records(metadata_type, entity_id)
                    """)
                    
                    cursor.execute("""
                        CREATE INDEX IF NOT EXISTS idx_metadata_created_at 
                        ON metadata_records(created_at)
                    """)
                    
                    cursor.execute("""
                        CREATE INDEX IF NOT EXISTS idx_version_history_entity 
                        ON version_history(entity_id, changed_at)
                    """)
                    
                    conn.commit()
                    logger.info("Database schema initialized successfully")
                    return True
                    
            finally:
                self.put_connection(conn)
                
        except Exception as e:
            logger.error(f"Failed to initialize schema: {e}")
            return False
    
    def create_metadata_record(self, record: MetadataRecord) -> bool:
        """
        创建元数据记录
        
        Args:
            record: 元数据记录
            
        Returns:
            是否创建成功
        """
        try:
            conn = self.get_connection()
            try:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        INSERT INTO metadata_records 
                        (id, metadata_type, entity_id, schema_version, content, 
                         created_at, updated_at, created_by, last_modified_by, version, status)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        record.id, record.metadata_type, record.entity_id,
                        record.schema_version, Json(record.content),
                        record.created_at, record.updated_at,
                        record.created_by, record.last_modified_by,
                        record.version, record.status
                    ))
                    
                    # 记录版本历史
                    cursor.execute("""
                        INSERT INTO version_history 
                        (entity_id, metadata_type, version, content, change_type, 
                         change_description, changed_by, changed_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        record.entity_id, record.metadata_type, record.version,
                        Json(record.content), 'create', 'Initial creation',
                        record.created_by, record.created_at
                    ))
                    
                    conn.commit()
                    logger.debug(f"Created metadata record: {record.id}")
                    return True
                    
            finally:
                self.put_connection(conn)
                
        except Exception as e:
            logger.error(f"Failed to create metadata record: {e}")
            return False
    
    def update_metadata_record(self, record_id: str, updates: Dict[str, Any],
                             modified_by: str) -> bool:
        """
        更新元数据记录
        
        Args:
            record_id: 记录ID
            updates: 更新内容
            modified_by: 修改者
            
        Returns:
            是否更新成功
        """
        try:
            conn = self.get_connection()
            try:
                with conn.cursor() as cursor:
                    # 获取当前记录
                    cursor.execute("""
                        SELECT * FROM metadata_records WHERE id = %s
                    """, (record_id,))
                    
                    current_record = cursor.fetchone()
                    if not current_record:
                        logger.error(f"Metadata record not found: {record_id}")
                        return False
                    
                    # 更新内容
                    new_content = dict(current_record['content'])
                    new_content.update(updates.get('content', {}))
                    
                    # 增加版本号
                    current_version = current_record['version']
                    version_parts = current_version.split('.')
                    version_parts[-1] = str(int(version_parts[-1]) + 1)
                    new_version = '.'.join(version_parts)
                    
                    # 更新记录
                    cursor.execute("""
                        UPDATE metadata_records 
                        SET content = %s, updated_at = %s, last_modified_by = %s, version = %s
                        WHERE id = %s
                    """, (
                        Json(new_content), datetime.now(), modified_by, new_version, record_id
                    ))
                    
                    # 记录版本历史
                    cursor.execute("""
                        INSERT INTO version_history 
                        (entity_id, metadata_type, version, content, change_type, 
                         change_description, changed_by, changed_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        current_record['entity_id'], current_record['metadata_type'],
                        new_version, Json(new_content), 'update',
                        updates.get('change_description', 'Record updated'),
                        modified_by, datetime.now()
                    ))
                    
                    conn.commit()
                    logger.debug(f"Updated metadata record: {record_id}")
                    return True
                    
            finally:
                self.put_connection(conn)
                
        except Exception as e:
            logger.error(f"Failed to update metadata record: {e}")
            return False
    
    def get_metadata_record(self, record_id: str) -> Optional[MetadataRecord]:
        """
        获取元数据记录
        
        Args:
            record_id: 记录ID
            
        Returns:
            元数据记录或None
        """
        try:
            conn = self.get_connection()
            try:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        SELECT * FROM metadata_records WHERE id = %s
                    """, (record_id,))
                    
                    row = cursor.fetchone()
                    if row:
                        return MetadataRecord(
                            id=row['id'],
                            metadata_type=row['metadata_type'],
                            entity_id=row['entity_id'],
                            schema_version=row['schema_version'],
                            content=row['content'],
                            created_at=row['created_at'],
                            updated_at=row['updated_at'],
                            created_by=row['created_by'],
                            last_modified_by=row['last_modified_by'],
                            version=row['version'],
                            status=row['status']
                        )
                    return None
                    
            finally:
                self.put_connection(conn)
                
        except Exception as e:
            logger.error(f"Failed to get metadata record: {e}")
            return None
    
    def query_metadata_records(self, filters: Dict[str, Any] = None,
                             limit: int = 100, offset: int = 0) -> List[MetadataRecord]:
        """
        查询元数据记录
        
        Args:
            filters: 过滤条件
            limit: 结果限制
            offset: 偏移量
            
        Returns:
            元数据记录列表
        """
        try:
            conn = self.get_connection()
            try:
                with conn.cursor() as cursor:
                    query = "SELECT * FROM metadata_records"
                    params = []
                    
                    if filters:
                        conditions = []
                        for key, value in filters.items():
                            if key == 'content':
                                # JSON查询
                                for json_key, json_value in value.items():
                                    conditions.append(f"content->>%s = %s")
                                    params.extend([json_key, str(json_value)])
                            else:
                                conditions.append(f"{key} = %s")
                                params.append(value)
                        
                        if conditions:
                            query += " WHERE " + " AND ".join(conditions)
                    
                    query += " ORDER BY created_at DESC LIMIT %s OFFSET %s"
                    params.extend([limit, offset])
                    
                    cursor.execute(query, params)
                    rows = cursor.fetchall()
                    
                    records = []
                    for row in rows:
                        records.append(MetadataRecord(
                            id=row['id'],
                            metadata_type=row['metadata_type'],
                            entity_id=row['entity_id'],
                            schema_version=row['schema_version'],
                            content=row['content'],
                            created_at=row['created_at'],
                            updated_at=row['updated_at'],
                            created_by=row['created_by'],
                            last_modified_by=row['last_modified_by'],
                            version=row['version'],
                            status=row['status']
                        ))
                    
                    logger.info(f"Queried {len(records)} metadata records")
                    return records
                    
            finally:
                self.put_connection(conn)
                
        except Exception as e:
            logger.error(f"Failed to query metadata records: {e}")
            return []
    
    def get_version_history(self, entity_id: str) -> List[Dict[str, Any]]:
        """
        获取版本历史
        
        Args:
            entity_id: 实体ID
            
        Returns:
            版本历史列表
        """
        try:
            conn = self.get_connection()
            try:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        SELECT * FROM version_history 
                        WHERE entity_id = %s 
                        ORDER BY changed_at DESC
                    """, (entity_id,))
                    
                    rows = cursor.fetchall()
                    return [dict(row) for row in rows]
                    
            finally:
                self.put_connection(conn)
                
        except Exception as e:
            logger.error(f"Failed to get version history: {e}")
            return []
    
    def create_schema_definition(self, schema: SchemaDefinition) -> bool:
        """
        创建模式定义
        
        Args:
            schema: 模式定义
            
        Returns:
            是否创建成功
        """
        try:
            conn = self.get_connection()
            try:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        INSERT INTO schema_definitions 
                        (schema_id, schema_name, schema_version, schema_content, 
                         description, created_at, is_active)
                        VALUES (%s, %s, %s, %s, %s, %s, %s)
                    """, (
                        schema.schema_id, schema.schema_name, schema.schema_version,
                        Json(schema.schema_content), schema.description,
                        schema.created_at, schema.is_active
                    ))
                    
                    conn.commit()
                    logger.debug(f"Created schema definition: {schema.schema_id}")
                    return True
                    
            finally:
                self.put_connection(conn)
                
        except Exception as e:
            logger.error(f"Failed to create schema definition: {e}")
            return False
    
    def get_schema_definition(self, schema_name: str, 
                            schema_version: str = None) -> Optional[SchemaDefinition]:
        """
        获取模式定义
        
        Args:
            schema_name: 模式名称
            schema_version: 模式版本（可选，默认获取最新版本）
            
        Returns:
            模式定义或None
        """
        try:
            conn = self.get_connection()
            try:
                with conn.cursor() as cursor:
                    if schema_version:
                        cursor.execute("""
                            SELECT * FROM schema_definitions 
                            WHERE schema_name = %s AND schema_version = %s
                        """, (schema_name, schema_version))
                    else:
                        cursor.execute("""
                            SELECT * FROM schema_definitions 
                            WHERE schema_name = %s AND is_active = true
                            ORDER BY created_at DESC LIMIT 1
                        """, (schema_name,))
                    
                    row = cursor.fetchone()
                    if row:
                        return SchemaDefinition(
                            schema_id=row['schema_id'],
                            schema_name=row['schema_name'],
                            schema_version=row['schema_version'],
                            schema_content=row['schema_content'],
                            description=row['description'],
                            created_at=row['created_at'],
                            is_active=row['is_active']
                        )
                    return None
                    
            finally:
                self.put_connection(conn)
                
        except Exception as e:
            logger.error(f"Failed to get schema definition: {e}")
            return None
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            conn = self.get_connection()
            try:
                with conn.cursor() as cursor:
                    # 元数据记录统计
                    cursor.execute("""
                        SELECT metadata_type, COUNT(*) as count 
                        FROM metadata_records 
                        WHERE status = 'active'
                        GROUP BY metadata_type
                    """)
                    metadata_stats = dict(cursor.fetchall())
                    
                    # 模式定义统计
                    cursor.execute("""
                        SELECT COUNT(*) as total_schemas,
                               COUNT(CASE WHEN is_active THEN 1 END) as active_schemas
                        FROM schema_definitions
                    """)
                    schema_stats = cursor.fetchone()
                    
                    # 版本历史统计
                    cursor.execute("""
                        SELECT change_type, COUNT(*) as count 
                        FROM version_history 
                        GROUP BY change_type
                    """)
                    version_stats = dict(cursor.fetchall())
                    
                    return {
                        'metadata_records': metadata_stats,
                        'schema_definitions': dict(schema_stats),
                        'version_history': version_stats
                    }
                    
            finally:
                self.put_connection(conn)
                
        except Exception as e:
            logger.error(f"Failed to get statistics: {e}")
            return {}


class SystemMetadataService:
    """系统元数据服务类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化系统元数据服务
        
        Args:
            config: 配置信息，包含PostgreSQL连接参数
        """
        self.config = config
        self.manager = SystemMetadataManager(
            host=config['postgresql']['host'],
            port=config['postgresql']['port'],
            database=config['postgresql']['database'],
            username=config['postgresql']['username'],
            password=config['postgresql']['password'],
            min_conn=config['postgresql'].get('min_conn', 1),
            max_conn=config['postgresql'].get('max_conn', 20)
        )
        
    def initialize(self) -> bool:
        """初始化服务"""
        if not self.manager.connect():
            return False
        return self.manager.initialize_schema()
    
    def shutdown(self):
        """关闭服务"""
        self.manager.disconnect()
    
    def store_metadata(self, metadata: Dict[str, Any]) -> bool:
        """
        存储元数据
        
        Args:
            metadata: 元数据内容
            
        Returns:
            是否存储成功
        """
        record = MetadataRecord(
            id=metadata.get('id', str(uuid.uuid4())),
            metadata_type=metadata['metadata_type'],
            entity_id=metadata['entity_id'],
            schema_version=metadata['schema_version'],
            content=metadata['content'],
            created_at=datetime.now(),
            updated_at=datetime.now(),
            created_by=metadata['created_by'],
            last_modified_by=metadata['created_by'],
            version=metadata.get('version', '1.0.0'),
            status=metadata.get('status', 'active')
        )
        
        return self.manager.create_metadata_record(record)
    
    def get_metadata(self, record_id: str) -> Optional[Dict[str, Any]]:
        """
        获取元数据
        
        Args:
            record_id: 记录ID
            
        Returns:
            元数据内容或None
        """
        record = self.manager.get_metadata_record(record_id)
        if record:
            return asdict(record)
        return None
    
    def update_metadata(self, record_id: str, updates: Dict[str, Any],
                       modified_by: str) -> bool:
        """
        更新元数据
        
        Args:
            record_id: 记录ID
            updates: 更新内容
            modified_by: 修改者
            
        Returns:
            是否更新成功
        """
        return self.manager.update_metadata_record(record_id, updates, modified_by)
    
    def query_metadata(self, query: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        查询元数据
        
        Args:
            query: 查询条件
            
        Returns:
            查询结果
        """
        records = self.manager.query_metadata_records(
            filters=query.get('filters'),
            limit=query.get('limit', 100),
            offset=query.get('offset', 0)
        )
        
        return [asdict(record) for record in records]
    
    def get_version_history(self, entity_id: str) -> List[Dict[str, Any]]:
        """获取版本历史"""
        return self.manager.get_version_history(entity_id)
    
    def register_schema(self, schema_data: Dict[str, Any]) -> bool:
        """
        注册模式定义
        
        Args:
            schema_data: 模式数据
            
        Returns:
            是否注册成功
        """
        schema = SchemaDefinition(
            schema_id=schema_data.get('schema_id', str(uuid.uuid4())),
            schema_name=schema_data['schema_name'],
            schema_version=schema_data['schema_version'],
            schema_content=schema_data['schema_content'],
            description=schema_data.get('description', ''),
            created_at=datetime.now(),
            is_active=schema_data.get('is_active', True)
        )
        
        return self.manager.create_schema_definition(schema)
    
    def get_schema(self, schema_name: str, schema_version: str = None) -> Optional[Dict[str, Any]]:
        """获取模式定义"""
        schema = self.manager.get_schema_definition(schema_name, schema_version)
        if schema:
            return asdict(schema)
        return None
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.manager.get_statistics()
