"""
AGI Knowledge Graph System - 主入口文件
"""
import asyncio
import uvicorn
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from loguru import logger
import sys

from config import settings
from core.semantic.semantic_network import SemanticNetwork
from core.knowledge.knowledge_base import KnowledgeBase
from core.knowledge.rule_engine import RuleEngine
from core.knowledge.skill_library import SkillLibrary


# 全局实例
semantic_network = SemanticNetwork()
knowledge_base = KnowledgeBase()
rule_engine = RuleEngine()
skill_library = SkillLibrary()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    logger.info("Starting AGI Knowledge Graph System...")
    
    # 配置日志
    logger.remove()
    logger.add(
        sys.stderr,
        level=settings.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    if settings.log_file:
        logger.add(
            settings.log_file,
            level=settings.log_level,
            rotation="10 MB",
            retention="30 days",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}"
        )
    
    logger.info(f"Application started with settings: {settings.app_name} v{settings.app_version}")
    
    yield
    
    # 关闭时清理
    logger.info("Shutting down AGI Knowledge Graph System...")


# 创建FastAPI应用
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="一个基于动态知识图谱的人工通用智能(AGI)工程项目",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": f"Welcome to {settings.app_name}",
        "version": settings.app_version,
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": "2024-01-01T00:00:00Z",
        "components": {
            "semantic_network": "operational",
            "knowledge_base": "operational", 
            "rule_engine": "operational",
            "skill_library": "operational"
        }
    }


@app.get("/stats")
async def get_system_stats():
    """获取系统统计信息"""
    try:
        return {
            "knowledge_graph": semantic_network.get_knowledge_summary(),
            "knowledge_base": knowledge_base.get_knowledge_statistics(),
            "rule_engine": rule_engine.get_execution_statistics(),
            "skill_library": skill_library.get_library_statistics()
        }
    except Exception as e:
        logger.error(f"Error getting system stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/query")
async def process_query(query: dict):
    """处理用户查询"""
    try:
        user_question = query.get("question", "")
        context = query.get("context", {})
        
        if not user_question:
            raise HTTPException(status_code=400, detail="Question is required")
        
        logger.info(f"Processing query: {user_question}")
        
        # 这里实现查询处理逻辑
        # 1. 分析用户问题
        # 2. 从知识图谱中检索相关信息
        # 3. 应用规则进行推理
        # 4. 生成回答
        
        response = {
            "question": user_question,
            "answer": "This is a placeholder response. The actual AGI processing logic will be implemented here.",
            "confidence": 0.8,
            "sources": [],
            "reasoning_path": [],
            "timestamp": "2024-01-01T00:00:00Z"
        }
        
        return response
        
    except Exception as e:
        logger.error(f"Error processing query: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/knowledge/{knowledge_id}")
async def get_knowledge_item(knowledge_id: str):
    """获取知识项"""
    try:
        knowledge_item = knowledge_base.get_knowledge(knowledge_id)
        if not knowledge_item:
            raise HTTPException(status_code=404, detail="Knowledge item not found")
        
        return knowledge_item.to_dict()
        
    except Exception as e:
        logger.error(f"Error getting knowledge item: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/skills/{skill_id}")
async def get_skill(skill_id: str):
    """获取技能"""
    try:
        skill = skill_library.get_skill(skill_id)
        if not skill:
            raise HTTPException(status_code=404, detail="Skill not found")
        
        return skill.model_dump()
        
    except Exception as e:
        logger.error(f"Error getting skill: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/skills/{skill_id}/execute")
async def execute_skill(skill_id: str, context: dict):
    """执行技能"""
    try:
        skill = skill_library.get_skill(skill_id)
        if not skill:
            raise HTTPException(status_code=404, detail="Skill not found")
        
        result = skill.execute(context)
        return result
        
    except Exception as e:
        logger.error(f"Error executing skill: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/rules/execute")
async def execute_rules(request: dict):
    """执行规则"""
    try:
        context = request.get("context", {})
        rule_type = request.get("rule_type")
        category = request.get("category")
        
        results = rule_engine.execute_rules(
            context=context,
            rule_type=rule_type,
            category=category
        )
        
        return {
            "results": results,
            "execution_count": len(results)
        }
        
    except Exception as e:
        logger.error(f"Error executing rules: {e}")
        raise HTTPException(status_code=500, detail=str(e))


def main():
    """主函数"""
    logger.info(f"Starting {settings.app_name} on {settings.api_host}:{settings.api_port}")
    
    uvicorn.run(
        "main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )


if __name__ == "__main__":
    main()
