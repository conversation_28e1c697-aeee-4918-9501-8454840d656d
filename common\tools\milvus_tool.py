"""
Milvus向量数据库工具类
提供通用的Milvus操作接口，与业务逻辑解耦
"""

import time
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from pymilvus import (
    connections, Collection, CollectionSchema, FieldSchema, DataType,
    utility, Index, MilvusException
)

from .database_base import (
    VectorDatabaseInterface, DatabaseConfig, QueryResult, OperationResult,
    ConnectionStatus, ConnectionException, QueryException, OperationException
)

import logging
logger = logging.getLogger(__name__)


class MilvusConfig(DatabaseConfig):
    """Milvus配置类"""
    
    def __init__(self, host: str = "localhost", port: int = 19530, 
                 alias: str = "default", user: str = None, password: str = None,
                 secure: bool = False, **kwargs):
        super().__init__(
            host=host,
            port=port,
            database="",  # Milvus没有database概念
            username=user,
            password=password,
            **kwargs
        )
        
        self.alias = alias
        self.secure = secure


class MilvusTool(VectorDatabaseInterface):
    """Milvus工具类"""
    
    def __init__(self, config: MilvusConfig):
        """
        初始化Milvus工具
        
        Args:
            config: Milvus配置
        """
        super().__init__(config)
        self.config: MilvusConfig = config
        self.collections: Dict[str, Collection] = {}
        
    def connect(self) -> bool:
        """建立连接"""
        try:
            self.status = ConnectionStatus.CONNECTING
            
            connect_params = {
                "alias": self.config.alias,
                "host": self.config.host,
                "port": str(self.config.port)
            }
            
            if self.config.username:
                connect_params["user"] = self.config.username
            if self.config.password:
                connect_params["password"] = self.config.password
            if self.config.secure:
                connect_params["secure"] = self.config.secure
            
            connections.connect(**connect_params)
            
            # 测试连接
            utility.get_server_version(using=self.config.alias)
            
            self.status = ConnectionStatus.CONNECTED
            self.connection_time = datetime.now()
            self.last_error = None
            
            logger.info(f"Connected to Milvus at {self.config.host}:{self.config.port}")
            return True
            
        except Exception as e:
            self.status = ConnectionStatus.ERROR
            self.last_error = str(e)
            logger.error(f"Failed to connect to Milvus: {e}")
            return False
    
    def disconnect(self) -> bool:
        """关闭连接"""
        try:
            connections.disconnect(alias=self.config.alias)
            self.collections.clear()
            
            self.status = ConnectionStatus.DISCONNECTED
            logger.info("Disconnected from Milvus")
            return True
            
        except Exception as e:
            logger.error(f"Error disconnecting from Milvus: {e}")
            return False
    
    def is_connected(self) -> bool:
        """检查连接状态"""
        try:
            return (self.status == ConnectionStatus.CONNECTED and 
                   connections.has_connection(self.config.alias))
        except:
            return False
    
    def ping(self) -> bool:
        """测试连接"""
        if not self.is_connected():
            return False
        
        try:
            utility.get_server_version(using=self.config.alias)
            return True
        except Exception:
            return False
    
    def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> QueryResult:
        """执行查询（Milvus没有SQL查询，这里用于兼容接口）"""
        return QueryResult(
            success=False,
            error="Milvus does not support SQL queries. Use specific vector operations."
        )
    
    def execute_command(self, command: str, params: Optional[Dict[str, Any]] = None) -> OperationResult:
        """执行命令（Milvus没有SQL命令，这里用于兼容接口）"""
        return OperationResult(
            success=False,
            error="Milvus does not support SQL commands. Use specific vector operations."
        )
    
    def create_collection(self, collection_name: str, dimension: int, 
                         index_type: str = "IVF_FLAT", metric_type: str = "L2") -> OperationResult:
        """创建集合"""
        if not self.is_connected():
            return OperationResult(
                success=False,
                error="Not connected to database"
            )
        
        start_time = time.time()
        
        try:
            # 检查集合是否已存在
            if utility.has_collection(collection_name, using=self.config.alias):
                return OperationResult(
                    success=False,
                    error=f"Collection {collection_name} already exists"
                )
            
            # 定义字段
            fields = [
                FieldSchema(name="id", dtype=DataType.VARCHAR, max_length=100, is_primary=True),
                FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=dimension),
                FieldSchema(name="metadata", dtype=DataType.VARCHAR, max_length=10000)
            ]
            
            # 创建集合模式
            schema = CollectionSchema(
                fields=fields,
                description=f"Vector collection with {dimension} dimensions"
            )
            
            # 创建集合
            collection = Collection(
                name=collection_name,
                schema=schema,
                using=self.config.alias
            )
            
            # 创建索引
            index_params = {
                "index_type": index_type,
                "metric_type": metric_type,
                "params": {"nlist": 1024}
            }
            
            collection.create_index(
                field_name="vector",
                index_params=index_params
            )
            
            self.collections[collection_name] = collection
            
            execution_time = time.time() - start_time
            
            return OperationResult(
                success=True,
                execution_time=execution_time,
                result_id=collection_name
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Create collection failed: {e}")
            
            return OperationResult(
                success=False,
                error=str(e),
                execution_time=execution_time
            )
    
    def insert_vectors(self, collection_name: str, vectors: List[List[float]], 
                      ids: List[str] = None, metadata: List[Dict[str, Any]] = None) -> OperationResult:
        """插入向量"""
        if not self.is_connected():
            return OperationResult(
                success=False,
                error="Not connected to database"
            )
        
        start_time = time.time()
        
        try:
            # 获取或创建集合
            if collection_name not in self.collections:
                if not utility.has_collection(collection_name, using=self.config.alias):
                    return OperationResult(
                        success=False,
                        error=f"Collection {collection_name} does not exist"
                    )
                self.collections[collection_name] = Collection(collection_name, using=self.config.alias)
            
            collection = self.collections[collection_name]
            
            # 准备数据
            if ids is None:
                ids = [str(i) for i in range(len(vectors))]
            
            if metadata is None:
                metadata = ["{}" for _ in range(len(vectors))]
            else:
                metadata = [str(meta) if isinstance(meta, dict) else meta for meta in metadata]
            
            data = [ids, vectors, metadata]
            
            # 插入数据
            mr = collection.insert(data)
            collection.flush()
            
            execution_time = time.time() - start_time
            
            return OperationResult(
                success=True,
                affected_count=len(vectors),
                execution_time=execution_time,
                metadata={"insert_count": mr.insert_count, "primary_keys": mr.primary_keys}
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Insert vectors failed: {e}")
            
            return OperationResult(
                success=False,
                error=str(e),
                execution_time=execution_time
            )
    
    def search_vectors(self, collection_name: str, query_vectors: List[List[float]], 
                      top_k: int = 10, filters: Dict[str, Any] = None) -> QueryResult:
        """搜索向量"""
        if not self.is_connected():
            return QueryResult(
                success=False,
                error="Not connected to database"
            )
        
        start_time = time.time()
        
        try:
            # 获取集合
            if collection_name not in self.collections:
                if not utility.has_collection(collection_name, using=self.config.alias):
                    return QueryResult(
                        success=False,
                        error=f"Collection {collection_name} does not exist"
                    )
                self.collections[collection_name] = Collection(collection_name, using=self.config.alias)
            
            collection = self.collections[collection_name]
            collection.load()
            
            # 搜索参数
            search_params = {"metric_type": "L2", "params": {"nprobe": 10}}
            
            # 构建过滤表达式
            expr = None
            if filters:
                expr_parts = []
                for key, value in filters.items():
                    if isinstance(value, str):
                        expr_parts.append(f'{key} == "{value}"')
                    else:
                        expr_parts.append(f'{key} == {value}')
                expr = " and ".join(expr_parts) if expr_parts else None
            
            # 执行搜索
            results = collection.search(
                data=query_vectors,
                anns_field="vector",
                param=search_params,
                limit=top_k,
                expr=expr,
                output_fields=["metadata"]
            )
            
            # 转换结果
            search_results = []
            for hits in results:
                for hit in hits:
                    search_results.append({
                        "id": hit.id,
                        "distance": hit.distance,
                        "score": 1.0 - hit.distance,  # 转换为相似度分数
                        "metadata": hit.entity.get("metadata", "{}")
                    })
            
            execution_time = time.time() - start_time
            
            return QueryResult(
                success=True,
                data=search_results,
                count=len(search_results),
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Search vectors failed: {e}")
            
            return QueryResult(
                success=False,
                error=str(e),
                execution_time=execution_time
            )
    
    def delete_vectors(self, collection_name: str, ids: List[str]) -> OperationResult:
        """删除向量"""
        if not self.is_connected():
            return OperationResult(
                success=False,
                error="Not connected to database"
            )
        
        start_time = time.time()
        
        try:
            # 获取集合
            if collection_name not in self.collections:
                if not utility.has_collection(collection_name, using=self.config.alias):
                    return OperationResult(
                        success=False,
                        error=f"Collection {collection_name} does not exist"
                    )
                self.collections[collection_name] = Collection(collection_name, using=self.config.alias)
            
            collection = self.collections[collection_name]
            
            # 构建删除表达式
            ids_str = '", "'.join(ids)
            expr = f'id in ["{ids_str}"]'
            
            # 删除数据
            collection.delete(expr)
            collection.flush()
            
            execution_time = time.time() - start_time
            
            return OperationResult(
                success=True,
                affected_count=len(ids),
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Delete vectors failed: {e}")
            
            return OperationResult(
                success=False,
                error=str(e),
                execution_time=execution_time
            )
    
    def drop_collection(self, collection_name: str) -> OperationResult:
        """删除集合"""
        if not self.is_connected():
            return OperationResult(
                success=False,
                error="Not connected to database"
            )
        
        try:
            if utility.has_collection(collection_name, using=self.config.alias):
                utility.drop_collection(collection_name, using=self.config.alias)
                if collection_name in self.collections:
                    del self.collections[collection_name]
                
                return OperationResult(success=True)
            else:
                return OperationResult(
                    success=False,
                    error=f"Collection {collection_name} does not exist"
                )
                
        except Exception as e:
            logger.error(f"Drop collection failed: {e}")
            return OperationResult(success=False, error=str(e))
    
    def get_collection_info(self, collection_name: str) -> Dict[str, Any]:
        """获取集合信息"""
        if not self.is_connected():
            return {}
        
        try:
            if not utility.has_collection(collection_name, using=self.config.alias):
                return {}
            
            if collection_name not in self.collections:
                self.collections[collection_name] = Collection(collection_name, using=self.config.alias)
            
            collection = self.collections[collection_name]
            
            return {
                "name": collection_name,
                "num_entities": collection.num_entities,
                "description": collection.description,
                "schema": {
                    "fields": [
                        {
                            "name": field.name,
                            "type": str(field.dtype),
                            "is_primary": field.is_primary
                        }
                        for field in collection.schema.fields
                    ]
                }
            }
            
        except Exception as e:
            logger.error(f"Get collection info failed: {e}")
            return {}
    
    def list_collections(self) -> List[str]:
        """列出所有集合"""
        if not self.is_connected():
            return []
        
        try:
            return utility.list_collections(using=self.config.alias)
        except Exception as e:
            logger.error(f"List collections failed: {e}")
            return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        base_stats = super().get_statistics()
        
        if self.is_connected():
            try:
                collections = self.list_collections()
                collection_info = {}
                
                for collection_name in collections[:5]:  # 只获取前5个集合的信息
                    info = self.get_collection_info(collection_name)
                    if info:
                        collection_info[collection_name] = {
                            "num_entities": info.get("num_entities", 0),
                            "description": info.get("description", "")
                        }
                
                base_stats.update({
                    "collection_count": len(collections),
                    "collections": collection_info,
                    "server_version": utility.get_server_version(using=self.config.alias)
                })
            except Exception as e:
                logger.error(f"Error getting Milvus statistics: {e}")
        
        return base_stats
