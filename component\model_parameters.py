"""
模型参数组件 - 基于Milvus向量数据库
用于快速检索相似案例和模型参数的向量化存储
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import numpy as np
import json
from datetime import datetime
from pymilvus import (
    connections, Collection, CollectionSchema, FieldSchema, DataType,
    utility, Index
)

logger = logging.getLogger(__name__)


@dataclass
class ModelVector:
    """模型向量数据结构"""
    id: str
    model_id: str
    model_type: str
    vector: List[float]
    parameters: Dict[str, Any]
    metadata: Dict[str, Any]
    created_at: datetime


@dataclass
class SimilarityResult:
    """相似性搜索结果"""
    id: str
    model_id: str
    similarity_score: float
    parameters: Dict[str, Any]
    metadata: Dict[str, Any]


class ModelParameterManager:
    """模型参数管理器"""
    
    def __init__(self, host: str = "localhost", port: str = "19530", 
                 alias: str = "default"):
        """
        初始化模型参数管理器
        
        Args:
            host: Milvus服务器主机
            port: Milvus服务器端口
            alias: 连接别名
        """
        self.host = host
        self.port = port
        self.alias = alias
        self.collections = {}
        
    def connect(self) -> bool:
        """建立数据库连接"""
        try:
            connections.connect(
                alias=self.alias,
                host=self.host,
                port=self.port
            )
            logger.info(f"Successfully connected to Milvus at {self.host}:{self.port}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to Milvus: {e}")
            return False
    
    def disconnect(self):
        """关闭数据库连接"""
        try:
            connections.disconnect(alias=self.alias)
            logger.info("Disconnected from Milvus")
        except Exception as e:
            logger.error(f"Error disconnecting from Milvus: {e}")
    
    def create_collection(self, collection_name: str, vector_dim: int, 
                         description: str = "") -> bool:
        """
        创建集合
        
        Args:
            collection_name: 集合名称
            vector_dim: 向量维度
            description: 集合描述
            
        Returns:
            是否创建成功
        """
        try:
            # 检查集合是否已存在
            if utility.has_collection(collection_name):
                logger.info(f"Collection {collection_name} already exists")
                self.collections[collection_name] = Collection(collection_name)
                return True
            
            # 定义字段
            fields = [
                FieldSchema(name="id", dtype=DataType.VARCHAR, max_length=100, is_primary=True),
                FieldSchema(name="model_id", dtype=DataType.VARCHAR, max_length=100),
                FieldSchema(name="model_type", dtype=DataType.VARCHAR, max_length=50),
                FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=vector_dim),
                FieldSchema(name="parameters", dtype=DataType.VARCHAR, max_length=10000),
                FieldSchema(name="metadata", dtype=DataType.VARCHAR, max_length=5000),
                FieldSchema(name="created_at", dtype=DataType.VARCHAR, max_length=50)
            ]
            
            # 创建集合模式
            schema = CollectionSchema(
                fields=fields,
                description=description
            )
            
            # 创建集合
            collection = Collection(
                name=collection_name,
                schema=schema,
                using=self.alias
            )
            
            self.collections[collection_name] = collection
            logger.info(f"Created collection: {collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create collection {collection_name}: {e}")
            return False
    
    def create_index(self, collection_name: str, field_name: str = "vector",
                    index_type: str = "IVF_FLAT", metric_type: str = "L2",
                    params: Dict[str, Any] = None) -> bool:
        """
        创建索引
        
        Args:
            collection_name: 集合名称
            field_name: 字段名称
            index_type: 索引类型
            metric_type: 距离度量类型
            params: 索引参数
            
        Returns:
            是否创建成功
        """
        try:
            if collection_name not in self.collections:
                logger.error(f"Collection {collection_name} not found")
                return False
            
            collection = self.collections[collection_name]
            
            if params is None:
                params = {"nlist": 1024}
            
            index_params = {
                "index_type": index_type,
                "metric_type": metric_type,
                "params": params
            }
            
            collection.create_index(
                field_name=field_name,
                index_params=index_params
            )
            
            logger.info(f"Created index for {collection_name}.{field_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create index: {e}")
            return False
    
    def insert_model_vector(self, collection_name: str, model_vector: ModelVector) -> bool:
        """
        插入模型向量
        
        Args:
            collection_name: 集合名称
            model_vector: 模型向量数据
            
        Returns:
            是否插入成功
        """
        try:
            if collection_name not in self.collections:
                logger.error(f"Collection {collection_name} not found")
                return False
            
            collection = self.collections[collection_name]
            
            data = [
                [model_vector.id],
                [model_vector.model_id],
                [model_vector.model_type],
                [model_vector.vector],
                [json.dumps(model_vector.parameters)],
                [json.dumps(model_vector.metadata)],
                [model_vector.created_at.isoformat()]
            ]
            
            collection.insert(data)
            collection.flush()
            
            logger.debug(f"Inserted model vector: {model_vector.id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to insert model vector: {e}")
            return False
    
    def batch_insert_model_vectors(self, collection_name: str, 
                                  model_vectors: List[ModelVector]) -> bool:
        """
        批量插入模型向量
        
        Args:
            collection_name: 集合名称
            model_vectors: 模型向量列表
            
        Returns:
            是否插入成功
        """
        try:
            if collection_name not in self.collections:
                logger.error(f"Collection {collection_name} not found")
                return False
            
            collection = self.collections[collection_name]
            
            ids = [mv.id for mv in model_vectors]
            model_ids = [mv.model_id for mv in model_vectors]
            model_types = [mv.model_type for mv in model_vectors]
            vectors = [mv.vector for mv in model_vectors]
            parameters = [json.dumps(mv.parameters) for mv in model_vectors]
            metadata = [json.dumps(mv.metadata) for mv in model_vectors]
            created_ats = [mv.created_at.isoformat() for mv in model_vectors]
            
            data = [ids, model_ids, model_types, vectors, parameters, metadata, created_ats]
            
            collection.insert(data)
            collection.flush()
            
            logger.info(f"Batch inserted {len(model_vectors)} model vectors")
            return True
            
        except Exception as e:
            logger.error(f"Failed to batch insert model vectors: {e}")
            return False
    
    def search_similar_models(self, collection_name: str, query_vector: List[float],
                            top_k: int = 10, search_params: Dict[str, Any] = None,
                            expr: str = None) -> List[SimilarityResult]:
        """
        搜索相似模型
        
        Args:
            collection_name: 集合名称
            query_vector: 查询向量
            top_k: 返回结果数量
            search_params: 搜索参数
            expr: 过滤表达式
            
        Returns:
            相似性搜索结果列表
        """
        try:
            if collection_name not in self.collections:
                logger.error(f"Collection {collection_name} not found")
                return []
            
            collection = self.collections[collection_name]
            collection.load()
            
            if search_params is None:
                search_params = {"nprobe": 10}
            
            results = collection.search(
                data=[query_vector],
                anns_field="vector",
                param=search_params,
                limit=top_k,
                expr=expr,
                output_fields=["model_id", "model_type", "parameters", "metadata"]
            )
            
            similarity_results = []
            for hits in results:
                for hit in hits:
                    similarity_results.append(SimilarityResult(
                        id=hit.id,
                        model_id=hit.entity.get("model_id"),
                        similarity_score=1.0 - hit.distance,  # 转换为相似度分数
                        parameters=json.loads(hit.entity.get("parameters", "{}")),
                        metadata=json.loads(hit.entity.get("metadata", "{}"))
                    ))
            
            logger.info(f"Found {len(similarity_results)} similar models")
            return similarity_results
            
        except Exception as e:
            logger.error(f"Failed to search similar models: {e}")
            return []
    
    def get_model_by_id(self, collection_name: str, model_id: str) -> Optional[ModelVector]:
        """
        根据ID获取模型
        
        Args:
            collection_name: 集合名称
            model_id: 模型ID
            
        Returns:
            模型向量数据或None
        """
        try:
            if collection_name not in self.collections:
                logger.error(f"Collection {collection_name} not found")
                return None
            
            collection = self.collections[collection_name]
            collection.load()
            
            expr = f'model_id == "{model_id}"'
            results = collection.query(
                expr=expr,
                output_fields=["id", "model_id", "model_type", "vector", 
                              "parameters", "metadata", "created_at"]
            )
            
            if results:
                result = results[0]
                return ModelVector(
                    id=result["id"],
                    model_id=result["model_id"],
                    model_type=result["model_type"],
                    vector=result["vector"],
                    parameters=json.loads(result["parameters"]),
                    metadata=json.loads(result["metadata"]),
                    created_at=datetime.fromisoformat(result["created_at"])
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get model by ID: {e}")
            return None
    
    def delete_model(self, collection_name: str, model_id: str) -> bool:
        """
        删除模型
        
        Args:
            collection_name: 集合名称
            model_id: 模型ID
            
        Returns:
            是否删除成功
        """
        try:
            if collection_name not in self.collections:
                logger.error(f"Collection {collection_name} not found")
                return False
            
            collection = self.collections[collection_name]
            
            expr = f'model_id == "{model_id}"'
            collection.delete(expr)
            collection.flush()
            
            logger.info(f"Deleted model: {model_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete model: {e}")
            return False
    
    def get_collection_stats(self, collection_name: str) -> Dict[str, Any]:
        """
        获取集合统计信息
        
        Args:
            collection_name: 集合名称
            
        Returns:
            统计信息
        """
        try:
            if collection_name not in self.collections:
                logger.error(f"Collection {collection_name} not found")
                return {}
            
            collection = self.collections[collection_name]
            
            stats = {
                "num_entities": collection.num_entities,
                "description": collection.description,
                "schema": {
                    "fields": [
                        {
                            "name": field.name,
                            "type": str(field.dtype),
                            "is_primary": field.is_primary
                        }
                        for field in collection.schema.fields
                    ]
                }
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get collection stats: {e}")
            return {}
    
    def search_by_parameters(self, collection_name: str, 
                           parameter_filters: Dict[str, Any],
                           limit: int = 100) -> List[ModelVector]:
        """
        根据参数搜索模型
        
        Args:
            collection_name: 集合名称
            parameter_filters: 参数过滤条件
            limit: 结果限制
            
        Returns:
            匹配的模型列表
        """
        try:
            if collection_name not in self.collections:
                logger.error(f"Collection {collection_name} not found")
                return []
            
            collection = self.collections[collection_name]
            collection.load()
            
            # 构建过滤表达式（简化版本，实际应用中可能需要更复杂的查询）
            expr_parts = []
            for key, value in parameter_filters.items():
                if isinstance(value, str):
                    expr_parts.append(f'{key} == "{value}"')
                else:
                    expr_parts.append(f'{key} == {value}')
            
            expr = " and ".join(expr_parts) if expr_parts else None
            
            results = collection.query(
                expr=expr,
                limit=limit,
                output_fields=["id", "model_id", "model_type", "vector", 
                              "parameters", "metadata", "created_at"]
            )
            
            models = []
            for result in results:
                models.append(ModelVector(
                    id=result["id"],
                    model_id=result["model_id"],
                    model_type=result["model_type"],
                    vector=result["vector"],
                    parameters=json.loads(result["parameters"]),
                    metadata=json.loads(result["metadata"]),
                    created_at=datetime.fromisoformat(result["created_at"])
                ))
            
            logger.info(f"Found {len(models)} models matching parameters")
            return models
            
        except Exception as e:
            logger.error(f"Failed to search by parameters: {e}")
            return []


class ModelParameterService:
    """模型参数服务类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化模型参数服务
        
        Args:
            config: 配置信息，包含Milvus连接参数
        """
        self.config = config
        self.manager = ModelParameterManager(
            host=config['milvus']['host'],
            port=config['milvus']['port'],
            alias=config['milvus'].get('alias', 'default')
        )
        self.default_collection = config['milvus'].get('default_collection', 'model_parameters')
        self.vector_dim = config['milvus'].get('vector_dim', 512)
        
    def initialize(self) -> bool:
        """初始化服务"""
        if not self.manager.connect():
            return False
        
        # 创建默认集合
        if not self.manager.create_collection(
            collection_name=self.default_collection,
            vector_dim=self.vector_dim,
            description="AGI model parameters storage"
        ):
            return False
        
        # 创建索引
        return self.manager.create_index(
            collection_name=self.default_collection,
            index_type=self.config['milvus'].get('index_type', 'IVF_FLAT'),
            metric_type=self.config['milvus'].get('metric_type', 'L2')
        )
    
    def shutdown(self):
        """关闭服务"""
        self.manager.disconnect()
    
    def store_model(self, model_data: Dict[str, Any]) -> bool:
        """
        存储模型参数
        
        Args:
            model_data: 模型数据，包含向量和参数
            
        Returns:
            是否存储成功
        """
        try:
            model_vector = ModelVector(
                id=model_data.get('id', f"model_{datetime.now().timestamp()}"),
                model_id=model_data['model_id'],
                model_type=model_data['model_type'],
                vector=model_data['vector'],
                parameters=model_data['parameters'],
                metadata=model_data.get('metadata', {}),
                created_at=datetime.now()
            )
            
            return self.manager.insert_model_vector(
                collection_name=self.default_collection,
                model_vector=model_vector
            )
            
        except Exception as e:
            logger.error(f"Failed to store model: {e}")
            return False
    
    def find_similar_models(self, query_vector: List[float], 
                          top_k: int = 10, filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        查找相似模型
        
        Args:
            query_vector: 查询向量
            top_k: 返回结果数量
            filters: 过滤条件
            
        Returns:
            相似模型列表
        """
        # 构建过滤表达式
        expr = None
        if filters:
            expr_parts = []
            for key, value in filters.items():
                if isinstance(value, str):
                    expr_parts.append(f'{key} == "{value}"')
                else:
                    expr_parts.append(f'{key} == {value}')
            expr = " and ".join(expr_parts) if expr_parts else None
        
        results = self.manager.search_similar_models(
            collection_name=self.default_collection,
            query_vector=query_vector,
            top_k=top_k,
            expr=expr
        )
        
        return [
            {
                'id': result.id,
                'model_id': result.model_id,
                'similarity_score': result.similarity_score,
                'parameters': result.parameters,
                'metadata': result.metadata
            }
            for result in results
        ]
    
    def get_model(self, model_id: str) -> Optional[Dict[str, Any]]:
        """
        获取模型
        
        Args:
            model_id: 模型ID
            
        Returns:
            模型数据或None
        """
        model_vector = self.manager.get_model_by_id(
            collection_name=self.default_collection,
            model_id=model_id
        )
        
        if model_vector:
            return {
                'id': model_vector.id,
                'model_id': model_vector.model_id,
                'model_type': model_vector.model_type,
                'vector': model_vector.vector,
                'parameters': model_vector.parameters,
                'metadata': model_vector.metadata,
                'created_at': model_vector.created_at.isoformat()
            }
        
        return None
    
    def delete_model(self, model_id: str) -> bool:
        """
        删除模型
        
        Args:
            model_id: 模型ID
            
        Returns:
            是否删除成功
        """
        return self.manager.delete_model(
            collection_name=self.default_collection,
            model_id=model_id
        )
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.manager.get_collection_stats(self.default_collection)
