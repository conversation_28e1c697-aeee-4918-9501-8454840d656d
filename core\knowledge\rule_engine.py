"""
规则引擎模块 - 形式化规则和逻辑推理
"""
from typing import Dict, Any, List, Optional, Set, Callable, Union
from enum import Enum
from datetime import datetime
from pydantic import BaseModel, Field
from uuid import uuid4
import re


class RuleType(str, Enum):
    """规则类型枚举"""
    IF_THEN = "if_then"                 # 如果-那么规则
    CONSTRAINT = "constraint"           # 约束规则
    VALIDATION = "validation"           # 验证规则
    TRANSFORMATION = "transformation"   # 转换规则
    INFERENCE = "inference"             # 推理规则
    TRIGGER = "trigger"                 # 触发规则


class ConditionOperator(str, Enum):
    """条件操作符枚举"""
    EQUALS = "equals"                   # 等于
    NOT_EQUALS = "not_equals"          # 不等于
    GREATER_THAN = "greater_than"      # 大于
    LESS_THAN = "less_than"            # 小于
    GREATER_EQUAL = "greater_equal"    # 大于等于
    LESS_EQUAL = "less_equal"          # 小于等于
    CONTAINS = "contains"              # 包含
    NOT_CONTAINS = "not_contains"      # 不包含
    STARTS_WITH = "starts_with"        # 开始于
    ENDS_WITH = "ends_with"            # 结束于
    MATCHES = "matches"                # 正则匹配
    IN = "in"                          # 在集合中
    NOT_IN = "not_in"                  # 不在集合中


class LogicalOperator(str, Enum):
    """逻辑操作符枚举"""
    AND = "and"                        # 与
    OR = "or"                          # 或
    NOT = "not"                        # 非


class Condition(BaseModel):
    """条件"""
    
    field: str = Field(..., description="字段名")
    operator: ConditionOperator = Field(..., description="操作符")
    value: Any = Field(..., description="比较值")
    logical_operator: Optional[LogicalOperator] = Field(None, description="逻辑操作符")
    
    def evaluate(self, context: Dict[str, Any]) -> bool:
        """评估条件"""
        field_value = self._get_field_value(context, self.field)
        
        if field_value is None:
            return False
        
        try:
            if self.operator == ConditionOperator.EQUALS:
                return field_value == self.value
            elif self.operator == ConditionOperator.NOT_EQUALS:
                return field_value != self.value
            elif self.operator == ConditionOperator.GREATER_THAN:
                return field_value > self.value
            elif self.operator == ConditionOperator.LESS_THAN:
                return field_value < self.value
            elif self.operator == ConditionOperator.GREATER_EQUAL:
                return field_value >= self.value
            elif self.operator == ConditionOperator.LESS_EQUAL:
                return field_value <= self.value
            elif self.operator == ConditionOperator.CONTAINS:
                return str(self.value) in str(field_value)
            elif self.operator == ConditionOperator.NOT_CONTAINS:
                return str(self.value) not in str(field_value)
            elif self.operator == ConditionOperator.STARTS_WITH:
                return str(field_value).startswith(str(self.value))
            elif self.operator == ConditionOperator.ENDS_WITH:
                return str(field_value).endswith(str(self.value))
            elif self.operator == ConditionOperator.MATCHES:
                return bool(re.match(str(self.value), str(field_value)))
            elif self.operator == ConditionOperator.IN:
                return field_value in self.value
            elif self.operator == ConditionOperator.NOT_IN:
                return field_value not in self.value
        except Exception:
            return False
        
        return False
    
    def _get_field_value(self, context: Dict[str, Any], field_path: str) -> Any:
        """获取字段值，支持嵌套字段"""
        try:
            value = context
            for part in field_path.split('.'):
                if isinstance(value, dict):
                    value = value.get(part)
                elif hasattr(value, part):
                    value = getattr(value, part)
                else:
                    return None
            return value
        except Exception:
            return None


class Action(BaseModel):
    """动作"""
    
    type: str = Field(..., description="动作类型")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="动作参数")
    
    def execute(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """执行动作"""
        # 这里可以根据动作类型执行不同的操作
        result = {
            "action_type": self.type,
            "parameters": self.parameters,
            "context": context,
            "executed_at": datetime.now().isoformat()
        }
        
        return result


class Rule(BaseModel):
    """规则"""
    
    id: str = Field(default_factory=lambda: str(uuid4()))
    name: str = Field(..., description="规则名称")
    description: Optional[str] = Field(None, description="规则描述")
    rule_type: RuleType = Field(..., description="规则类型")
    
    # 条件和动作
    conditions: List[Condition] = Field(default_factory=list, description="条件列表")
    actions: List[Action] = Field(default_factory=list, description="动作列表")
    
    # 规则属性
    priority: int = Field(default=0, description="优先级")
    is_active: bool = Field(default=True, description="是否激活")
    
    # 执行控制
    max_executions: Optional[int] = Field(None, description="最大执行次数")
    execution_count: int = Field(default=0, description="已执行次数")
    
    # 时间控制
    valid_from: Optional[datetime] = Field(None, description="有效开始时间")
    valid_to: Optional[datetime] = Field(None, description="有效结束时间")
    
    # 元数据
    tags: List[str] = Field(default_factory=list, description="标签")
    category: Optional[str] = Field(None, description="分类")
    
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    last_executed: Optional[datetime] = Field(None, description="最后执行时间")
    
    class Config:
        arbitrary_types_allowed = True
    
    def can_execute(self, current_time: Optional[datetime] = None) -> bool:
        """检查规则是否可以执行"""
        if not self.is_active:
            return False
        
        if current_time is None:
            current_time = datetime.now()
        
        # 检查时间有效性
        if self.valid_from and current_time < self.valid_from:
            return False
        if self.valid_to and current_time > self.valid_to:
            return False
        
        # 检查执行次数限制
        if self.max_executions and self.execution_count >= self.max_executions:
            return False
        
        return True
    
    def evaluate_conditions(self, context: Dict[str, Any]) -> bool:
        """评估所有条件"""
        if not self.conditions:
            return True
        
        results = []
        current_logical_op = LogicalOperator.AND  # 默认为AND
        
        for condition in self.conditions:
            result = condition.evaluate(context)
            
            if condition.logical_operator:
                current_logical_op = condition.logical_operator
            
            results.append((result, current_logical_op))
        
        # 计算最终结果
        if not results:
            return True
        
        final_result = results[0][0]
        
        for i in range(1, len(results)):
            result, logical_op = results[i]
            
            if logical_op == LogicalOperator.AND:
                final_result = final_result and result
            elif logical_op == LogicalOperator.OR:
                final_result = final_result or result
            elif logical_op == LogicalOperator.NOT:
                final_result = final_result and not result
        
        return final_result
    
    def execute(self, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """执行规则"""
        if not self.can_execute():
            return []
        
        if not self.evaluate_conditions(context):
            return []
        
        results = []
        
        for action in self.actions:
            try:
                result = action.execute(context)
                results.append(result)
            except Exception as e:
                results.append({
                    "error": str(e),
                    "action_type": action.type,
                    "parameters": action.parameters
                })
        
        # 更新执行统计
        self.execution_count += 1
        self.last_executed = datetime.now()
        
        return results


class RuleEngine:
    """规则引擎"""
    
    def __init__(self):
        self.rules: Dict[str, Rule] = {}
        self.rule_index: Dict[str, Set[str]] = {
            "by_type": {},
            "by_category": {},
            "by_tag": {}
        }
        
        # 执行历史
        self.execution_history: List[Dict[str, Any]] = []
    
    def add_rule(self, rule: Rule) -> None:
        """添加规则"""
        self.rules[rule.id] = rule
        self._update_index(rule)
    
    def get_rule(self, rule_id: str) -> Optional[Rule]:
        """获取规则"""
        return self.rules.get(rule_id)
    
    def remove_rule(self, rule_id: str) -> bool:
        """移除规则"""
        if rule_id in self.rules:
            rule = self.rules[rule_id]
            del self.rules[rule_id]
            self._remove_from_index(rule)
            return True
        return False
    
    def execute_rules(self, context: Dict[str, Any], 
                     rule_type: Optional[RuleType] = None,
                     category: Optional[str] = None) -> List[Dict[str, Any]]:
        """执行规则"""
        applicable_rules = self._get_applicable_rules(rule_type, category)
        
        # 按优先级排序
        applicable_rules.sort(key=lambda r: r.priority, reverse=True)
        
        all_results = []
        
        for rule in applicable_rules:
            try:
                results = rule.execute(context)
                if results:
                    execution_record = {
                        "rule_id": rule.id,
                        "rule_name": rule.name,
                        "executed_at": datetime.now().isoformat(),
                        "results": results,
                        "context_snapshot": context.copy()
                    }
                    
                    all_results.extend(results)
                    self.execution_history.append(execution_record)
                    
                    # 限制历史记录数量
                    if len(self.execution_history) > 1000:
                        self.execution_history = self.execution_history[-500:]
                        
            except Exception as e:
                error_record = {
                    "rule_id": rule.id,
                    "rule_name": rule.name,
                    "error": str(e),
                    "executed_at": datetime.now().isoformat()
                }
                self.execution_history.append(error_record)
        
        return all_results
    
    def _get_applicable_rules(self, rule_type: Optional[RuleType] = None,
                            category: Optional[str] = None) -> List[Rule]:
        """获取适用的规则"""
        if rule_type:
            rule_ids = self.rule_index["by_type"].get(rule_type.value, set())
        elif category:
            rule_ids = self.rule_index["by_category"].get(category, set())
        else:
            rule_ids = set(self.rules.keys())
        
        applicable_rules = []
        
        for rule_id in rule_ids:
            rule = self.rules.get(rule_id)
            if rule and rule.can_execute():
                applicable_rules.append(rule)
        
        return applicable_rules
    
    def get_rules_by_tag(self, tag: str) -> List[Rule]:
        """根据标签获取规则"""
        rule_ids = self.rule_index["by_tag"].get(tag, set())
        return [self.rules[rid] for rid in rule_ids if rid in self.rules]
    
    def get_execution_statistics(self) -> Dict[str, Any]:
        """获取执行统计"""
        total_executions = len(self.execution_history)
        
        if total_executions == 0:
            return {"total_executions": 0}
        
        # 按规则统计
        rule_stats = {}
        error_count = 0
        
        for record in self.execution_history:
            rule_id = record.get("rule_id")
            if rule_id:
                if rule_id not in rule_stats:
                    rule_stats[rule_id] = {"count": 0, "errors": 0}
                rule_stats[rule_id]["count"] += 1
                
                if "error" in record:
                    rule_stats[rule_id]["errors"] += 1
                    error_count += 1
        
        return {
            "total_executions": total_executions,
            "total_errors": error_count,
            "success_rate": (total_executions - error_count) / total_executions,
            "rule_statistics": rule_stats,
            "active_rules": len([r for r in self.rules.values() if r.is_active]),
            "total_rules": len(self.rules)
        }
    
    def _update_index(self, rule: Rule) -> None:
        """更新索引"""
        # 按类型索引
        if rule.rule_type.value not in self.rule_index["by_type"]:
            self.rule_index["by_type"][rule.rule_type.value] = set()
        self.rule_index["by_type"][rule.rule_type.value].add(rule.id)
        
        # 按分类索引
        if rule.category:
            if rule.category not in self.rule_index["by_category"]:
                self.rule_index["by_category"][rule.category] = set()
            self.rule_index["by_category"][rule.category].add(rule.id)
        
        # 按标签索引
        for tag in rule.tags:
            if tag not in self.rule_index["by_tag"]:
                self.rule_index["by_tag"][tag] = set()
            self.rule_index["by_tag"][tag].add(rule.id)
    
    def _remove_from_index(self, rule: Rule) -> None:
        """从索引中移除"""
        # 从类型索引移除
        if rule.rule_type.value in self.rule_index["by_type"]:
            self.rule_index["by_type"][rule.rule_type.value].discard(rule.id)
        
        # 从分类索引移除
        if rule.category and rule.category in self.rule_index["by_category"]:
            self.rule_index["by_category"][rule.category].discard(rule.id)
        
        # 从标签索引移除
        for tag in rule.tags:
            if tag in self.rule_index["by_tag"]:
                self.rule_index["by_tag"][tag].discard(rule.id)
