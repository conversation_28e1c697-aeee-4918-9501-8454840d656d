#!/usr/bin/env python3
"""
AGI Knowledge Graph System - 项目验证脚本
验证项目的完整性和功能正确性
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

def check_file_structure():
    """检查文件结构"""
    print("🔍 检查项目文件结构...")
    
    required_files = [
        "README.md",
        "requirements.txt",
        "config.py",
        "main.py",
        "cli.py",
        "demo.py",
        "simple_demo.py",
        "run.py",
        "LICENSE",
        "Makefile",
        "pyproject.toml",
        ".gitignore",
        "PROJECT_SUMMARY.md",
        "QUICK_START.md"
    ]
    
    required_dirs = [
        "core",
        "core/entities",
        "core/semantic", 
        "core/knowledge",
        "core/tasks",
        "examples",
        "scripts",
        "tests"
    ]
    
    missing_files = []
    missing_dirs = []
    
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
        else:
            print(f"   ✅ {file}")
    
    for dir in required_dirs:
        if not Path(dir).exists():
            missing_dirs.append(dir)
        else:
            print(f"   ✅ {dir}/")
    
    if missing_files:
        print(f"   ❌ 缺少文件: {missing_files}")
        return False
    
    if missing_dirs:
        print(f"   ❌ 缺少目录: {missing_dirs}")
        return False
    
    print("   ✅ 文件结构检查通过")
    return True


def check_core_modules():
    """检查核心模块导入"""
    print("\n🔍 检查核心模块导入...")
    
    modules_to_test = [
        ("core.entities.concept", "Concept"),
        ("core.entities.entity", "Entity"),
        ("core.semantic.relationship", "Relationship"),
        ("core.knowledge.knowledge_base", "KnowledgeBase"),
        ("core.knowledge.rule_engine", "RuleEngine"),
        ("core.knowledge.skill_library", "SkillLibrary"),
        ("core.tasks.task", "Task"),
    ]
    
    failed_imports = []
    
    for module_name, class_name in modules_to_test:
        try:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            print(f"   ✅ {module_name}.{class_name}")
        except Exception as e:
            print(f"   ❌ {module_name}.{class_name}: {e}")
            failed_imports.append(f"{module_name}.{class_name}")
    
    if failed_imports:
        print(f"   ❌ 导入失败的模块: {failed_imports}")
        return False
    
    print("   ✅ 核心模块导入检查通过")
    return True


def check_basic_functionality():
    """检查基本功能"""
    print("\n🔍 检查基本功能...")
    
    try:
        # 测试概念创建
        from core.entities.concept import Concept, ConceptType
        concept = Concept(name="测试概念", concept_type=ConceptType.ABSTRACT)
        assert concept.validate_node() == True
        print("   ✅ 概念创建和验证")
        
        # 测试实体创建
        from core.entities.entity import Entity, EntityType
        entity = Entity(name="测试实体", entity_type=EntityType.PERSON)
        assert entity.validate_node() == True
        print("   ✅ 实体创建和验证")
        
        # 测试关系创建
        from core.semantic.relationship import Relationship, RelationshipType
        relationship = Relationship(
            relationship_type=RelationshipType.IS_A,
            source_id=entity.id,
            target_id=concept.id
        )
        print("   ✅ 关系创建")
        
        # 测试知识库
        from core.knowledge.knowledge_base import KnowledgeBase, KnowledgeItem, KnowledgeType
        kb = KnowledgeBase()
        knowledge = KnowledgeItem(
            title="测试知识",
            content="测试内容",
            knowledge_type=KnowledgeType.FACTUAL
        )
        kb.add_knowledge(knowledge)
        results = kb.search_by_keyword("测试")
        assert len(results) > 0
        print("   ✅ 知识库操作")
        
        # 测试规则引擎
        from core.knowledge.rule_engine import RuleEngine, Rule, RuleType
        engine = RuleEngine()
        rule = Rule(name="测试规则", rule_type=RuleType.IF_THEN)
        engine.add_rule(rule)
        print("   ✅ 规则引擎")
        
        # 测试技能库
        from core.knowledge.skill_library import SkillLibrary, Skill, SkillType
        skill_lib = SkillLibrary()
        skill = Skill(
            name="测试技能",
            description="测试描述",
            skill_type=SkillType.COGNITIVE
        )
        skill_lib.add_skill(skill)
        print("   ✅ 技能库")
        
        # 测试任务管理
        from core.tasks.task import Task, TaskType
        task = Task(
            name="测试任务",
            description="测试描述",
            task_type=TaskType.COGNITIVE
        )
        print("   ✅ 任务管理")
        
        print("   ✅ 基本功能检查通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 基本功能检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_configuration():
    """检查配置"""
    print("\n🔍 检查配置...")
    
    try:
        from config import settings
        
        # 检查基本配置
        assert settings.app_name is not None
        assert settings.app_version is not None
        print(f"   ✅ 应用配置: {settings.app_name} v{settings.app_version}")
        
        # 检查数据库配置
        assert settings.database_url is not None
        print(f"   ✅ 数据库配置: {settings.database_url}")
        
        # 检查API配置
        assert settings.api_host is not None
        assert settings.api_port is not None
        print(f"   ✅ API配置: {settings.api_host}:{settings.api_port}")
        
        print("   ✅ 配置检查通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 配置检查失败: {e}")
        return False


def check_scripts():
    """检查脚本可执行性"""
    print("\n🔍 检查脚本可执行性...")
    
    scripts_to_test = [
        "simple_demo.py",
        "cli.py",
        "run.py"
    ]
    
    for script in scripts_to_test:
        try:
            # 检查文件是否存在且可读
            if Path(script).exists() and Path(script).is_file():
                print(f"   ✅ {script} 存在且可访问")
            else:
                print(f"   ❌ {script} 不存在或不可访问")
                return False
        except Exception as e:
            print(f"   ❌ {script} 检查失败: {e}")
            return False
    
    print("   ✅ 脚本可执行性检查通过")
    return True


def generate_report(results):
    """生成检查报告"""
    print("\n" + "="*60)
    print("📊 项目验证报告")
    print("="*60)
    
    total_checks = len(results)
    passed_checks = sum(results.values())
    
    print(f"总检查项: {total_checks}")
    print(f"通过检查: {passed_checks}")
    print(f"失败检查: {total_checks - passed_checks}")
    print(f"通过率: {passed_checks/total_checks*100:.1f}%")
    
    print("\n详细结果:")
    for check_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {check_name}: {status}")
    
    if passed_checks == total_checks:
        print("\n🎉 所有检查都通过！项目状态良好。")
        print("\n💡 下一步建议:")
        print("  1. 运行简化演示: python simple_demo.py")
        print("  2. 安装完整依赖: pip install -r requirements.txt")
        print("  3. 运行完整演示: python demo.py")
        print("  4. 启动API服务: python main.py")
        return True
    else:
        print("\n⚠️  部分检查失败，请修复问题后重新验证。")
        return False


def main():
    """主函数"""
    print("🚀 AGI Knowledge Graph System - 项目验证")
    print("="*60)
    
    # 执行各项检查
    results = {
        "文件结构": check_file_structure(),
        "核心模块": check_core_modules(),
        "基本功能": check_basic_functionality(),
        "配置检查": check_configuration(),
        "脚本检查": check_scripts()
    }
    
    # 生成报告
    success = generate_report(results)
    
    if success:
        print("\n🎯 项目验证成功！可以开始使用AGI Knowledge Graph System。")
        return 0
    else:
        print("\n❌ 项目验证失败，请检查并修复问题。")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
