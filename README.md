# AGI Knowledge Graph System

一个基于动态知识图谱的人工通用智能(AGI)工程项目，结合大模型和构造动态知识图谱，在接收到用户问题时动态获取相关信息生成提示词指导大模型回答问题。

## 项目架构

本项目采用模块化设计，包含以下8个核心模块：

### 1. 概念和实体层 (core/entities/)
- 认知系统中的核心抽象概念及其层次结构
- 具体的知识实例或数据点管理
- 实体唯一标识符和描述字段

### 2. 语义关系和连接层 (core/semantic/)
- 实体间的逻辑关系描述
- 属性连接和本体组织
- 语义理解的上下文和推理基础

### 3. 知识和技能模块 (core/knowledge/)
- 事实性、程序性和描述性知识库
- 形式化规则库
- 具体任务执行步骤的技能库

### 4. 任务与目标层 (core/tasks/)
- 任务描述和层次结构
- 优先级和调度机制
- 条件和约束管理

### 5. 环境和情境感知 (core/context/)
- 环境描述和情境模型
- 实时环境变化响应
- 情境更新频率控制

### 6. 反馈和学习机制 (core/learning/)
- 反馈记录和元数据标签
- 学习算法和模型优化
- 自适应能力提升

### 7. 逻辑与推理层 (core/reasoning/)
- 推理规则和决策树
- 因果链理解
- 复杂问题决策支持

### 8. 数据和隐私保护层 (core/security/)
- 数据访问控制
- 隐私元数据管理
- 合规性规则确保

## 快速开始

### 方法一：使用快速启动脚本（推荐）

```bash
# 运行快速启动脚本，自动检查环境并引导设置
python run.py
```

### 方法二：使用Makefile

```bash
# 完整设置（安装依赖 + 初始化系统）
make setup

# 运行演示
make demo

# 启动API服务器
make serve
```

### 方法三：使用CLI工具

```bash
# 初始化系统
python cli.py init

# 创建示例数据
python cli.py create-sample

# 运行演示
python cli.py demo

# 启动API服务器
python cli.py serve
```

### 方法四：手动步骤

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 初始化数据库
python scripts/init_db.py

# 3. 运行演示（可选）
python examples/basic_usage.py

# 4. 启动API服务器
python main.py
```

## 项目特性

- 🧠 动态知识图谱构建
- 🔗 语义关系推理
- 📚 多层次知识管理
- 🎯 智能任务调度
- 🌍 情境感知能力
- 📈 自适应学习机制
- 🔒 数据隐私保护

## API使用

启动服务器后，可以通过以下方式使用API：

```bash
# 查看API文档
curl http://localhost:8000/docs

# 健康检查
curl http://localhost:8000/health

# 获取系统统计
curl http://localhost:8000/stats

# 处理查询
curl -X POST http://localhost:8000/query \
  -H "Content-Type: application/json" \
  -d '{"question": "什么是人工智能？", "context": {}}'
```

## 开发指南

### 项目结构

```
AGI-Knowledge-Graph/
├── core/                    # 核心模块
│   ├── entities/           # 概念和实体层
│   ├── semantic/           # 语义关系和连接层
│   ├── knowledge/          # 知识和技能模块
│   ├── tasks/             # 任务与目标层
│   ├── context/           # 环境和情境感知
│   ├── learning/          # 反馈和学习机制
│   ├── reasoning/         # 逻辑与推理层
│   └── security/          # 数据和隐私保护层
├── examples/              # 示例代码
├── scripts/               # 工具脚本
├── tests/                 # 测试文件
├── main.py               # 主入口文件
├── cli.py                # 命令行工具
├── run.py                # 快速启动脚本
└── config.py             # 配置文件
```

### 开发环境设置

```bash
# 安装开发依赖
make dev-install

# 代码格式化
make format

# 代码质量检查
make lint

# 运行测试
make test
```

### 添加新模块

1. 在相应的core子目录中创建新模块
2. 实现必要的基类和接口
3. 添加相应的测试文件
4. 更新文档和示例

## 技术栈

- **Python 3.8+** - 主要编程语言
- **FastAPI** - 现代高性能Web框架
- **Neo4j** - 图数据库，存储知识图谱
- **SQLAlchemy** - SQL工具包和ORM
- **NetworkX** - 图分析和算法库
- **Pydantic** - 数据验证和设置管理
- **OpenAI API** - 大模型集成
- **Loguru** - 现代日志库
- **Typer** - 命令行界面库
- **Rich** - 丰富的终端输出

## 核心概念

### 知识图谱架构

本系统采用多层次的知识图谱架构：

1. **概念层** - 抽象概念和概念层次
2. **实体层** - 具体实例和数据点
3. **关系层** - 实体间的语义关系
4. **规则层** - 推理规则和约束
5. **技能层** - 可执行的任务流程

### 动态知识获取

系统支持多种知识获取方式：

- **手动输入** - 通过API或CLI添加知识
- **自动学习** - 从交互中学习新知识
- **外部导入** - 从文件或数据库导入
- **推理生成** - 通过规则推理产生新知识

### 语义推理

系统提供多种推理能力：

- **层次推理** - 基于概念层次的推理
- **关系推理** - 基于实体关系的推理
- **规则推理** - 基于形式化规则的推理
- **相似性推理** - 基于语义相似性的推理

## 使用场景

### 1. 智能问答系统

```python
from core.semantic.semantic_network import SemanticNetwork

# 创建语义网络
network = SemanticNetwork()

# 添加知识
# ... 添加概念、实体、关系

# 查询相关信息
related_info = network.find_related_nodes("用户问题实体")
```

### 2. 知识管理系统

```python
from core.knowledge.knowledge_base import KnowledgeBase

# 创建知识库
kb = KnowledgeBase()

# 搜索知识
results = kb.full_text_search("人工智能")
```

### 3. 规则引擎

```python
from core.knowledge.rule_engine import RuleEngine

# 创建规则引擎
engine = RuleEngine()

# 执行规则
results = engine.execute_rules(context_data)
```

## 配置说明

系统配置通过环境变量或`.env`文件进行：

```bash
# 复制配置模板
cp .env.example .env

# 编辑配置
vim .env
```

主要配置项：

- `DATABASE_URL` - 数据库连接URL
- `NEO4J_URI` - Neo4j图数据库URI
- `OPENAI_API_KEY` - OpenAI API密钥
- `LOG_LEVEL` - 日志级别
- `MAX_GRAPH_DEPTH` - 最大图遍历深度

## 测试

```bash
# 运行所有测试
make test

# 运行特定测试
pytest tests/test_entities.py -v

# 生成覆盖率报告
pytest --cov=core --cov-report=html
```

## 部署

### Docker部署（待实现）

```bash
# 构建镜像
docker build -t agi-knowledge-graph .

# 运行容器
docker run -p 8000:8000 agi-knowledge-graph
```

### 生产环境部署

1. 设置环境变量
2. 配置数据库连接
3. 启动Neo4j服务
4. 运行应用服务器

```bash
# 生产环境启动
uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
```

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目主页: [GitHub Repository](https://github.com/example/agi-knowledge-graph)
- 问题反馈: [Issues](https://github.com/example/agi-knowledge-graph/issues)
- 文档: [Documentation](https://agi-knowledge-graph.readthedocs.io/)

## 致谢

感谢所有为这个项目做出贡献的开发者和研究人员。

---

**注意**: 这是一个研究性项目，仍在积极开发中。欢迎反馈和贡献！
