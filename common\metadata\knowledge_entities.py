"""
知识图实体和关系定义
实现底层知识图的实体、属性、关系结构
"""

from typing import Dict, List, Optional, Any, Union, Set
from dataclasses import dataclass, field
from datetime import datetime
import uuid
import json

from .core_structures import SemanticDescriptor, EvolutionRecord


@dataclass
class KnowledgeEntity:
    """知识实体 - 底层知识图的基本实体"""
    entity_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    entity_type: str = ""  # 对应TypeSchema的schema_id
    name: str = ""
    description: str = ""
    
    # 语义描述
    semantic_descriptor: Optional[SemanticDescriptor] = None
    
    # 属性数据
    attributes: Dict[str, Any] = field(default_factory=dict)
    
    # 计算属性（动态计算的属性）
    computed_attributes: Dict[str, str] = field(default_factory=dict)  # 属性名 -> 计算表达式
    
    # 时间信息
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    created_by: str = "system"
    updated_by: str = "system"
    
    # 版本信息
    version: str = "1.0.0"
    evolution_history: List[str] = field(default_factory=list)  # evolution_record_ids
    
    # 状态信息
    status: str = "active"  # active, inactive, deprecated, archived
    confidence: float = 1.0  # 置信度 0.0-1.0
    
    # 标签和分类
    tags: Set[str] = field(default_factory=set)
    categories: Set[str] = field(default_factory=set)
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "entity_id": self.entity_id,
            "entity_type": self.entity_type,
            "name": self.name,
            "description": self.description,
            "semantic_descriptor": self.semantic_descriptor.to_dict() if self.semantic_descriptor else None,
            "attributes": self.attributes,
            "computed_attributes": self.computed_attributes,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "created_by": self.created_by,
            "updated_by": self.updated_by,
            "version": self.version,
            "evolution_history": self.evolution_history,
            "status": self.status,
            "confidence": self.confidence,
            "tags": list(self.tags),
            "categories": list(self.categories),
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'KnowledgeEntity':
        """从字典创建"""
        entity = cls()
        entity.entity_id = data.get("entity_id", entity.entity_id)
        entity.entity_type = data.get("entity_type", "")
        entity.name = data.get("name", "")
        entity.description = data.get("description", "")
        
        if data.get("semantic_descriptor"):
            entity.semantic_descriptor = SemanticDescriptor.from_dict(data["semantic_descriptor"])
        
        entity.attributes = data.get("attributes", {})
        entity.computed_attributes = data.get("computed_attributes", {})
        entity.created_by = data.get("created_by", "system")
        entity.updated_by = data.get("updated_by", "system")
        entity.version = data.get("version", "1.0.0")
        entity.evolution_history = data.get("evolution_history", [])
        entity.status = data.get("status", "active")
        entity.confidence = data.get("confidence", 1.0)
        entity.tags = set(data.get("tags", []))
        entity.categories = set(data.get("categories", []))
        entity.metadata = data.get("metadata", {})
        
        if "created_at" in data:
            entity.created_at = datetime.fromisoformat(data["created_at"])
        if "updated_at" in data:
            entity.updated_at = datetime.fromisoformat(data["updated_at"])
        
        return entity
    
    def get_attribute(self, name: str, default: Any = None) -> Any:
        """获取属性值"""
        return self.attributes.get(name, default)
    
    def set_attribute(self, name: str, value: Any, operator: str = "system") -> None:
        """设置属性值"""
        old_value = self.attributes.get(name)
        self.attributes[name] = value
        self.updated_at = datetime.now()
        self.updated_by = operator
        
        # 记录变更到元数据
        if "attribute_changes" not in self.metadata:
            self.metadata["attribute_changes"] = []
        
        self.metadata["attribute_changes"].append({
            "attribute": name,
            "old_value": old_value,
            "new_value": value,
            "timestamp": self.updated_at.isoformat(),
            "operator": operator
        })
    
    def remove_attribute(self, name: str, operator: str = "system") -> Any:
        """移除属性"""
        old_value = self.attributes.pop(name, None)
        if old_value is not None:
            self.updated_at = datetime.now()
            self.updated_by = operator
            
            # 记录变更到元数据
            if "attribute_changes" not in self.metadata:
                self.metadata["attribute_changes"] = []
            
            self.metadata["attribute_changes"].append({
                "attribute": name,
                "old_value": old_value,
                "new_value": None,
                "timestamp": self.updated_at.isoformat(),
                "operator": operator,
                "action": "remove"
            })
        
        return old_value
    
    def add_tag(self, tag: str) -> None:
        """添加标签"""
        self.tags.add(tag)
        self.updated_at = datetime.now()
    
    def remove_tag(self, tag: str) -> bool:
        """移除标签"""
        if tag in self.tags:
            self.tags.remove(tag)
            self.updated_at = datetime.now()
            return True
        return False
    
    def add_category(self, category: str) -> None:
        """添加分类"""
        self.categories.add(category)
        self.updated_at = datetime.now()
    
    def remove_category(self, category: str) -> bool:
        """移除分类"""
        if category in self.categories:
            self.categories.remove(category)
            self.updated_at = datetime.now()
            return True
        return False


@dataclass
class KnowledgeRelation:
    """知识关系 - 底层知识图的关系"""
    relation_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    relation_type: str = ""  # 对应RelationSpecification的relation_id
    name: str = ""
    description: str = ""
    
    # 关系端点
    source_entity_id: str = ""
    target_entity_id: str = ""
    
    # 语义描述
    semantic_descriptor: Optional[SemanticDescriptor] = None
    
    # 关系属性
    attributes: Dict[str, Any] = field(default_factory=dict)
    
    # 关系强度和权重
    strength: float = 1.0  # 关系强度 0.0-1.0
    weight: float = 1.0    # 关系权重
    confidence: float = 1.0  # 置信度 0.0-1.0
    
    # 时间信息
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    created_by: str = "system"
    updated_by: str = "system"
    
    # 有效期（可选）
    valid_from: Optional[datetime] = None
    valid_to: Optional[datetime] = None
    
    # 版本信息
    version: str = "1.0.0"
    evolution_history: List[str] = field(default_factory=list)
    
    # 状态信息
    status: str = "active"  # active, inactive, deprecated, archived
    
    # 标签和分类
    tags: Set[str] = field(default_factory=set)
    categories: Set[str] = field(default_factory=set)
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "relation_id": self.relation_id,
            "relation_type": self.relation_type,
            "name": self.name,
            "description": self.description,
            "source_entity_id": self.source_entity_id,
            "target_entity_id": self.target_entity_id,
            "semantic_descriptor": self.semantic_descriptor.to_dict() if self.semantic_descriptor else None,
            "attributes": self.attributes,
            "strength": self.strength,
            "weight": self.weight,
            "confidence": self.confidence,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "created_by": self.created_by,
            "updated_by": self.updated_by,
            "valid_from": self.valid_from.isoformat() if self.valid_from else None,
            "valid_to": self.valid_to.isoformat() if self.valid_to else None,
            "version": self.version,
            "evolution_history": self.evolution_history,
            "status": self.status,
            "tags": list(self.tags),
            "categories": list(self.categories),
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'KnowledgeRelation':
        """从字典创建"""
        relation = cls()
        relation.relation_id = data.get("relation_id", relation.relation_id)
        relation.relation_type = data.get("relation_type", "")
        relation.name = data.get("name", "")
        relation.description = data.get("description", "")
        relation.source_entity_id = data.get("source_entity_id", "")
        relation.target_entity_id = data.get("target_entity_id", "")
        
        if data.get("semantic_descriptor"):
            relation.semantic_descriptor = SemanticDescriptor.from_dict(data["semantic_descriptor"])
        
        relation.attributes = data.get("attributes", {})
        relation.strength = data.get("strength", 1.0)
        relation.weight = data.get("weight", 1.0)
        relation.confidence = data.get("confidence", 1.0)
        relation.created_by = data.get("created_by", "system")
        relation.updated_by = data.get("updated_by", "system")
        relation.version = data.get("version", "1.0.0")
        relation.evolution_history = data.get("evolution_history", [])
        relation.status = data.get("status", "active")
        relation.tags = set(data.get("tags", []))
        relation.categories = set(data.get("categories", []))
        relation.metadata = data.get("metadata", {})
        
        if "created_at" in data:
            relation.created_at = datetime.fromisoformat(data["created_at"])
        if "updated_at" in data:
            relation.updated_at = datetime.fromisoformat(data["updated_at"])
        if "valid_from" in data and data["valid_from"]:
            relation.valid_from = datetime.fromisoformat(data["valid_from"])
        if "valid_to" in data and data["valid_to"]:
            relation.valid_to = datetime.fromisoformat(data["valid_to"])
        
        return relation
    
    def is_valid_at(self, timestamp: datetime = None) -> bool:
        """检查关系在指定时间是否有效"""
        if timestamp is None:
            timestamp = datetime.now()
        
        if self.valid_from and timestamp < self.valid_from:
            return False
        
        if self.valid_to and timestamp > self.valid_to:
            return False
        
        return self.status == "active"
    
    def get_attribute(self, name: str, default: Any = None) -> Any:
        """获取关系属性值"""
        return self.attributes.get(name, default)
    
    def set_attribute(self, name: str, value: Any, operator: str = "system") -> None:
        """设置关系属性值"""
        old_value = self.attributes.get(name)
        self.attributes[name] = value
        self.updated_at = datetime.now()
        self.updated_by = operator
        
        # 记录变更到元数据
        if "attribute_changes" not in self.metadata:
            self.metadata["attribute_changes"] = []
        
        self.metadata["attribute_changes"].append({
            "attribute": name,
            "old_value": old_value,
            "new_value": value,
            "timestamp": self.updated_at.isoformat(),
            "operator": operator
        })
    
    def update_strength(self, new_strength: float, operator: str = "system") -> None:
        """更新关系强度"""
        old_strength = self.strength
        self.strength = max(0.0, min(1.0, new_strength))  # 限制在0-1范围内
        self.updated_at = datetime.now()
        self.updated_by = operator
        
        # 记录变更到元数据
        if "strength_changes" not in self.metadata:
            self.metadata["strength_changes"] = []
        
        self.metadata["strength_changes"].append({
            "old_strength": old_strength,
            "new_strength": self.strength,
            "timestamp": self.updated_at.isoformat(),
            "operator": operator
        })
    
    def update_confidence(self, new_confidence: float, operator: str = "system") -> None:
        """更新置信度"""
        old_confidence = self.confidence
        self.confidence = max(0.0, min(1.0, new_confidence))  # 限制在0-1范围内
        self.updated_at = datetime.now()
        self.updated_by = operator
        
        # 记录变更到元数据
        if "confidence_changes" not in self.metadata:
            self.metadata["confidence_changes"] = []
        
        self.metadata["confidence_changes"].append({
            "old_confidence": old_confidence,
            "new_confidence": self.confidence,
            "timestamp": self.updated_at.isoformat(),
            "operator": operator
        })


@dataclass
class KnowledgePattern:
    """知识模式 - 表示知识图中的模式和规律"""
    pattern_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    pattern_type: str = "structural"  # structural, temporal, semantic, behavioral
    
    # 模式定义
    pattern_definition: Dict[str, Any] = field(default_factory=dict)
    
    # 匹配条件
    match_conditions: List[Dict[str, Any]] = field(default_factory=list)
    
    # 应用规则
    application_rules: List[str] = field(default_factory=list)
    
    # 统计信息
    occurrence_count: int = 0
    confidence_score: float = 0.0
    support_score: float = 0.0
    
    # 时间信息
    discovered_at: datetime = field(default_factory=datetime.now)
    last_seen_at: datetime = field(default_factory=datetime.now)
    
    # 状态信息
    status: str = "active"
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "pattern_id": self.pattern_id,
            "name": self.name,
            "description": self.description,
            "pattern_type": self.pattern_type,
            "pattern_definition": self.pattern_definition,
            "match_conditions": self.match_conditions,
            "application_rules": self.application_rules,
            "occurrence_count": self.occurrence_count,
            "confidence_score": self.confidence_score,
            "support_score": self.support_score,
            "discovered_at": self.discovered_at.isoformat(),
            "last_seen_at": self.last_seen_at.isoformat(),
            "status": self.status,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'KnowledgePattern':
        """从字典创建"""
        pattern = cls()
        pattern.pattern_id = data.get("pattern_id", pattern.pattern_id)
        pattern.name = data.get("name", "")
        pattern.description = data.get("description", "")
        pattern.pattern_type = data.get("pattern_type", "structural")
        pattern.pattern_definition = data.get("pattern_definition", {})
        pattern.match_conditions = data.get("match_conditions", [])
        pattern.application_rules = data.get("application_rules", [])
        pattern.occurrence_count = data.get("occurrence_count", 0)
        pattern.confidence_score = data.get("confidence_score", 0.0)
        pattern.support_score = data.get("support_score", 0.0)
        pattern.status = data.get("status", "active")
        pattern.metadata = data.get("metadata", {})
        
        if "discovered_at" in data:
            pattern.discovered_at = datetime.fromisoformat(data["discovered_at"])
        if "last_seen_at" in data:
            pattern.last_seen_at = datetime.fromisoformat(data["last_seen_at"])
        
        return pattern
