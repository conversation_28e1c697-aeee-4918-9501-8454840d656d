{"$schema": "http://json-schema.org/draft-07/schema#", "title": "AGI知识层元数据标准", "description": "定义AGI系统中知识层的元数据结构和标准", "version": "1.0.0", "type": "object", "properties": {"metadata_info": {"type": "object", "description": "元数据基本信息", "properties": {"schema_version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "created_by": {"type": "string"}, "last_modified_by": {"type": "string"}}, "required": ["schema_version", "created_at", "created_by"]}, "knowledge_definition": {"type": "object", "description": "知识定义信息", "properties": {"knowledge_id": {"type": "string", "description": "知识唯一标识符", "pattern": "^knowledge_[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$"}, "title": {"type": "string", "description": "知识标题", "minLength": 1, "maxLength": 500}, "knowledge_type": {"type": "string", "description": "知识类型", "enum": ["factual", "procedural", "declarative", "conceptual", "metacognitive", "experiential", "conditional", "causal", "temporal", "spatial"]}, "content_format": {"type": "string", "description": "内容格式", "enum": ["text", "structured_data", "rule_based", "mathematical", "logical", "multimedia", "code", "diagram", "table", "graph"]}, "description": {"type": "string", "description": "知识描述", "maxLength": 2000}, "abstract": {"type": "string", "description": "知识摘要", "maxLength": 1000}, "content": {"description": "知识内容，可以是文本、结构化数据或其他格式"}, "content_schema": {"type": "object", "description": "内容结构模式（当content为结构化数据时）"}}, "required": ["knowledge_id", "title", "knowledge_type", "content_format", "content"]}, "classification_taxonomy": {"type": "object", "description": "分类和分类法", "properties": {"primary_category": {"type": "string", "description": "主要分类", "maxLength": 100}, "secondary_categories": {"type": "array", "description": "次要分类", "items": {"type": "string", "maxLength": 100}, "uniqueItems": true}, "domain": {"type": "string", "description": "知识领域", "enum": ["general", "science", "technology", "medicine", "biology", "physics", "chemistry", "mathematics", "computer_science", "psychology", "philosophy", "linguistics", "economics", "sociology", "history", "geography", "arts", "literature", "music", "sports", "business", "law", "education", "engineering", "custom"]}, "subdomain": {"type": "string", "description": "子领域", "maxLength": 100}, "subject_area": {"type": "string", "description": "主题领域", "maxLength": 100}, "keywords": {"type": "array", "description": "关键词", "items": {"type": "string", "maxLength": 50}, "uniqueItems": true}, "tags": {"type": "array", "description": "标签", "items": {"type": "object", "properties": {"tag_name": {"type": "string", "maxLength": 50}, "tag_type": {"type": "string", "enum": ["topic", "method", "application", "difficulty", "audience", "custom"]}, "weight": {"type": "number", "minimum": 0.0, "maximum": 1.0, "default": 1.0}}, "required": ["tag_name", "tag_type"]}}, "hierarchical_path": {"type": "array", "description": "分类层次路径", "items": {"type": "string"}}}}, "semantic_annotations": {"type": "object", "description": "语义标注", "properties": {"entities": {"type": "array", "description": "实体标注", "items": {"type": "object", "properties": {"entity_id": {"type": "string"}, "entity_name": {"type": "string"}, "entity_type": {"type": "string"}, "position": {"type": "object", "description": "在内容中的位置", "properties": {"start": {"type": "integer"}, "end": {"type": "integer"}}}, "confidence": {"type": "number", "minimum": 0.0, "maximum": 1.0}}, "required": ["entity_id", "entity_name", "entity_type"]}}, "concepts": {"type": "array", "description": "概念标注", "items": {"type": "object", "properties": {"concept_id": {"type": "string"}, "concept_name": {"type": "string"}, "relevance_score": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "context": {"type": "string", "description": "概念在知识中的上下文"}}, "required": ["concept_id", "concept_name"]}}, "relationships": {"type": "array", "description": "关系标注", "items": {"type": "object", "properties": {"relationship_id": {"type": "string"}, "relationship_type": {"type": "string"}, "source_entity": {"type": "string"}, "target_entity": {"type": "string"}, "confidence": {"type": "number", "minimum": 0.0, "maximum": 1.0}}, "required": ["relationship_id", "relationship_type", "source_entity", "target_entity"]}}}}, "quality_assessment": {"type": "object", "description": "质量评估", "properties": {"accuracy": {"type": "number", "description": "准确性", "minimum": 0.0, "maximum": 1.0}, "completeness": {"type": "number", "description": "完整性", "minimum": 0.0, "maximum": 1.0}, "consistency": {"type": "number", "description": "一致性", "minimum": 0.0, "maximum": 1.0}, "reliability": {"type": "number", "description": "可靠性", "minimum": 0.0, "maximum": 1.0}, "relevance": {"type": "number", "description": "相关性", "minimum": 0.0, "maximum": 1.0}, "timeliness": {"type": "number", "description": "时效性", "minimum": 0.0, "maximum": 1.0}, "clarity": {"type": "number", "description": "清晰度", "minimum": 0.0, "maximum": 1.0}, "difficulty_level": {"type": "string", "description": "难度级别", "enum": ["beginner", "intermediate", "advanced", "expert"]}, "confidence_score": {"type": "number", "description": "整体置信度", "minimum": 0.0, "maximum": 1.0}, "validation_status": {"type": "string", "description": "验证状态", "enum": ["validated", "pending", "rejected", "needs_review", "auto_validated"]}, "quality_score": {"type": "number", "description": "综合质量分数", "minimum": 0.0, "maximum": 1.0}}}, "usage_statistics": {"type": "object", "description": "使用统计", "properties": {"access_count": {"type": "integer", "description": "访问次数", "minimum": 0}, "last_accessed": {"type": "string", "format": "date-time", "description": "最后访问时间"}, "usage_frequency": {"type": "number", "description": "使用频率（次/天）", "minimum": 0.0}, "user_ratings": {"type": "array", "description": "用户评分", "items": {"type": "object", "properties": {"user_id": {"type": "string"}, "rating": {"type": "number", "minimum": 1.0, "maximum": 5.0}, "comment": {"type": "string", "maxLength": 500}, "timestamp": {"type": "string", "format": "date-time"}}, "required": ["user_id", "rating", "timestamp"]}}, "average_rating": {"type": "number", "description": "平均评分", "minimum": 1.0, "maximum": 5.0}, "popularity_score": {"type": "number", "description": "流行度分数", "minimum": 0.0, "maximum": 1.0}}}, "versioning_lineage": {"type": "object", "description": "版本控制和血缘关系", "properties": {"version": {"type": "string", "description": "当前版本号", "pattern": "^\\d+\\.\\d+\\.\\d+$"}, "previous_versions": {"type": "array", "description": "历史版本", "items": {"type": "object", "properties": {"version": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}, "author": {"type": "string"}, "changes": {"type": "string"}, "change_type": {"type": "string", "enum": ["create", "update", "merge", "split", "delete", "restore"]}}}}, "derived_from": {"type": "array", "description": "派生来源", "items": {"type": "string", "description": "源知识ID"}}, "derived_to": {"type": "array", "description": "派生目标", "items": {"type": "string", "description": "派生知识ID"}}, "related_knowledge": {"type": "array", "description": "相关知识", "items": {"type": "object", "properties": {"knowledge_id": {"type": "string"}, "relationship_type": {"type": "string", "enum": ["prerequisite", "follow_up", "alternative", "complementary", "contradictory"]}, "strength": {"type": "number", "minimum": 0.0, "maximum": 1.0}}, "required": ["knowledge_id", "relationship_type"]}}}, "required": ["version"]}, "provenance_info": {"type": "object", "description": "数据溯源信息", "properties": {"source": {"type": "string", "enum": ["manual_input", "automatic_extraction", "machine_learning", "expert_knowledge", "external_import", "user_contribution", "system_inference", "collaborative_editing", "data_mining", "web_scraping"]}, "source_reference": {"type": "string", "description": "来源引用或链接", "maxLength": 1000}, "author": {"type": "string", "description": "作者", "maxLength": 100}, "contributors": {"type": "array", "description": "贡献者列表", "items": {"type": "object", "properties": {"contributor_id": {"type": "string"}, "contributor_name": {"type": "string"}, "contribution_type": {"type": "string", "enum": ["author", "editor", "reviewer", "validator", "translator"]}, "contribution_date": {"type": "string", "format": "date-time"}}, "required": ["contributor_id", "contributor_name", "contribution_type"]}}, "publication_info": {"type": "object", "description": "发布信息", "properties": {"publisher": {"type": "string"}, "publication_date": {"type": "string", "format": "date"}, "isbn": {"type": "string"}, "doi": {"type": "string"}, "url": {"type": "string", "format": "uri"}}}, "license": {"type": "string", "description": "许可证信息", "enum": ["public_domain", "cc_by", "cc_by_sa", "cc_by_nc", "cc_by_nd", "proprietary", "custom"]}, "copyright": {"type": "string", "description": "版权信息", "maxLength": 200}}, "required": ["source"]}, "extensions": {"type": "object", "description": "扩展字段", "patternProperties": {"^ext_[a-zA-Z_][a-zA-Z0-9_]*$": {"description": "扩展字段，字段名必须以ext_开头"}}}}, "required": ["metadata_info", "knowledge_definition", "classification_taxonomy", "quality_assessment", "versioning_lineage", "provenance_info"], "additionalProperties": false}