{"$schema": "http://json-schema.org/draft-07/schema#", "title": "AGI实体层元数据标准", "description": "定义AGI系统中实体层的元数据结构和标准", "version": "1.0.0", "type": "object", "properties": {"metadata_info": {"type": "object", "description": "元数据基本信息", "properties": {"schema_version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "created_by": {"type": "string"}, "last_modified_by": {"type": "string"}}, "required": ["schema_version", "created_at", "created_by"]}, "entity_definition": {"type": "object", "description": "实体定义", "properties": {"entity_id": {"type": "string", "description": "实体唯一标识符", "pattern": "^entity_[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$"}, "entity_name": {"type": "string", "description": "实体名称", "minLength": 1, "maxLength": 255}, "entity_type": {"type": "string", "description": "实体类型", "enum": ["person", "organization", "location", "event", "object", "document", "concept_instance", "data_structure", "algorithm", "model", "system", "service", "resource", "custom"]}, "description": {"type": "string", "description": "实体描述", "maxLength": 2000}, "aliases": {"type": "array", "description": "实体别名", "items": {"type": "string", "maxLength": 255}, "maxItems": 20}, "category": {"type": "string", "description": "实体分类", "maxLength": 100}}, "required": ["entity_id", "entity_name", "entity_type"]}, "entity_attributes": {"type": "object", "description": "实体属性", "properties": {"properties": {"type": "object", "description": "实体属性集合", "patternProperties": {"^[a-zA-Z_][a-zA-Z0-9_]*$": {"type": "object", "properties": {"value": {"description": "属性值"}, "data_type": {"type": "string", "enum": ["string", "number", "boolean", "date", "array", "object"]}, "unit": {"type": "string", "description": "属性单位"}, "description": {"type": "string", "description": "属性描述"}, "required": {"type": "boolean", "description": "是否必需", "default": false}, "constraints": {"type": "object", "description": "属性约束", "properties": {"min_value": {"type": "number"}, "max_value": {"type": "number"}, "pattern": {"type": "string"}, "allowed_values": {"type": "array"}}}}, "required": ["value", "data_type"]}}}, "computed_properties": {"type": "object", "description": "计算属性", "patternProperties": {"^[a-zA-Z_][a-zA-Z0-9_]*$": {"type": "object", "properties": {"formula": {"type": "string", "description": "计算公式"}, "dependencies": {"type": "array", "description": "依赖属性", "items": {"type": "string"}}, "result_type": {"type": "string", "enum": ["string", "number", "boolean", "date", "array", "object"]}, "description": {"type": "string"}}, "required": ["formula", "result_type"]}}}}}, "classification_info": {"type": "object", "description": "分类信息", "properties": {"domain": {"type": "string", "description": "所属领域", "enum": ["general", "science", "technology", "medicine", "business", "education", "government", "entertainment", "sports", "custom"]}, "subdomain": {"type": "string", "description": "子领域", "maxLength": 100}, "tags": {"type": "array", "description": "标签", "items": {"type": "string", "maxLength": 50}, "maxItems": 30}, "classification_scheme": {"type": "string", "description": "分类体系", "enum": ["custom", "de<PERSON>y", "lcc", "udc", "domain_specific"]}, "classification_code": {"type": "string", "description": "分类代码", "maxLength": 50}}}, "temporal_info": {"type": "object", "description": "时间信息", "properties": {"existence_period": {"type": "object", "description": "存在期间", "properties": {"start_time": {"type": "string", "format": "date-time", "description": "开始时间"}, "end_time": {"type": "string", "format": "date-time", "description": "结束时间"}, "is_ongoing": {"type": "boolean", "description": "是否持续中", "default": true}}}, "temporal_granularity": {"type": "string", "description": "时间粒度", "enum": ["second", "minute", "hour", "day", "week", "month", "year", "decade", "century"]}, "temporal_precision": {"type": "string", "description": "时间精度", "enum": ["exact", "approximate", "estimated", "unknown"]}, "time_zone": {"type": "string", "description": "时区", "pattern": "^[A-Z]{3,4}$|^[+-]\\d{2}:\\d{2}$"}}}, "spatial_info": {"type": "object", "description": "空间信息", "properties": {"location": {"type": "object", "description": "位置信息", "properties": {"coordinates": {"type": "object", "description": "坐标", "properties": {"latitude": {"type": "number", "minimum": -90, "maximum": 90}, "longitude": {"type": "number", "minimum": -180, "maximum": 180}, "altitude": {"type": "number", "description": "海拔高度（米）"}}}, "address": {"type": "object", "description": "地址信息", "properties": {"country": {"type": "string", "maxLength": 100}, "state_province": {"type": "string", "maxLength": 100}, "city": {"type": "string", "maxLength": 100}, "street": {"type": "string", "maxLength": 200}, "postal_code": {"type": "string", "maxLength": 20}, "full_address": {"type": "string", "maxLength": 500}}}, "spatial_reference": {"type": "string", "description": "空间参考系统", "default": "WGS84"}}}, "spatial_extent": {"type": "object", "description": "空间范围", "properties": {"bounding_box": {"type": "object", "description": "边界框", "properties": {"min_latitude": {"type": "number", "minimum": -90, "maximum": 90}, "max_latitude": {"type": "number", "minimum": -90, "maximum": 90}, "min_longitude": {"type": "number", "minimum": -180, "maximum": 180}, "max_longitude": {"type": "number", "minimum": -180, "maximum": 180}}}, "area": {"type": "number", "description": "面积（平方米）", "minimum": 0}, "perimeter": {"type": "number", "description": "周长（米）", "minimum": 0}}}}}, "relationships": {"type": "object", "description": "关系信息", "properties": {"related_concepts": {"type": "array", "description": "相关概念", "items": {"type": "string", "pattern": "^concept_[a-f0-9-]+$"}}, "related_entities": {"type": "array", "description": "相关实体", "items": {"type": "string", "pattern": "^entity_[a-f0-9-]+$"}}, "parent_entities": {"type": "array", "description": "父实体", "items": {"type": "string", "pattern": "^entity_[a-f0-9-]+$"}}, "child_entities": {"type": "array", "description": "子实体", "items": {"type": "string", "pattern": "^entity_[a-f0-9-]+$"}}}}, "quality_metrics": {"type": "object", "description": "质量度量", "properties": {"completeness": {"type": "number", "minimum": 0.0, "maximum": 1.0, "description": "完整性"}, "accuracy": {"type": "number", "minimum": 0.0, "maximum": 1.0, "description": "准确性"}, "consistency": {"type": "number", "minimum": 0.0, "maximum": 1.0, "description": "一致性"}, "timeliness": {"type": "number", "minimum": 0.0, "maximum": 1.0, "description": "时效性"}, "relevance": {"type": "number", "minimum": 0.0, "maximum": 1.0, "description": "相关性"}, "confidence": {"type": "number", "minimum": 0.0, "maximum": 1.0, "description": "置信度"}}}, "provenance_info": {"type": "object", "description": "数据溯源信息", "properties": {"source": {"type": "string", "description": "数据来源", "enum": ["manual_input", "automatic_extraction", "machine_learning", "expert_knowledge", "external_import", "user_feedback", "system_inference", "sensor_data", "database_import"]}, "source_reference": {"type": "string", "description": "来源引用", "maxLength": 500}, "extraction_method": {"type": "string", "description": "提取方法", "maxLength": 200}, "validation_status": {"type": "string", "description": "验证状态", "enum": ["validated", "pending", "rejected", "needs_review"]}, "validator": {"type": "string", "description": "验证者", "maxLength": 100}, "validation_date": {"type": "string", "format": "date-time", "description": "验证日期"}, "version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$", "description": "版本号"}, "change_log": {"type": "array", "description": "变更日志", "items": {"type": "object", "properties": {"version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$"}, "timestamp": {"type": "string", "format": "date-time"}, "author": {"type": "string"}, "changes": {"type": "string"}, "change_type": {"type": "string", "enum": ["create", "update", "delete", "merge", "split"]}}, "required": ["version", "timestamp", "author", "changes", "change_type"]}}}, "required": ["source", "version"]}, "extensions": {"type": "object", "description": "扩展字段", "patternProperties": {"^ext_[a-zA-Z_][a-zA-Z0-9_]*$": {"description": "扩展字段，字段名必须以ext_开头"}}}}, "required": ["metadata_info", "entity_definition", "quality_metrics", "provenance_info"], "additionalProperties": false}