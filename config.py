"""
AGI Knowledge Graph System Configuration
"""
import os
from typing import Optional
from pydantic import BaseSettings, Field


class Settings(BaseSettings):
    """Application settings"""
    
    # Application
    app_name: str = "AGI Knowledge Graph System"
    app_version: str = "1.0.0"
    debug: bool = Field(default=False, env="DEBUG")
    
    # Database
    database_url: str = Field(default="sqlite:///./agi_system.db", env="DATABASE_URL")
    
    # Neo4j Graph Database
    neo4j_uri: str = Field(default="bolt://localhost:7687", env="NEO4J_URI")
    neo4j_user: str = Field(default="neo4j", env="NEO4J_USER")
    neo4j_password: str = Field(default="password", env="NEO4J_PASSWORD")
    
    # OpenAI API
    openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    openai_model: str = Field(default="gpt-3.5-turbo", env="OPENAI_MODEL")
    
    # API Settings
    api_host: str = Field(default="0.0.0.0", env="API_HOST")
    api_port: int = Field(default=8000, env="API_PORT")
    
    # Logging
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_file: str = Field(default="logs/agi_system.log", env="LOG_FILE")
    
    # Knowledge Graph Settings
    max_graph_depth: int = Field(default=5, env="MAX_GRAPH_DEPTH")
    similarity_threshold: float = Field(default=0.7, env="SIMILARITY_THRESHOLD")
    
    # Learning Settings
    learning_rate: float = Field(default=0.001, env="LEARNING_RATE")
    feedback_weight: float = Field(default=0.8, env="FEEDBACK_WEIGHT")
    
    # Context Settings
    context_window_size: int = Field(default=100, env="CONTEXT_WINDOW_SIZE")
    context_update_interval: int = Field(default=60, env="CONTEXT_UPDATE_INTERVAL")  # seconds
    
    # Security Settings
    secret_key: str = Field(default="your-secret-key-here", env="SECRET_KEY")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()
