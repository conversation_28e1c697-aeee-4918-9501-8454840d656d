"""
语义网络模块 - 整合概念、实体和关系的语义网络
"""
from typing import Dict, Any, List, Optional, Set, Tuple
from datetime import datetime
import networkx as nx
from collections import defaultdict

from ..entities.concept import Concept, ConceptHierarchy
from ..entities.entity import Entity, EntityManager
from .relationship import Relationship, RelationshipManager, RelationshipType
from .ontology import Ontology, OntologyManager


class SemanticNetwork:
    """语义网络 - 整合概念、实体和关系的知识图谱"""
    
    def __init__(self):
        # 核心组件
        self.concept_hierarchy = ConceptHierarchy()
        self.entity_manager = EntityManager()
        self.relationship_manager = RelationshipManager()
        self.ontology_manager = OntologyManager()
        
        # NetworkX图用于复杂分析
        self.graph = nx.MultiDiGraph()
        
        # 缓存
        self._similarity_cache: Dict[Tuple[str, str], float] = {}
        self._path_cache: Dict[Tuple[str, str], List[List[str]]] = {}
    
    def add_concept(self, concept: Concept) -> None:
        """添加概念"""
        self.concept_hierarchy.add_concept(concept)
        self.graph.add_node(concept.id, type="concept", data=concept)
        self._clear_cache()
    
    def add_entity(self, entity: Entity) -> None:
        """添加实体"""
        self.entity_manager.add_entity(entity)
        self.graph.add_node(entity.id, type="entity", data=entity)
        self._clear_cache()
    
    def add_relationship(self, relationship: Relationship) -> None:
        """添加关系"""
        self.relationship_manager.add_relationship(relationship)
        self.graph.add_edge(
            relationship.source_id,
            relationship.target_id,
            key=relationship.id,
            type="relationship",
            data=relationship
        )
        self._clear_cache()
    
    def get_node(self, node_id: str) -> Optional[Any]:
        """获取节点（概念或实体）"""
        # 先尝试获取概念
        concept = self.concept_hierarchy.get_concept(node_id)
        if concept:
            return concept
        
        # 再尝试获取实体
        entity = self.entity_manager.get_entity(node_id)
        if entity:
            return entity
        
        return None
    
    def find_semantic_similarity(self, node_id1: str, node_id2: str) -> float:
        """计算两个节点的语义相似度"""
        cache_key = (min(node_id1, node_id2), max(node_id1, node_id2))
        
        if cache_key in self._similarity_cache:
            return self._similarity_cache[cache_key]
        
        similarity = self._calculate_similarity(node_id1, node_id2)
        self._similarity_cache[cache_key] = similarity
        
        return similarity
    
    def _calculate_similarity(self, node_id1: str, node_id2: str) -> float:
        """计算语义相似度的内部方法"""
        if node_id1 == node_id2:
            return 1.0
        
        node1 = self.get_node(node_id1)
        node2 = self.get_node(node_id2)
        
        if not node1 or not node2:
            return 0.0
        
        # 基于路径的相似度
        path_similarity = self._calculate_path_similarity(node_id1, node_id2)
        
        # 基于共同邻居的相似度
        neighbor_similarity = self._calculate_neighbor_similarity(node_id1, node_id2)
        
        # 基于属性的相似度
        attribute_similarity = self._calculate_attribute_similarity(node1, node2)
        
        # 加权平均
        total_similarity = (
            0.4 * path_similarity +
            0.3 * neighbor_similarity +
            0.3 * attribute_similarity
        )
        
        return min(1.0, max(0.0, total_similarity))
    
    def _calculate_path_similarity(self, node_id1: str, node_id2: str) -> float:
        """基于路径距离的相似度"""
        try:
            if self.graph.has_node(node_id1) and self.graph.has_node(node_id2):
                # 使用无向图计算最短路径
                undirected_graph = self.graph.to_undirected()
                shortest_path_length = nx.shortest_path_length(
                    undirected_graph, node_id1, node_id2
                )
                # 路径越短，相似度越高
                return 1.0 / (1.0 + shortest_path_length)
        except nx.NetworkXNoPath:
            pass
        
        return 0.0
    
    def _calculate_neighbor_similarity(self, node_id1: str, node_id2: str) -> float:
        """基于共同邻居的相似度"""
        neighbors1 = set(self.graph.neighbors(node_id1))
        neighbors2 = set(self.graph.neighbors(node_id2))
        
        if not neighbors1 and not neighbors2:
            return 0.0
        
        intersection = len(neighbors1.intersection(neighbors2))
        union = len(neighbors1.union(neighbors2))
        
        return intersection / union if union > 0 else 0.0
    
    def _calculate_attribute_similarity(self, node1: Any, node2: Any) -> float:
        """基于属性的相似度"""
        # 检查节点类型
        if type(node1) != type(node2):
            return 0.0
        
        # 对于概念节点
        if isinstance(node1, Concept) and isinstance(node2, Concept):
            similarity = 0.0
            
            # 概念类型相似度
            if node1.concept_type == node2.concept_type:
                similarity += 0.3
            
            # 领域相似度
            if node1.domain and node2.domain and node1.domain == node2.domain:
                similarity += 0.3
            
            # 抽象层级相似度
            level_diff = abs(node1.abstraction_level - node2.abstraction_level)
            level_similarity = 1.0 / (1.0 + level_diff)
            similarity += 0.4 * level_similarity
            
            return similarity
        
        # 对于实体节点
        elif isinstance(node1, Entity) and isinstance(node2, Entity):
            similarity = 0.0
            
            # 实体类型相似度
            if node1.entity_type == node2.entity_type:
                similarity += 0.4
            
            # 概念相似度
            if node1.concept_id and node2.concept_id:
                if node1.concept_id == node2.concept_id:
                    similarity += 0.3
                else:
                    concept_sim = self.find_semantic_similarity(
                        node1.concept_id, node2.concept_id
                    )
                    similarity += 0.3 * concept_sim
            
            # 位置相似度
            if node1.location and node2.location and node1.location == node2.location:
                similarity += 0.3
            
            return similarity
        
        return 0.0
    
    def find_related_nodes(self, node_id: str, max_distance: int = 2, 
                          min_similarity: float = 0.5) -> List[Tuple[str, float]]:
        """查找相关节点"""
        related_nodes = []
        
        # 获取邻居节点
        neighbors = self.relationship_manager.get_neighbors(node_id, max_distance)
        
        for neighbor_id in neighbors:
            similarity = self.find_semantic_similarity(node_id, neighbor_id)
            if similarity >= min_similarity:
                related_nodes.append((neighbor_id, similarity))
        
        # 按相似度排序
        related_nodes.sort(key=lambda x: x[1], reverse=True)
        
        return related_nodes
    
    def get_context_subgraph(self, node_id: str, radius: int = 2) -> nx.MultiDiGraph:
        """获取节点的上下文子图"""
        if not self.graph.has_node(node_id):
            return nx.MultiDiGraph()
        
        # 获取指定半径内的所有节点
        subgraph_nodes = set([node_id])
        current_level = set([node_id])
        
        for _ in range(radius):
            next_level = set()
            for node in current_level:
                # 添加邻居节点
                neighbors = set(self.graph.neighbors(node))
                predecessors = set(self.graph.predecessors(node))
                next_level.update(neighbors.union(predecessors))
            
            subgraph_nodes.update(next_level)
            current_level = next_level
        
        # 创建子图
        subgraph = self.graph.subgraph(subgraph_nodes).copy()
        
        return subgraph
    
    def analyze_centrality(self, node_id: str) -> Dict[str, float]:
        """分析节点的中心性"""
        if not self.graph.has_node(node_id):
            return {}
        
        centrality_measures = {}
        
        try:
            # 度中心性
            degree_centrality = nx.degree_centrality(self.graph)
            centrality_measures["degree"] = degree_centrality.get(node_id, 0.0)
            
            # 接近中心性
            closeness_centrality = nx.closeness_centrality(self.graph)
            centrality_measures["closeness"] = closeness_centrality.get(node_id, 0.0)
            
            # 介数中心性
            betweenness_centrality = nx.betweenness_centrality(self.graph)
            centrality_measures["betweenness"] = betweenness_centrality.get(node_id, 0.0)
            
            # 特征向量中心性
            try:
                eigenvector_centrality = nx.eigenvector_centrality(self.graph, max_iter=1000)
                centrality_measures["eigenvector"] = eigenvector_centrality.get(node_id, 0.0)
            except nx.PowerIterationFailedConvergence:
                centrality_measures["eigenvector"] = 0.0
            
        except Exception as e:
            print(f"Error calculating centrality: {e}")
        
        return centrality_measures
    
    def detect_communities(self) -> List[Set[str]]:
        """检测社区结构"""
        try:
            # 转换为无向图进行社区检测
            undirected_graph = self.graph.to_undirected()
            
            # 使用贪婪模块度优化算法
            communities = nx.community.greedy_modularity_communities(undirected_graph)
            
            return [set(community) for community in communities]
        
        except Exception as e:
            print(f"Error detecting communities: {e}")
            return []
    
    def get_knowledge_summary(self) -> Dict[str, Any]:
        """获取知识图谱摘要"""
        return {
            "total_nodes": self.graph.number_of_nodes(),
            "total_edges": self.graph.number_of_edges(),
            "concepts_count": len(self.concept_hierarchy.concepts),
            "entities_count": len(self.entity_manager.entities),
            "relationships_count": len(self.relationship_manager.relationships),
            "ontologies_count": len(self.ontology_manager.ontologies),
            "graph_density": nx.density(self.graph),
            "is_connected": nx.is_weakly_connected(self.graph),
            "communities_count": len(self.detect_communities())
        }
    
    def _clear_cache(self) -> None:
        """清除缓存"""
        self._similarity_cache.clear()
        self._path_cache.clear()
