{"$schema": "http://json-schema.org/draft-07/schema#", "title": "AGI反馈与学习层元数据标准", "description": "定义AGI系统中反馈与学习层的元数据结构和标准", "version": "1.0.0", "type": "object", "properties": {"metadata_info": {"type": "object", "description": "元数据基本信息", "properties": {"schema_version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "created_by": {"type": "string"}, "last_modified_by": {"type": "string"}}, "required": ["schema_version", "created_at", "created_by"]}, "feedback_definition": {"type": "object", "description": "反馈定义", "properties": {"feedback_id": {"type": "string", "description": "反馈唯一标识符", "pattern": "^feedback_[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$"}, "feedback_type": {"type": "string", "description": "反馈类型", "enum": ["explicit", "implicit", "positive", "negative", "corrective", "reinforcement", "evaluative", "informational", "behavioral", "performance"]}, "feedback_source": {"type": "string", "description": "反馈来源", "enum": ["human_user", "expert_evaluator", "peer_system", "automated_metric", "environment", "self_evaluation", "crowd_sourcing", "sensor_data", "performance_monitor"]}, "feedback_content": {"description": "反馈内容，可以是文本、数值或结构化数据"}, "feedback_format": {"type": "string", "description": "反馈格式", "enum": ["text", "numeric_score", "binary_rating", "multi_choice", "ranking", "structured_data", "multimedia", "behavioral_signal"]}, "target_component": {"type": "string", "description": "目标组件", "enum": ["overall_system", "specific_task", "knowledge_item", "reasoning_process", "decision_making", "user_interaction", "output_quality", "response_time"]}, "context_id": {"type": "string", "description": "相关情境ID"}}, "required": ["feedback_id", "feedback_type", "feedback_source", "feedback_content", "target_component"]}, "feedback_characteristics": {"type": "object", "description": "反馈特征", "properties": {"timeliness": {"type": "string", "description": "时效性", "enum": ["immediate", "delayed", "batch", "periodic"]}, "granularity": {"type": "string", "description": "粒度", "enum": ["coarse", "medium", "fine", "very_fine"]}, "specificity": {"type": "string", "description": "具体性", "enum": ["general", "specific", "detailed", "precise"]}, "objectivity": {"type": "string", "description": "客观性", "enum": ["objective", "subjective", "mixed"]}, "reliability": {"type": "number", "description": "可靠性", "minimum": 0.0, "maximum": 1.0}, "confidence": {"type": "number", "description": "置信度", "minimum": 0.0, "maximum": 1.0}, "bias_indicators": {"type": "array", "description": "偏见指标", "items": {"type": "object", "properties": {"bias_type": {"type": "string", "enum": ["confirmation", "selection", "cultural", "temporal", "personal"]}, "bias_score": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "mitigation_applied": {"type": "boolean"}}}}}}, "feedback_metrics": {"type": "object", "description": "反馈度量", "properties": {"quantitative_metrics": {"type": "object", "description": "定量指标", "properties": {"accuracy_score": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "satisfaction_rating": {"type": "number", "minimum": 1.0, "maximum": 10.0}, "completion_rate": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "response_time": {"type": "number", "description": "响应时间（毫秒）", "minimum": 0}, "error_rate": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "efficiency_score": {"type": "number", "minimum": 0.0, "maximum": 1.0}}}, "qualitative_metrics": {"type": "object", "description": "定性指标", "properties": {"usability_assessment": {"type": "string", "enum": ["excellent", "good", "fair", "poor", "very_poor"]}, "clarity_rating": {"type": "string", "enum": ["very_clear", "clear", "somewhat_clear", "unclear", "very_unclear"]}, "relevance_rating": {"type": "string", "enum": ["highly_relevant", "relevant", "somewhat_relevant", "irrelevant", "completely_irrelevant"]}, "helpfulness_rating": {"type": "string", "enum": ["very_helpful", "helpful", "somewhat_helpful", "not_helpful", "counterproductive"]}}}, "behavioral_metrics": {"type": "object", "description": "行为指标", "properties": {"interaction_duration": {"type": "number", "description": "交互持续时间（秒）"}, "click_through_rate": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "abandonment_rate": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "retry_count": {"type": "integer", "minimum": 0}, "follow_up_questions": {"type": "integer", "minimum": 0}}}}}, "learning_process": {"type": "object", "description": "学习过程", "properties": {"learning_algorithm": {"type": "object", "description": "学习算法", "properties": {"algorithm_type": {"type": "string", "enum": ["supervised_learning", "unsupervised_learning", "reinforcement_learning", "semi_supervised_learning", "transfer_learning", "meta_learning", "online_learning", "incremental_learning"]}, "algorithm_name": {"type": "string", "description": "具体算法名称"}, "hyperparameters": {"type": "object", "description": "超参数配置"}, "learning_rate": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "convergence_criteria": {"type": "object", "description": "收敛标准"}}, "required": ["algorithm_type"]}, "training_data": {"type": "object", "description": "训练数据", "properties": {"data_source": {"type": "string", "enum": ["feedback_history", "expert_annotations", "synthetic_data", "external_dataset", "mixed"]}, "data_size": {"type": "integer", "minimum": 0, "description": "数据集大小"}, "data_quality": {"type": "object", "properties": {"completeness": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "accuracy": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "consistency": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "timeliness": {"type": "number", "minimum": 0.0, "maximum": 1.0}}}, "preprocessing_steps": {"type": "array", "items": {"type": "string"}}}}, "model_updates": {"type": "array", "description": "模型更新记录", "items": {"type": "object", "properties": {"update_id": {"type": "string"}, "update_timestamp": {"type": "string", "format": "date-time"}, "update_type": {"type": "string", "enum": ["parameter_update", "architecture_change", "feature_addition", "bug_fix", "performance_optimization"]}, "trigger_condition": {"type": "string", "description": "触发更新的条件"}, "performance_before": {"type": "object", "description": "更新前性能"}, "performance_after": {"type": "object", "description": "更新后性能"}, "validation_results": {"type": "object", "description": "验证结果"}}, "required": ["update_id", "update_timestamp", "update_type"]}}}}, "adaptation_mechanisms": {"type": "object", "description": "适应机制", "properties": {"adaptation_strategies": {"type": "array", "description": "适应策略", "items": {"type": "object", "properties": {"strategy_id": {"type": "string"}, "strategy_name": {"type": "string"}, "strategy_type": {"type": "string", "enum": ["reactive", "proactive", "predictive", "adaptive"]}, "trigger_conditions": {"type": "array", "items": {"type": "string"}}, "adaptation_actions": {"type": "array", "items": {"type": "string"}}, "effectiveness_score": {"type": "number", "minimum": 0.0, "maximum": 1.0}}, "required": ["strategy_id", "strategy_name", "strategy_type"]}}, "personalization": {"type": "object", "description": "个性化", "properties": {"user_modeling": {"type": "object", "properties": {"preference_learning": {"type": "boolean"}, "behavior_modeling": {"type": "boolean"}, "skill_assessment": {"type": "boolean"}, "context_adaptation": {"type": "boolean"}}}, "customization_level": {"type": "string", "enum": ["none", "basic", "moderate", "advanced", "full"]}, "adaptation_scope": {"type": "array", "items": {"type": "string", "enum": ["interface", "content", "interaction_style", "response_format", "difficulty_level"]}}}}, "continuous_improvement": {"type": "object", "description": "持续改进", "properties": {"improvement_cycles": {"type": "array", "items": {"type": "object", "properties": {"cycle_id": {"type": "string"}, "start_date": {"type": "string", "format": "date"}, "end_date": {"type": "string", "format": "date"}, "improvement_goals": {"type": "array", "items": {"type": "string"}}, "achieved_improvements": {"type": "array", "items": {"type": "string"}}, "success_metrics": {"type": "object"}}}}, "feedback_integration_rate": {"type": "number", "minimum": 0.0, "maximum": 1.0, "description": "反馈集成率"}, "learning_velocity": {"type": "number", "minimum": 0.0, "description": "学习速度"}}}}}, "evaluation_framework": {"type": "object", "description": "评估框架", "properties": {"evaluation_criteria": {"type": "array", "description": "评估标准", "items": {"type": "object", "properties": {"criterion_id": {"type": "string"}, "criterion_name": {"type": "string"}, "description": {"type": "string"}, "measurement_method": {"type": "string"}, "weight": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "target_value": {"type": "number"}, "current_value": {"type": "number"}}, "required": ["criterion_id", "criterion_name", "measurement_method"]}}, "validation_methods": {"type": "array", "description": "验证方法", "items": {"type": "object", "properties": {"method_name": {"type": "string"}, "method_type": {"type": "string", "enum": ["cross_validation", "holdout", "bootstrap", "a_b_testing", "expert_review", "user_study"]}, "validation_data": {"type": "object", "description": "验证数据信息"}, "results": {"type": "object", "description": "验证结果"}}, "required": ["method_name", "method_type"]}}, "performance_benchmarks": {"type": "array", "description": "性能基准", "items": {"type": "object", "properties": {"benchmark_name": {"type": "string"}, "benchmark_type": {"type": "string", "enum": ["accuracy", "speed", "efficiency", "robustness", "scalability"]}, "baseline_score": {"type": "number"}, "current_score": {"type": "number"}, "target_score": {"type": "number"}, "improvement_rate": {"type": "number"}}, "required": ["benchmark_name", "benchmark_type", "current_score"]}}}}, "privacy_ethics": {"type": "object", "description": "隐私和伦理", "properties": {"data_privacy": {"type": "object", "properties": {"anonymization_level": {"type": "string", "enum": ["none", "pseudonymization", "anonymization", "differential_privacy"]}, "consent_status": {"type": "string", "enum": ["explicit_consent", "implied_consent", "opt_out", "no_consent_required"]}, "data_retention_policy": {"type": "string", "description": "数据保留政策"}, "sharing_permissions": {"type": "array", "items": {"type": "string"}}}}, "ethical_considerations": {"type": "object", "properties": {"fairness_assessment": {"type": "object", "properties": {"bias_detection": {"type": "boolean"}, "fairness_metrics": {"type": "array", "items": {"type": "string"}}, "mitigation_strategies": {"type": "array", "items": {"type": "string"}}}}, "transparency_level": {"type": "string", "enum": ["opaque", "limited", "moderate", "high", "full"]}, "explainability_support": {"type": "boolean"}}}}}, "quality_assurance": {"type": "object", "description": "质量保证", "properties": {"data_quality_checks": {"type": "array", "items": {"type": "object", "properties": {"check_type": {"type": "string", "enum": ["completeness", "accuracy", "consistency", "validity", "timeliness"]}, "check_result": {"type": "string", "enum": ["passed", "failed", "warning"]}, "quality_score": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "issues_found": {"type": "array", "items": {"type": "string"}}}}}, "feedback_validation": {"type": "object", "properties": {"validation_status": {"type": "string", "enum": ["validated", "pending", "rejected", "needs_review"]}, "validator_id": {"type": "string"}, "validation_timestamp": {"type": "string", "format": "date-time"}, "validation_notes": {"type": "string"}}}, "reliability_metrics": {"type": "object", "properties": {"consistency_score": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "stability_score": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "robustness_score": {"type": "number", "minimum": 0.0, "maximum": 1.0}}}}}, "provenance_info": {"type": "object", "description": "数据溯源信息", "properties": {"feedback_origin": {"type": "string", "description": "反馈来源"}, "collection_context": {"type": "string", "description": "收集上下文"}, "processing_pipeline": {"type": "array", "description": "处理流水线", "items": {"type": "object", "properties": {"step_name": {"type": "string"}, "step_type": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}, "parameters": {"type": "object"}}}}, "version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$"}}, "required": ["feedback_origin", "version"]}, "extensions": {"type": "object", "description": "扩展字段", "patternProperties": {"^ext_[a-zA-Z_][a-zA-Z0-9_]*$": {"description": "扩展字段，字段名必须以ext_开头"}}}}, "required": ["metadata_info", "feedback_definition", "feedback_characteristics", "learning_process", "quality_assurance", "provenance_info"], "additionalProperties": false}