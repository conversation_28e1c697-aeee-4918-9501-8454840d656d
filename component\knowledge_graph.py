"""
知识图谱组件 - 基于Neo4j图数据库
用于高效处理AGI系统中的关联查询和知识推理
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from neo4j import GraphDatabase, Driver, Session
import json
import uuid
from datetime import datetime

logger = logging.getLogger(__name__)


@dataclass
class GraphNode:
    """图节点数据结构"""
    id: str
    labels: List[str]
    properties: Dict[str, Any]


@dataclass
class GraphRelationship:
    """图关系数据结构"""
    id: str
    start_node_id: str
    end_node_id: str
    type: str
    properties: Dict[str, Any]


@dataclass
class GraphPath:
    """图路径数据结构"""
    nodes: List[GraphNode]
    relationships: List[GraphRelationship]
    length: int


class KnowledgeGraphManager:
    """知识图谱管理器"""
    
    def __init__(self, uri: str, username: str, password: str, database: str = "neo4j"):
        """
        初始化知识图谱管理器
        
        Args:
            uri: Neo4j数据库URI
            username: 用户名
            password: 密码
            database: 数据库名称
        """
        self.uri = uri
        self.username = username
        self.password = password
        self.database = database
        self.driver: Optional[Driver] = None
        
    def connect(self) -> bool:
        """建立数据库连接"""
        try:
            self.driver = GraphDatabase.driver(
                self.uri, 
                auth=(self.username, self.password)
            )
            # 测试连接
            with self.driver.session(database=self.database) as session:
                session.run("RETURN 1")
            logger.info(f"Successfully connected to Neo4j at {self.uri}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to Neo4j: {e}")
            return False
    
    def disconnect(self):
        """关闭数据库连接"""
        if self.driver:
            self.driver.close()
            logger.info("Disconnected from Neo4j")
    
    def create_concept_node(self, concept_data: Dict[str, Any]) -> str:
        """
        创建概念节点
        
        Args:
            concept_data: 概念数据，包含concept_id, concept_name, concept_type等
            
        Returns:
            创建的节点ID
        """
        with self.driver.session(database=self.database) as session:
            query = """
            CREATE (c:Concept {
                concept_id: $concept_id,
                concept_name: $concept_name,
                concept_type: $concept_type,
                description: $description,
                domain: $domain,
                abstraction_level: $abstraction_level,
                created_at: $created_at,
                updated_at: $updated_at
            })
            RETURN c.concept_id as id
            """
            
            result = session.run(query, {
                'concept_id': concept_data.get('concept_id', str(uuid.uuid4())),
                'concept_name': concept_data.get('concept_name'),
                'concept_type': concept_data.get('concept_type'),
                'description': concept_data.get('description', ''),
                'domain': concept_data.get('domain', 'general'),
                'abstraction_level': concept_data.get('abstraction_level', 0),
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            })
            
            record = result.single()
            node_id = record['id'] if record else None
            logger.info(f"Created concept node: {node_id}")
            return node_id
    
    def create_entity_node(self, entity_data: Dict[str, Any]) -> str:
        """
        创建实体节点
        
        Args:
            entity_data: 实体数据
            
        Returns:
            创建的节点ID
        """
        with self.driver.session(database=self.database) as session:
            query = """
            CREATE (e:Entity {
                entity_id: $entity_id,
                entity_name: $entity_name,
                entity_type: $entity_type,
                description: $description,
                properties: $properties,
                created_at: $created_at,
                updated_at: $updated_at
            })
            RETURN e.entity_id as id
            """
            
            result = session.run(query, {
                'entity_id': entity_data.get('entity_id', str(uuid.uuid4())),
                'entity_name': entity_data.get('entity_name'),
                'entity_type': entity_data.get('entity_type'),
                'description': entity_data.get('description', ''),
                'properties': json.dumps(entity_data.get('properties', {})),
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            })
            
            record = result.single()
            node_id = record['id'] if record else None
            logger.info(f"Created entity node: {node_id}")
            return node_id
    
    def create_relationship(self, start_id: str, end_id: str, 
                          relationship_type: str, properties: Dict[str, Any] = None) -> str:
        """
        创建关系
        
        Args:
            start_id: 起始节点ID
            end_id: 结束节点ID
            relationship_type: 关系类型
            properties: 关系属性
            
        Returns:
            关系ID
        """
        if properties is None:
            properties = {}
            
        with self.driver.session(database=self.database) as session:
            query = f"""
            MATCH (a), (b)
            WHERE a.concept_id = $start_id OR a.entity_id = $start_id
            AND b.concept_id = $end_id OR b.entity_id = $end_id
            CREATE (a)-[r:{relationship_type} {{
                relationship_id: $relationship_id,
                strength: $strength,
                confidence: $confidence,
                created_at: $created_at,
                properties: $properties
            }}]->(b)
            RETURN r.relationship_id as id
            """
            
            relationship_id = str(uuid.uuid4())
            result = session.run(query, {
                'start_id': start_id,
                'end_id': end_id,
                'relationship_id': relationship_id,
                'strength': properties.get('strength', 1.0),
                'confidence': properties.get('confidence', 1.0),
                'created_at': datetime.now().isoformat(),
                'properties': json.dumps(properties)
            })
            
            record = result.single()
            rel_id = record['id'] if record else None
            logger.info(f"Created relationship: {rel_id}")
            return rel_id
    
    def find_shortest_path(self, start_id: str, end_id: str, 
                          max_depth: int = 5) -> Optional[GraphPath]:
        """
        查找最短路径
        
        Args:
            start_id: 起始节点ID
            end_id: 结束节点ID
            max_depth: 最大搜索深度
            
        Returns:
            最短路径或None
        """
        with self.driver.session(database=self.database) as session:
            query = """
            MATCH (start), (end)
            WHERE start.concept_id = $start_id OR start.entity_id = $start_id
            AND end.concept_id = $end_id OR end.entity_id = $end_id
            MATCH path = shortestPath((start)-[*1..%d]-(end))
            RETURN path
            """ % max_depth
            
            result = session.run(query, {
                'start_id': start_id,
                'end_id': end_id
            })
            
            record = result.single()
            if record:
                path = record['path']
                return self._convert_path_to_graph_path(path)
            return None
    
    def find_related_concepts(self, concept_id: str, 
                            relationship_types: List[str] = None,
                            max_depth: int = 2) -> List[GraphNode]:
        """
        查找相关概念
        
        Args:
            concept_id: 概念ID
            relationship_types: 关系类型过滤
            max_depth: 最大搜索深度
            
        Returns:
            相关概念列表
        """
        with self.driver.session(database=self.database) as session:
            if relationship_types:
                rel_filter = "|".join(relationship_types)
                query = f"""
                MATCH (c:Concept)-[r:{rel_filter}*1..{max_depth}]-(related:Concept)
                WHERE c.concept_id = $concept_id
                RETURN DISTINCT related
                """
            else:
                query = f"""
                MATCH (c:Concept)-[*1..{max_depth}]-(related:Concept)
                WHERE c.concept_id = $concept_id
                RETURN DISTINCT related
                """
            
            result = session.run(query, {'concept_id': concept_id})
            
            related_concepts = []
            for record in result:
                node = record['related']
                related_concepts.append(self._convert_node_to_graph_node(node))
            
            logger.info(f"Found {len(related_concepts)} related concepts for {concept_id}")
            return related_concepts
    
    def semantic_search(self, query_text: str, node_types: List[str] = None,
                       limit: int = 10) -> List[GraphNode]:
        """
        语义搜索
        
        Args:
            query_text: 查询文本
            node_types: 节点类型过滤
            limit: 结果限制
            
        Returns:
            匹配的节点列表
        """
        with self.driver.session(database=self.database) as session:
            if node_types:
                label_filter = "|".join(node_types)
                query = f"""
                MATCH (n:{label_filter})
                WHERE n.concept_name CONTAINS $query_text 
                   OR n.entity_name CONTAINS $query_text
                   OR n.description CONTAINS $query_text
                RETURN n
                ORDER BY 
                    CASE 
                        WHEN n.concept_name = $query_text OR n.entity_name = $query_text THEN 1
                        WHEN n.concept_name STARTS WITH $query_text OR n.entity_name STARTS WITH $query_text THEN 2
                        ELSE 3
                    END
                LIMIT $limit
                """
            else:
                query = """
                MATCH (n)
                WHERE n.concept_name CONTAINS $query_text 
                   OR n.entity_name CONTAINS $query_text
                   OR n.description CONTAINS $query_text
                RETURN n
                ORDER BY 
                    CASE 
                        WHEN n.concept_name = $query_text OR n.entity_name = $query_text THEN 1
                        WHEN n.concept_name STARTS WITH $query_text OR n.entity_name STARTS WITH $query_text THEN 2
                        ELSE 3
                    END
                LIMIT $limit
                """
            
            result = session.run(query, {
                'query_text': query_text,
                'limit': limit
            })
            
            nodes = []
            for record in result:
                node = record['n']
                nodes.append(self._convert_node_to_graph_node(node))
            
            logger.info(f"Semantic search found {len(nodes)} nodes for '{query_text}'")
            return nodes
    
    def get_node_statistics(self) -> Dict[str, int]:
        """获取节点统计信息"""
        with self.driver.session(database=self.database) as session:
            query = """
            MATCH (n)
            RETURN labels(n) as labels, count(n) as count
            """
            
            result = session.run(query)
            stats = {}
            
            for record in result:
                labels = record['labels']
                count = record['count']
                for label in labels:
                    stats[label] = stats.get(label, 0) + count
            
            return stats
    
    def get_relationship_statistics(self) -> Dict[str, int]:
        """获取关系统计信息"""
        with self.driver.session(database=self.database) as session:
            query = """
            MATCH ()-[r]->()
            RETURN type(r) as relationship_type, count(r) as count
            """
            
            result = session.run(query)
            stats = {}
            
            for record in result:
                rel_type = record['relationship_type']
                count = record['count']
                stats[rel_type] = count
            
            return stats
    
    def _convert_node_to_graph_node(self, neo4j_node) -> GraphNode:
        """将Neo4j节点转换为GraphNode"""
        return GraphNode(
            id=neo4j_node.get('concept_id') or neo4j_node.get('entity_id'),
            labels=list(neo4j_node.labels),
            properties=dict(neo4j_node)
        )
    
    def _convert_path_to_graph_path(self, neo4j_path) -> GraphPath:
        """将Neo4j路径转换为GraphPath"""
        nodes = [self._convert_node_to_graph_node(node) for node in neo4j_path.nodes]
        relationships = []
        
        for rel in neo4j_path.relationships:
            relationships.append(GraphRelationship(
                id=rel.get('relationship_id'),
                start_node_id=rel.start_node.get('concept_id') or rel.start_node.get('entity_id'),
                end_node_id=rel.end_node.get('concept_id') or rel.end_node.get('entity_id'),
                type=rel.type,
                properties=dict(rel)
            ))
        
        return GraphPath(
            nodes=nodes,
            relationships=relationships,
            length=len(relationships)
        )


class KnowledgeGraphService:
    """知识图谱服务类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化知识图谱服务
        
        Args:
            config: 配置信息，包含数据库连接参数
        """
        self.config = config
        self.manager = KnowledgeGraphManager(
            uri=config['neo4j']['uri'],
            username=config['neo4j']['username'],
            password=config['neo4j']['password'],
            database=config['neo4j'].get('database', 'neo4j')
        )
        
    def initialize(self) -> bool:
        """初始化服务"""
        return self.manager.connect()
    
    def shutdown(self):
        """关闭服务"""
        self.manager.disconnect()
    
    def add_knowledge(self, knowledge_data: Dict[str, Any]) -> bool:
        """
        添加知识到图谱
        
        Args:
            knowledge_data: 知识数据，包含概念、实体和关系
            
        Returns:
            是否成功
        """
        try:
            # 添加概念
            if 'concepts' in knowledge_data:
                for concept in knowledge_data['concepts']:
                    self.manager.create_concept_node(concept)
            
            # 添加实体
            if 'entities' in knowledge_data:
                for entity in knowledge_data['entities']:
                    self.manager.create_entity_node(entity)
            
            # 添加关系
            if 'relationships' in knowledge_data:
                for rel in knowledge_data['relationships']:
                    self.manager.create_relationship(
                        start_id=rel['start_id'],
                        end_id=rel['end_id'],
                        relationship_type=rel['type'],
                        properties=rel.get('properties', {})
                    )
            
            return True
        except Exception as e:
            logger.error(f"Failed to add knowledge: {e}")
            return False
    
    def query_knowledge(self, query: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        查询知识
        
        Args:
            query: 查询条件
            
        Returns:
            查询结果
        """
        query_type = query.get('type', 'semantic_search')
        
        if query_type == 'semantic_search':
            nodes = self.manager.semantic_search(
                query_text=query['text'],
                node_types=query.get('node_types'),
                limit=query.get('limit', 10)
            )
            return [{'id': node.id, 'labels': node.labels, 'properties': node.properties} 
                   for node in nodes]
        
        elif query_type == 'related_concepts':
            nodes = self.manager.find_related_concepts(
                concept_id=query['concept_id'],
                relationship_types=query.get('relationship_types'),
                max_depth=query.get('max_depth', 2)
            )
            return [{'id': node.id, 'labels': node.labels, 'properties': node.properties} 
                   for node in nodes]
        
        elif query_type == 'shortest_path':
            path = self.manager.find_shortest_path(
                start_id=query['start_id'],
                end_id=query['end_id'],
                max_depth=query.get('max_depth', 5)
            )
            if path:
                return {
                    'nodes': [{'id': node.id, 'labels': node.labels, 'properties': node.properties} 
                             for node in path.nodes],
                    'relationships': [{'id': rel.id, 'type': rel.type, 'properties': rel.properties} 
                                    for rel in path.relationships],
                    'length': path.length
                }
            return None
        
        else:
            logger.warning(f"Unknown query type: {query_type}")
            return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取图谱统计信息"""
        return {
            'nodes': self.manager.get_node_statistics(),
            'relationships': self.manager.get_relationship_statistics()
        }
