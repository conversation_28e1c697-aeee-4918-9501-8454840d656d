{"title": "AGI关系层元数据示例", "description": "展示关系层元数据标准的实际应用示例", "version": "1.0.0", "examples": [{"example_name": "层次关系示例 - is_a关系", "description": "展示概念间的分类层次关系", "metadata": {"metadata_info": {"schema_version": "1.0.0", "created_at": "2024-01-01T10:30:00Z", "created_by": "knowledge_engineer_001"}, "relationship_definition": {"relationship_id": "rel_12345678-1234-1234-1234-123456789abc", "relationship_type": "is_a", "source_id": "concept_87654321-4321-4321-4321-cba987654321", "target_id": "concept_12345678-1234-1234-1234-123456789abc", "description": "神经网络是人工智能的一个子类", "directionality": "directed", "strength": 0.95, "weight": 1.0}, "semantic_properties": {"relationship_semantics": {"inheritance": true, "transitivity": true, "symmetry": false, "reflexivity": false}, "logical_properties": {"implies_properties": true, "subsumption": true, "specialization_direction": "source_to_target"}, "domain_context": "computer_science", "semantic_tags": ["分类", "层次", "继承", "子类"]}, "relationship_attributes": {"certainty": 0.98, "evidence_strength": "strong", "consensus_level": "high", "temporal_stability": "stable", "context_dependency": "low"}, "validation_info": {"validated_by": ["expert_001", "expert_002"], "validation_method": "expert_consensus", "validation_date": "2024-01-01T10:25:00Z", "validation_score": 0.96}, "quality_metrics": {"confidence": 0.95, "accuracy": 0.98, "completeness": 0.9, "consistency": 0.97, "relevance": 0.94}, "provenance_info": {"source": "expert_knowledge", "derivation_method": "manual_annotation", "evidence_sources": ["AI textbook definitions", "IEEE AI standards", "Expert consensus"], "version": "1.0.0"}}}, {"example_name": "组成关系示例 - part_of关系", "description": "展示部分-整体关系", "metadata": {"metadata_info": {"schema_version": "1.0.0", "created_at": "2024-01-01T11:00:00Z", "created_by": "system_architect_001"}, "relationship_definition": {"relationship_id": "rel_87654321-4321-4321-4321-cba987654321", "relationship_type": "part_of", "source_id": "concept_activation_function_001", "target_id": "concept_87654321-4321-4321-4321-cba987654321", "description": "激活函数是神经网络的组成部分", "directionality": "directed", "strength": 0.9, "weight": 0.8}, "semantic_properties": {"relationship_semantics": {"composition": true, "aggregation": false, "transitivity": true, "symmetry": false}, "mereological_properties": {"essential_part": true, "functional_role": "computation", "cardinality": "multiple"}, "domain_context": "computer_science", "semantic_tags": ["组成", "部分", "结构", "功能"]}, "relationship_attributes": {"certainty": 0.92, "evidence_strength": "strong", "functional_importance": "high", "structural_necessity": "required"}, "quality_metrics": {"confidence": 0.9, "accuracy": 0.95, "completeness": 0.88, "consistency": 0.93, "relevance": 0.91}, "provenance_info": {"source": "automatic_extraction", "derivation_method": "structural_analysis", "version": "1.0.0"}}}, {"example_name": "因果关系示例 - causes关系", "description": "展示因果关系的建模", "metadata": {"metadata_info": {"schema_version": "1.0.0", "created_at": "2024-01-01T12:00:00Z", "created_by": "domain_expert_001"}, "relationship_definition": {"relationship_id": "rel_causal_example_001", "relationship_type": "causes", "source_id": "concept_overfitting_001", "target_id": "concept_poor_generalization_001", "description": "过拟合导致模型泛化能力差", "directionality": "directed", "strength": 0.85, "weight": 1.2}, "semantic_properties": {"relationship_semantics": {"causality": true, "transitivity": false, "symmetry": false, "temporal_ordering": true}, "causal_properties": {"causal_strength": "strong", "causal_type": "direct", "temporal_delay": "immediate", "necessity": "sufficient", "probability": 0.85}, "domain_context": "machine_learning", "semantic_tags": ["因果", "影响", "结果", "机制"]}, "relationship_attributes": {"certainty": 0.85, "evidence_strength": "moderate", "mechanism_clarity": "well_understood", "empirical_support": "strong"}, "temporal_aspects": {"temporal_relation": "before", "duration": "persistent", "frequency": "always", "conditions": ["insufficient_training_data", "model_complexity_high"]}, "quality_metrics": {"confidence": 0.85, "accuracy": 0.88, "completeness": 0.82, "consistency": 0.9, "relevance": 0.93}, "provenance_info": {"source": "machine_learning", "derivation_method": "empirical_observation", "evidence_sources": ["Cross-validation experiments", "Learning curve analysis", "Theoretical analysis"], "version": "1.0.0"}}}, {"example_name": "功能关系示例 - enables关系", "description": "展示功能使能关系", "metadata": {"metadata_info": {"schema_version": "1.0.0", "created_at": "2024-01-01T13:00:00Z", "created_by": "system_designer_001"}, "relationship_definition": {"relationship_id": "rel_enables_example_001", "relationship_type": "enables", "source_id": "concept_gpu_acceleration_001", "target_id": "concept_deep_learning_training_001", "description": "GPU加速使得深度学习训练成为可能", "directionality": "directed", "strength": 0.92, "weight": 1.5}, "semantic_properties": {"relationship_semantics": {"enablement": true, "facilitation": true, "dependency": true, "conditional": true}, "functional_properties": {"enablement_type": "performance", "necessity_level": "practical", "efficiency_gain": "significant", "scalability_impact": "high"}, "domain_context": "computer_science", "semantic_tags": ["使能", "支持", "加速", "优化"]}, "relationship_attributes": {"certainty": 0.92, "evidence_strength": "strong", "practical_importance": "critical", "technological_dependency": "high"}, "quantitative_aspects": {"performance_improvement": "10x-100x", "cost_efficiency": "high", "adoption_rate": "widespread", "maturity_level": "mature"}, "quality_metrics": {"confidence": 0.92, "accuracy": 0.94, "completeness": 0.89, "consistency": 0.95, "relevance": 0.96}, "provenance_info": {"source": "empirical_evidence", "derivation_method": "performance_analysis", "version": "1.0.0"}}}, {"example_name": "时间关系示例 - before关系", "description": "展示时间顺序关系", "metadata": {"metadata_info": {"schema_version": "1.0.0", "created_at": "2024-01-01T14:00:00Z", "created_by": "process_analyst_001"}, "relationship_definition": {"relationship_id": "rel_temporal_example_001", "relationship_type": "before", "source_id": "concept_data_preprocessing_001", "target_id": "concept_ml_training_001", "description": "数据预处理必须在机器学习训练之前进行", "directionality": "directed", "strength": 1.0, "weight": 1.0}, "semantic_properties": {"relationship_semantics": {"temporal_ordering": true, "precedence": true, "sequence": true, "dependency": true}, "temporal_properties": {"temporal_type": "strict_ordering", "duration_constraint": "completion_required", "overlap_allowed": false, "gap_allowed": true}, "domain_context": "machine_learning", "semantic_tags": ["时序", "先后", "流程", "依赖"]}, "relationship_attributes": {"certainty": 1.0, "evidence_strength": "definitive", "logical_necessity": "required", "process_criticality": "essential"}, "temporal_constraints": {"strict_ordering": true, "minimum_gap": "none", "maximum_gap": "unlimited", "completion_requirement": "full"}, "quality_metrics": {"confidence": 1.0, "accuracy": 1.0, "completeness": 0.95, "consistency": 1.0, "relevance": 0.98}, "provenance_info": {"source": "process_definition", "derivation_method": "logical_analysis", "version": "1.0.0"}}}, {"example_name": "空间关系示例 - located_at关系", "description": "展示空间位置关系", "metadata": {"metadata_info": {"schema_version": "1.0.0", "created_at": "2024-01-01T15:00:00Z", "created_by": "system_administrator_001"}, "relationship_definition": {"relationship_id": "rel_spatial_example_001", "relationship_type": "located_at", "source_id": "entity_ml_model_instance_001", "target_id": "entity_gpu_server_001", "description": "机器学习模型实例部署在GPU服务器上", "directionality": "directed", "strength": 0.95, "weight": 1.0}, "semantic_properties": {"relationship_semantics": {"spatial_location": true, "containment": true, "deployment": true, "hosting": true}, "spatial_properties": {"location_type": "computational", "precision": "server_level", "mobility": "deployable", "exclusivity": false}, "domain_context": "system_deployment", "semantic_tags": ["位置", "部署", "托管", "分布"]}, "relationship_attributes": {"certainty": 0.95, "evidence_strength": "observable", "operational_status": "active", "resource_utilization": "moderate"}, "deployment_details": {"deployment_time": "2024-01-01T14:30:00Z", "resource_allocation": {"gpu_memory": "8GB", "cpu_cores": 4, "ram": "16GB"}, "performance_metrics": {"latency": "50ms", "throughput": "100 requests/sec"}}, "quality_metrics": {"confidence": 0.95, "accuracy": 0.98, "completeness": 0.92, "consistency": 0.96, "relevance": 0.89}, "provenance_info": {"source": "system_monitoring", "derivation_method": "automatic_detection", "version": "1.0.0"}, "extensions": {"ext_deployment_config": {"container_id": "ml_model_container_001", "port_mapping": "8080:80", "environment_variables": {"MODEL_PATH": "/models/trained_model.pkl", "BATCH_SIZE": "32"}}, "ext_monitoring_info": {"health_check_url": "/health", "metrics_endpoint": "/metrics", "log_level": "INFO"}}}}, {"example_name": "双向关系示例 - similar_to关系", "description": "展示对称的相似性关系", "metadata": {"metadata_info": {"schema_version": "1.0.0", "created_at": "2024-01-01T16:00:00Z", "created_by": "similarity_analyzer_001"}, "relationship_definition": {"relationship_id": "rel_similarity_example_001", "relationship_type": "similar_to", "source_id": "concept_cnn_001", "target_id": "concept_rnn_001", "description": "卷积神经网络和循环神经网络在某些方面相似", "directionality": "bidirectional", "strength": 0.75, "weight": 0.8}, "semantic_properties": {"relationship_semantics": {"similarity": true, "symmetry": true, "transitivity": false, "reflexivity": true}, "similarity_properties": {"similarity_type": "functional", "similarity_aspects": ["neural_architecture", "learning_capability", "parameter_optimization"], "difference_aspects": ["temporal_processing", "input_structure", "application_domains"]}, "domain_context": "deep_learning", "semantic_tags": ["相似", "比较", "类比", "特征"]}, "relationship_attributes": {"certainty": 0.75, "evidence_strength": "moderate", "similarity_score": 0.75, "comparison_basis": "architectural_features"}, "similarity_analysis": {"common_features": ["gradient_based_learning", "nonlinear_transformations", "hierarchical_representations"], "distinguishing_features": ["spatial_vs_temporal_focus", "parameter_sharing_patterns", "input_processing_methods"], "similarity_metrics": {"structural_similarity": 0.6, "functional_similarity": 0.8, "application_similarity": 0.7}}, "quality_metrics": {"confidence": 0.75, "accuracy": 0.78, "completeness": 0.85, "consistency": 0.82, "relevance": 0.88}, "provenance_info": {"source": "comparative_analysis", "derivation_method": "feature_comparison", "version": "1.0.0"}}}], "relationship_patterns": {"hierarchical_patterns": {"description": "层次结构中的常见关系模式", "patterns": [{"pattern_name": "taxonomy_chain", "description": "分类链：A is_a B is_a C", "transitivity": true, "example": "CNN is_a Neural_Network is_a ML_Algorithm"}, {"pattern_name": "composition_hierarchy", "description": "组成层次：A part_of B part_of C", "transitivity": true, "example": "Neuron part_of Layer part_of Network"}]}, "causal_patterns": {"description": "因果关系中的常见模式", "patterns": [{"pattern_name": "causal_chain", "description": "因果链：A causes B causes C", "transitivity": false, "example": "Overfitting causes Poor_Generalization causes Low_Accuracy"}, {"pattern_name": "enabling_sequence", "description": "使能序列：A enables B enables C", "transitivity": true, "example": "GPU enables Fast_Training enables Deep_Networks"}]}}, "validation_guidelines": ["确保关系类型与源、目标节点类型兼容", "验证关系的方向性设置正确", "检查关系强度和权重的合理性", "确认时间戳的逻辑一致性", "验证扩展字段的命名规范", "检查质量指标的有效范围"], "usage_recommendations": ["根据关系的语义选择合适的关系类型", "为重要关系设置较高的强度和权重", "记录关系建立的证据和来源", "定期验证关系的有效性", "使用扩展字段添加领域特定属性", "保持关系网络的一致性和完整性"]}