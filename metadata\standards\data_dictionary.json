{"$schema": "http://json-schema.org/draft-07/schema#", "title": "AGI元数据数据字典", "description": "AGI系统元数据的完整数据字典，包含所有字段的详细信息", "version": "1.0.0", "last_updated": "2024-01-01", "data_dictionary": {"概念层_Concept_Layer": {"table_description": "存储AGI系统中的概念定义和层次关系", "primary_key": "concept_id", "fields": {"concept_id": {"field_name": "concept_id", "chinese_name": "概念标识", "data_type": "VARCHAR(50)", "nullable": false, "primary_key": true, "foreign_key": false, "unique": true, "indexed": true, "description": "概念的唯一标识符，采用UUID格式", "business_meaning": "用于在系统中唯一标识一个概念", "data_source": "系统自动生成", "validation_rules": "必须符合concept_前缀的UUID格式", "example_values": ["concept_12345678-1234-1234-1234-123456789abc"], "related_fields": ["parent_concepts", "child_concepts"], "usage_notes": "创建后不可修改，用于建立概念间的关系"}, "concept_name": {"field_name": "concept_name", "chinese_name": "概念名称", "data_type": "VARCHAR(255)", "nullable": false, "primary_key": false, "foreign_key": false, "unique": false, "indexed": true, "description": "概念的人类可读名称", "business_meaning": "概念在特定领域内的标准名称", "data_source": "用户输入或自动提取", "validation_rules": "长度1-255字符，同一领域内唯一", "example_values": ["人工智能", "机器学习", "深度学习"], "related_fields": ["domain", "aliases"], "usage_notes": "应使用标准术语，避免歧义"}, "concept_type": {"field_name": "concept_type", "chinese_name": "概念类型", "data_type": "ENUM", "nullable": false, "primary_key": false, "foreign_key": false, "unique": false, "indexed": true, "description": "概念的分类类型", "business_meaning": "区分不同性质的概念", "data_source": "用户选择或自动分类", "validation_rules": "必须是预定义的枚举值之一", "allowed_values": ["abstract", "concrete", "relation", "attribute", "action", "state", "event", "process"], "example_values": ["abstract", "concrete"], "related_fields": ["abstraction_level"], "usage_notes": "影响概念在推理中的使用方式"}, "abstraction_level": {"field_name": "abstraction_level", "chinese_name": "抽象层级", "data_type": "INTEGER", "nullable": true, "primary_key": false, "foreign_key": false, "unique": false, "indexed": true, "description": "概念的抽象程度，0为最具体", "business_meaning": "表示概念在抽象层次中的位置", "data_source": "系统计算或用户指定", "validation_rules": "0-10之间的整数", "example_values": [0, 1, 2, 3], "related_fields": ["parent_concepts", "child_concepts"], "usage_notes": "用于概念层次推理和查询优化"}, "domain": {"field_name": "domain", "chinese_name": "所属领域", "data_type": "VARCHAR(50)", "nullable": true, "primary_key": false, "foreign_key": false, "unique": false, "indexed": true, "description": "概念所属的知识领域", "business_meaning": "概念的学科或应用领域分类", "data_source": "用户选择或自动分类", "validation_rules": "必须是预定义的领域值或custom", "example_values": ["computer_science", "medicine", "physics"], "related_fields": ["concept_name"], "usage_notes": "用于领域特定的推理和搜索"}, "definition": {"field_name": "definition", "chinese_name": "概念定义", "data_type": "TEXT", "nullable": true, "primary_key": false, "foreign_key": false, "unique": false, "indexed": false, "description": "概念的正式定义", "business_meaning": "概念的准确、完整的描述", "data_source": "专家输入或权威来源", "validation_rules": "最大长度2000字符", "example_values": ["人工智能是计算机科学的一个分支..."], "related_fields": ["description"], "usage_notes": "应基于权威来源，保持准确性"}, "parent_concepts": {"field_name": "parent_concepts", "chinese_name": "父概念列表", "data_type": "JSON_ARRAY", "nullable": true, "primary_key": false, "foreign_key": true, "unique": false, "indexed": true, "description": "当前概念的父概念ID列表", "business_meaning": "表示概念的上级分类关系", "data_source": "用户定义或自动推理", "validation_rules": "数组中的每个ID必须是有效的concept_id", "example_values": [["concept_ai_123", "concept_tech_456"]], "related_fields": ["concept_id", "child_concepts"], "usage_notes": "用于建立概念层次结构"}, "confidence": {"field_name": "confidence", "chinese_name": "置信度", "data_type": "DECIMAL(3,2)", "nullable": true, "primary_key": false, "foreign_key": false, "unique": false, "indexed": false, "description": "概念定义的置信度", "business_meaning": "表示对概念定义准确性的信心程度", "data_source": "系统计算或专家评估", "validation_rules": "0.0到1.0之间的小数", "example_values": [0.95, 0.87, 1.0], "related_fields": ["source"], "usage_notes": "用于质量评估和推理权重"}}, "indexes": [{"index_name": "idx_concept_name_domain", "index_type": "COMPOSITE", "fields": ["concept_name", "domain"], "purpose": "支持按名称和领域的快速查询"}, {"index_name": "idx_concept_type_level", "index_type": "COMPOSITE", "fields": ["concept_type", "abstraction_level"], "purpose": "支持按类型和层级的查询"}], "constraints": [{"constraint_name": "chk_abstraction_level", "constraint_type": "CHECK", "condition": "abstraction_level >= 0 AND abstraction_level <= 10"}, {"constraint_name": "uk_concept_name_domain", "constraint_type": "UNIQUE", "fields": ["concept_name", "domain"]}]}, "关系层_Relationship_Layer": {"table_description": "存储实体和概念之间的语义关系", "primary_key": "relationship_id", "fields": {"relationship_id": {"field_name": "relationship_id", "chinese_name": "关系标识", "data_type": "VARCHAR(50)", "nullable": false, "primary_key": true, "foreign_key": false, "unique": true, "indexed": true, "description": "关系的唯一标识符", "business_meaning": "用于在系统中唯一标识一个关系", "data_source": "系统自动生成", "validation_rules": "必须符合rel_前缀的UUID格式", "example_values": ["rel_12345678-1234-1234-1234-123456789abc"], "related_fields": ["source_id", "target_id"], "usage_notes": "创建后不可修改"}, "relationship_type": {"field_name": "relationship_type", "chinese_name": "关系类型", "data_type": "VARCHAR(50)", "nullable": false, "primary_key": false, "foreign_key": false, "unique": false, "indexed": true, "description": "关系的语义类型", "business_meaning": "定义两个节点之间的语义关系性质", "data_source": "用户选择或自动识别", "validation_rules": "必须是预定义的关系类型", "allowed_values": ["is_a", "part_of", "contains", "causes", "enables"], "example_values": ["is_a", "part_of", "causes"], "related_fields": ["directionality"], "usage_notes": "影响推理规则的应用"}, "source_id": {"field_name": "source_id", "chinese_name": "源节点标识", "data_type": "VARCHAR(50)", "nullable": false, "primary_key": false, "foreign_key": true, "unique": false, "indexed": true, "description": "关系的源节点ID", "business_meaning": "关系的起始节点", "data_source": "用户选择", "validation_rules": "必须是有效的节点ID", "example_values": ["concept_123", "entity_456"], "related_fields": ["target_id", "directionality"], "usage_notes": "对于有向关系，表示关系的起点"}, "target_id": {"field_name": "target_id", "chinese_name": "目标节点标识", "data_type": "VARCHAR(50)", "nullable": false, "primary_key": false, "foreign_key": true, "unique": false, "indexed": true, "description": "关系的目标节点ID", "business_meaning": "关系的终止节点", "data_source": "用户选择", "validation_rules": "必须是有效的节点ID", "example_values": ["concept_789", "entity_012"], "related_fields": ["source_id", "directionality"], "usage_notes": "对于有向关系，表示关系的终点"}, "strength": {"field_name": "strength", "chinese_name": "关系强度", "data_type": "DECIMAL(3,2)", "nullable": true, "primary_key": false, "foreign_key": false, "unique": false, "indexed": false, "description": "关系的强度或重要性", "business_meaning": "表示关系的紧密程度或重要性", "data_source": "系统计算或专家评估", "validation_rules": "0.0到1.0之间的小数", "example_values": [0.9, 0.7, 1.0], "related_fields": ["confidence", "weight"], "usage_notes": "用于推理中的权重计算"}, "directionality": {"field_name": "directionality", "chinese_name": "方向性", "data_type": "VARCHAR(20)", "nullable": false, "primary_key": false, "foreign_key": false, "unique": false, "indexed": true, "description": "关系的方向性质", "business_meaning": "定义关系是否有方向性", "data_source": "根据关系类型自动设置", "validation_rules": "必须是预定义的方向性值", "allowed_values": ["directed", "undirected", "bidirectional"], "example_values": ["directed", "undirected"], "related_fields": ["relationship_type"], "usage_notes": "影响图遍历和推理算法"}}}, "知识层_Knowledge_Layer": {"table_description": "存储各类知识项和相关元数据", "primary_key": "knowledge_id", "fields": {"knowledge_id": {"field_name": "knowledge_id", "chinese_name": "知识标识", "data_type": "VARCHAR(50)", "nullable": false, "primary_key": true, "foreign_key": false, "unique": true, "indexed": true, "description": "知识项的唯一标识符", "business_meaning": "用于在系统中唯一标识一个知识项", "data_source": "系统自动生成", "validation_rules": "必须符合knowledge_前缀的UUID格式", "example_values": ["knowledge_12345678-1234-1234-1234-123456789abc"], "related_fields": ["title", "content"], "usage_notes": "创建后不可修改"}, "title": {"field_name": "title", "chinese_name": "知识标题", "data_type": "VARCHAR(500)", "nullable": false, "primary_key": false, "foreign_key": false, "unique": false, "indexed": true, "description": "知识项的标题", "business_meaning": "知识项的简短描述性标题", "data_source": "用户输入或自动生成", "validation_rules": "长度1-500字符", "example_values": ["机器学习算法分类", "Python编程基础"], "related_fields": ["content", "keywords"], "usage_notes": "应简洁明了，便于搜索和识别"}, "knowledge_type": {"field_name": "knowledge_type", "chinese_name": "知识类型", "data_type": "VARCHAR(30)", "nullable": false, "primary_key": false, "foreign_key": false, "unique": false, "indexed": true, "description": "知识的分类类型", "business_meaning": "区分不同性质的知识", "data_source": "用户选择或自动分类", "validation_rules": "必须是预定义的知识类型", "allowed_values": ["factual", "procedural", "declarative", "conceptual"], "example_values": ["factual", "procedural"], "related_fields": ["content_format"], "usage_notes": "影响知识的使用和推理方式"}, "content": {"field_name": "content", "chinese_name": "知识内容", "data_type": "TEXT", "nullable": false, "primary_key": false, "foreign_key": false, "unique": false, "indexed": false, "description": "知识的具体内容", "business_meaning": "知识项的核心信息", "data_source": "用户输入或自动提取", "validation_rules": "不能为空，格式需与content_format匹配", "example_values": ["机器学习是人工智能的一个分支..."], "related_fields": ["content_format", "title"], "usage_notes": "内容应准确、完整、结构化"}, "keywords": {"field_name": "keywords", "chinese_name": "关键词", "data_type": "JSON_ARRAY", "nullable": true, "primary_key": false, "foreign_key": false, "unique": false, "indexed": true, "description": "知识项的关键词列表", "business_meaning": "用于搜索和分类的关键术语", "data_source": "用户输入或自动提取", "validation_rules": "最多50个关键词，每个最长50字符", "example_values": [["机器学习", "算法", "分类"]], "related_fields": ["title", "category"], "usage_notes": "应选择最相关和具有代表性的词汇"}}}}, "relationships": {"概念层次关系": {"relationship_type": "self_referencing", "parent_table": "概念层_Concept_Layer", "child_table": "概念层_Concept_Layer", "foreign_key": "parent_concepts", "referenced_key": "concept_id", "description": "概念之间的层次关系", "cardinality": "many_to_many"}, "概念关系映射": {"relationship_type": "one_to_many", "parent_table": "概念层_Concept_Layer", "child_table": "关系层_Relationship_Layer", "foreign_key": "source_id/target_id", "referenced_key": "concept_id", "description": "概念参与的关系", "cardinality": "one_to_many"}, "知识概念关联": {"relationship_type": "many_to_many", "parent_table": "概念层_Concept_Layer", "child_table": "知识层_Knowledge_Layer", "foreign_key": "related_concepts", "referenced_key": "concept_id", "description": "知识项与相关概念的关联", "cardinality": "many_to_many"}}, "business_rules": {"概念层规则": ["同一领域内概念名称必须唯一", "抽象层级必须与父子关系一致", "概念类型决定可参与的关系类型", "删除概念前必须先删除相关关系"], "关系层规则": ["源节点和目标节点必须存在", "关系类型必须与节点类型兼容", "有向关系不能形成循环（特定类型）", "关系强度必须基于有效证据"], "知识层规则": ["知识内容格式必须与声明的格式一致", "关键词必须与内容相关", "知识类型影响推理规则的应用", "版本更新必须保留历史记录"]}, "data_quality_standards": {"完整性要求": {"必填字段": "所有标记为required的字段必须有值", "引用完整性": "外键必须引用存在的记录", "业务完整性": "业务规则定义的必要信息必须完整"}, "准确性要求": {"数据格式": "数据必须符合定义的格式和约束", "业务逻辑": "数据必须符合业务逻辑规则", "一致性检查": "相关数据之间必须保持一致"}, "时效性要求": {"更新频率": "数据必须按照定义的频率更新", "版本控制": "必须维护适当的版本历史", "过期处理": "过期数据必须按规则处理"}}, "usage_guidelines": {"查询优化": ["使用索引字段进行查询", "避免在大文本字段上进行模糊查询", "合理使用复合索引", "考虑查询缓存策略"], "数据维护": ["定期检查数据质量", "及时更新过期信息", "维护引用完整性", "监控数据增长趋势"], "扩展建议": ["使用扩展字段添加自定义属性", "遵循命名约定", "考虑向后兼容性", "文档化扩展字段的用途"]}}