"""
Neo4j图数据库工具类
提供通用的Neo4j操作接口，与业务逻辑解耦
"""

import time
import uuid
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from neo4j import GraphDatabase, Driver, Session, Result
from neo4j.exceptions import ServiceUnavailable, AuthError, ConfigurationError

from .database_base import (
    GraphDatabaseInterface, DatabaseConfig, QueryResult, OperationResult,
    ConnectionStatus, ConnectionException, QueryException, OperationException
)

import logging
logger = logging.getLogger(__name__)


class Neo4jConfig(DatabaseConfig):
    """Neo4j配置类"""
    
    def __init__(self, uri: str, username: str, password: str, database: str = "neo4j",
                 max_connection_lifetime: int = 3600, max_connection_pool_size: int = 100,
                 connection_acquisition_timeout: int = 60, encrypted: bool = False,
                 trust: str = "TRUST_ALL_CERTIFICATES", **kwargs):
        # 解析URI获取host和port
        if "://" in uri:
            protocol, address = uri.split("://", 1)
            if ":" in address:
                host, port = address.split(":", 1)
                port = int(port)
            else:
                host = address
                port = 7687 if protocol == "bolt" else 7474
        else:
            host = uri
            port = 7687
            
        super().__init__(
            host=host,
            port=port,
            database=database,
            username=username,
            password=password,
            **kwargs
        )
        
        self.uri = uri
        self.max_connection_lifetime = max_connection_lifetime
        self.max_connection_pool_size = max_connection_pool_size
        self.connection_acquisition_timeout = connection_acquisition_timeout
        self.encrypted = encrypted
        self.trust = trust


class Neo4jTool(GraphDatabaseInterface):
    """Neo4j工具类"""
    
    def __init__(self, config: Neo4jConfig):
        """
        初始化Neo4j工具
        
        Args:
            config: Neo4j配置
        """
        super().__init__(config)
        self.config: Neo4jConfig = config
        self.driver: Optional[Driver] = None
        
    def connect(self) -> bool:
        """建立连接"""
        try:
            self.status = ConnectionStatus.CONNECTING
            
            self.driver = GraphDatabase.driver(
                self.config.uri,
                auth=(self.config.username, self.config.password),
                max_connection_lifetime=self.config.max_connection_lifetime,
                max_connection_pool_size=self.config.max_connection_pool_size,
                connection_acquisition_timeout=self.config.connection_acquisition_timeout,
                encrypted=self.config.encrypted,
                trust=self.config.trust
            )
            
            # 测试连接
            with self.driver.session(database=self.config.database) as session:
                session.run("RETURN 1")
            
            self.status = ConnectionStatus.CONNECTED
            self.connection_time = datetime.now()
            self.last_error = None
            
            logger.info(f"Connected to Neo4j at {self.config.uri}")
            return True
            
        except (ServiceUnavailable, AuthError, ConfigurationError) as e:
            self.status = ConnectionStatus.ERROR
            self.last_error = str(e)
            logger.error(f"Failed to connect to Neo4j: {e}")
            return False
        except Exception as e:
            self.status = ConnectionStatus.ERROR
            self.last_error = str(e)
            logger.error(f"Unexpected error connecting to Neo4j: {e}")
            return False
    
    def disconnect(self) -> bool:
        """关闭连接"""
        try:
            if self.driver:
                self.driver.close()
                self.driver = None
            
            self.status = ConnectionStatus.DISCONNECTED
            logger.info("Disconnected from Neo4j")
            return True
            
        except Exception as e:
            logger.error(f"Error disconnecting from Neo4j: {e}")
            return False
    
    def is_connected(self) -> bool:
        """检查连接状态"""
        return self.status == ConnectionStatus.CONNECTED and self.driver is not None
    
    def ping(self) -> bool:
        """测试连接"""
        if not self.is_connected():
            return False
        
        try:
            with self.driver.session(database=self.config.database) as session:
                session.run("RETURN 1")
            return True
        except Exception:
            return False
    
    def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> QueryResult:
        """执行查询"""
        if not self.is_connected():
            return QueryResult(
                success=False,
                error="Not connected to database"
            )
        
        start_time = time.time()
        
        try:
            with self.driver.session(database=self.config.database) as session:
                result = session.run(query, params or {})
                records = [record.data() for record in result]
                
                execution_time = time.time() - start_time
                
                return QueryResult(
                    success=True,
                    data=records,
                    count=len(records),
                    execution_time=execution_time
                )
                
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Query execution failed: {e}")
            
            return QueryResult(
                success=False,
                error=str(e),
                execution_time=execution_time
            )
    
    def execute_command(self, command: str, params: Optional[Dict[str, Any]] = None) -> OperationResult:
        """执行命令"""
        if not self.is_connected():
            return OperationResult(
                success=False,
                error="Not connected to database"
            )
        
        start_time = time.time()
        
        try:
            with self.driver.session(database=self.config.database) as session:
                result = session.run(command, params or {})
                summary = result.consume()
                
                execution_time = time.time() - start_time
                
                return OperationResult(
                    success=True,
                    affected_count=summary.counters.nodes_created + 
                                 summary.counters.relationships_created +
                                 summary.counters.nodes_deleted +
                                 summary.counters.relationships_deleted,
                    execution_time=execution_time,
                    metadata={
                        "nodes_created": summary.counters.nodes_created,
                        "nodes_deleted": summary.counters.nodes_deleted,
                        "relationships_created": summary.counters.relationships_created,
                        "relationships_deleted": summary.counters.relationships_deleted,
                        "properties_set": summary.counters.properties_set
                    }
                )
                
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Command execution failed: {e}")
            
            return OperationResult(
                success=False,
                error=str(e),
                execution_time=execution_time
            )
    
    def create_node(self, labels: List[str], properties: Dict[str, Any]) -> OperationResult:
        """创建节点"""
        node_id = properties.get('id', str(uuid.uuid4()))
        properties['id'] = node_id
        
        labels_str = ":".join(labels)
        query = f"CREATE (n:{labels_str} $properties) RETURN n.id as id"
        
        result = self.execute_command(query, {"properties": properties})
        if result.success:
            result.result_id = node_id
        
        return result
    
    def create_relationship(self, start_node_id: str, end_node_id: str, 
                          relationship_type: str, properties: Dict[str, Any] = None) -> OperationResult:
        """创建关系"""
        rel_id = str(uuid.uuid4())
        rel_properties = properties or {}
        rel_properties['id'] = rel_id
        
        query = f"""
        MATCH (a), (b)
        WHERE a.id = $start_id AND b.id = $end_id
        CREATE (a)-[r:{relationship_type} $properties]->(b)
        RETURN r.id as id
        """
        
        result = self.execute_command(query, {
            "start_id": start_node_id,
            "end_id": end_node_id,
            "properties": rel_properties
        })
        
        if result.success:
            result.result_id = rel_id
        
        return result
    
    def find_nodes(self, labels: List[str] = None, properties: Dict[str, Any] = None,
                  limit: int = 100) -> QueryResult:
        """查找节点"""
        query_parts = ["MATCH (n"]
        params = {}
        
        if labels:
            query_parts[0] += ":" + ":".join(labels)
        
        query_parts.append(")")
        
        if properties:
            conditions = []
            for key, value in properties.items():
                param_name = f"prop_{key}"
                conditions.append(f"n.{key} = ${param_name}")
                params[param_name] = value
            
            if conditions:
                query_parts.append("WHERE " + " AND ".join(conditions))
        
        query_parts.append(f"RETURN n LIMIT {limit}")
        query = " ".join(query_parts)
        
        return self.execute_query(query, params)
    
    def find_relationships(self, relationship_type: str = None, 
                         properties: Dict[str, Any] = None, limit: int = 100) -> QueryResult:
        """查找关系"""
        query_parts = ["MATCH ()-[r"]
        params = {}
        
        if relationship_type:
            query_parts[0] += f":{relationship_type}"
        
        query_parts[0] += "]->()"
        
        if properties:
            conditions = []
            for key, value in properties.items():
                param_name = f"prop_{key}"
                conditions.append(f"r.{key} = ${param_name}")
                params[param_name] = value
            
            if conditions:
                query_parts.append("WHERE " + " AND ".join(conditions))
        
        query_parts.append(f"RETURN r LIMIT {limit}")
        query = " ".join(query_parts)
        
        return self.execute_query(query, params)
    
    def find_path(self, start_node_id: str, end_node_id: str, 
                 max_depth: int = 5) -> QueryResult:
        """查找路径"""
        query = f"""
        MATCH (start), (end)
        WHERE start.id = $start_id AND end.id = $end_id
        MATCH path = shortestPath((start)-[*1..{max_depth}]-(end))
        RETURN path
        """
        
        return self.execute_query(query, {
            "start_id": start_node_id,
            "end_id": end_node_id
        })
    
    def update_node(self, node_id: str, properties: Dict[str, Any]) -> OperationResult:
        """更新节点"""
        query = "MATCH (n) WHERE n.id = $node_id SET n += $properties RETURN n"
        
        return self.execute_command(query, {
            "node_id": node_id,
            "properties": properties
        })
    
    def delete_node(self, node_id: str, detach: bool = True) -> OperationResult:
        """删除节点"""
        query = f"MATCH (n) WHERE n.id = $node_id {'DETACH ' if detach else ''}DELETE n"
        
        return self.execute_command(query, {"node_id": node_id})
    
    def delete_relationship(self, relationship_id: str) -> OperationResult:
        """删除关系"""
        query = "MATCH ()-[r]-() WHERE r.id = $rel_id DELETE r"
        
        return self.execute_command(query, {"rel_id": relationship_id})
    
    def get_node_count(self, labels: List[str] = None) -> int:
        """获取节点数量"""
        if labels:
            labels_str = ":".join(labels)
            query = f"MATCH (n:{labels_str}) RETURN count(n) as count"
        else:
            query = "MATCH (n) RETURN count(n) as count"
        
        result = self.execute_query(query)
        if result.success and result.data:
            return result.data[0].get('count', 0)
        return 0
    
    def get_relationship_count(self, relationship_type: str = None) -> int:
        """获取关系数量"""
        if relationship_type:
            query = f"MATCH ()-[r:{relationship_type}]-() RETURN count(r) as count"
        else:
            query = "MATCH ()-[r]-() RETURN count(r) as count"
        
        result = self.execute_query(query)
        if result.success and result.data:
            return result.data[0].get('count', 0)
        return 0
    
    def create_index(self, label: str, property_name: str) -> OperationResult:
        """创建索引"""
        query = f"CREATE INDEX FOR (n:{label}) ON (n.{property_name})"
        return self.execute_command(query)
    
    def drop_index(self, label: str, property_name: str) -> OperationResult:
        """删除索引"""
        query = f"DROP INDEX FOR (n:{label}) ON (n.{property_name})"
        return self.execute_command(query)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        base_stats = super().get_statistics()
        
        if self.is_connected():
            try:
                node_count = self.get_node_count()
                relationship_count = self.get_relationship_count()
                
                base_stats.update({
                    "node_count": node_count,
                    "relationship_count": relationship_count,
                    "database_name": self.config.database
                })
            except Exception as e:
                logger.error(f"Error getting Neo4j statistics: {e}")
        
        return base_stats
