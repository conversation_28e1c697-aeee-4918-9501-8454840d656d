{"$schema": "http://json-schema.org/draft-07/schema#", "title": "AGI情境感知层元数据标准", "description": "定义AGI系统中情境感知层的元数据结构和标准", "version": "1.0.0", "type": "object", "properties": {"metadata_info": {"type": "object", "description": "元数据基本信息", "properties": {"schema_version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "created_by": {"type": "string"}, "last_modified_by": {"type": "string"}}, "required": ["schema_version", "created_at", "created_by"]}, "context_definition": {"type": "object", "description": "情境定义", "properties": {"context_id": {"type": "string", "description": "情境唯一标识符", "pattern": "^context_[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$"}, "context_name": {"type": "string", "description": "情境名称", "minLength": 1, "maxLength": 255}, "context_type": {"type": "string", "description": "情境类型", "enum": ["environmental", "temporal", "spatial", "social", "cognitive", "emotional", "cultural", "technological", "organizational", "task_specific", "domain_specific", "user_specific"]}, "description": {"type": "string", "description": "情境描述", "maxLength": 1000}, "scope": {"type": "string", "description": "情境范围", "enum": ["global", "regional", "local", "personal", "session", "task"]}, "granularity": {"type": "string", "description": "情境粒度", "enum": ["coarse", "medium", "fine", "very_fine"]}}, "required": ["context_id", "context_name", "context_type"]}, "environmental_context": {"type": "object", "description": "环境情境", "properties": {"physical_environment": {"type": "object", "description": "物理环境", "properties": {"location": {"type": "object", "properties": {"coordinates": {"type": "object", "properties": {"latitude": {"type": "number", "minimum": -90, "maximum": 90}, "longitude": {"type": "number", "minimum": -180, "maximum": 180}, "altitude": {"type": "number"}}}, "address": {"type": "string"}, "place_type": {"type": "string", "enum": ["indoor", "outdoor", "vehicle", "public", "private", "office", "home", "unknown"]}}}, "weather": {"type": "object", "properties": {"temperature": {"type": "number", "description": "温度（摄氏度）"}, "humidity": {"type": "number", "minimum": 0, "maximum": 100, "description": "湿度百分比"}, "pressure": {"type": "number", "description": "气压（hPa）"}, "wind_speed": {"type": "number", "minimum": 0, "description": "风速（m/s）"}, "visibility": {"type": "number", "minimum": 0, "description": "能见度（km）"}, "conditions": {"type": "string", "enum": ["clear", "cloudy", "rainy", "snowy", "foggy", "stormy"]}}}, "lighting": {"type": "object", "properties": {"illuminance": {"type": "number", "minimum": 0, "description": "照度（lux）"}, "light_source": {"type": "string", "enum": ["natural", "artificial", "mixed"]}, "color_temperature": {"type": "number", "description": "色温（K）"}}}, "noise_level": {"type": "object", "properties": {"decibel_level": {"type": "number", "minimum": 0, "description": "噪音级别（dB）"}, "noise_type": {"type": "string", "enum": ["quiet", "moderate", "noisy", "very_noisy"]}}}}}, "digital_environment": {"type": "object", "description": "数字环境", "properties": {"platform": {"type": "string", "description": "运行平台"}, "operating_system": {"type": "string"}, "browser": {"type": "string"}, "device_type": {"type": "string", "enum": ["desktop", "laptop", "tablet", "smartphone", "server", "iot_device"]}, "screen_resolution": {"type": "object", "properties": {"width": {"type": "integer"}, "height": {"type": "integer"}}}, "network_conditions": {"type": "object", "properties": {"connection_type": {"type": "string", "enum": ["wifi", "ethernet", "cellular", "satellite", "offline"]}, "bandwidth": {"type": "number", "description": "带宽（Mbps）"}, "latency": {"type": "number", "description": "延迟（ms）"}, "stability": {"type": "string", "enum": ["stable", "unstable", "intermittent"]}}}}}}}, "temporal_context": {"type": "object", "description": "时间情境", "properties": {"timestamp": {"type": "string", "format": "date-time", "description": "时间戳"}, "timezone": {"type": "string", "description": "时区"}, "time_of_day": {"type": "string", "enum": ["morning", "afternoon", "evening", "night", "dawn", "dusk"]}, "day_of_week": {"type": "string", "enum": ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]}, "season": {"type": "string", "enum": ["spring", "summer", "autumn", "winter"]}, "is_holiday": {"type": "boolean", "description": "是否为节假日"}, "is_business_hours": {"type": "boolean", "description": "是否为工作时间"}, "temporal_patterns": {"type": "array", "description": "时间模式", "items": {"type": "object", "properties": {"pattern_type": {"type": "string", "enum": ["daily", "weekly", "monthly", "seasonal", "yearly", "custom"]}, "pattern_description": {"type": "string"}, "recurrence_rule": {"type": "string", "description": "重复规则（RFC 5545格式）"}}}}}}, "user_context": {"type": "object", "description": "用户情境", "properties": {"user_profile": {"type": "object", "description": "用户档案", "properties": {"user_id": {"type": "string"}, "demographics": {"type": "object", "properties": {"age_group": {"type": "string", "enum": ["child", "teenager", "young_adult", "adult", "senior"]}, "education_level": {"type": "string", "enum": ["primary", "secondary", "undergraduate", "graduate", "postgraduate"]}, "profession": {"type": "string"}, "expertise_areas": {"type": "array", "items": {"type": "string"}}}}, "preferences": {"type": "object", "properties": {"language": {"type": "string"}, "communication_style": {"type": "string", "enum": ["formal", "informal", "technical", "simple", "detailed", "concise"]}, "interaction_mode": {"type": "string", "enum": ["text", "voice", "visual", "multimodal"]}, "privacy_level": {"type": "string", "enum": ["public", "private", "restricted", "confidential"]}}}}}, "current_state": {"type": "object", "description": "当前状态", "properties": {"activity": {"type": "string", "description": "当前活动"}, "attention_level": {"type": "string", "enum": ["focused", "distracted", "multitasking", "idle"]}, "cognitive_load": {"type": "string", "enum": ["low", "medium", "high", "overloaded"]}, "emotional_state": {"type": "string", "enum": ["positive", "neutral", "negative", "stressed", "excited", "calm"]}, "urgency_level": {"type": "string", "enum": ["low", "normal", "high", "urgent"]}}}, "interaction_history": {"type": "object", "description": "交互历史", "properties": {"session_duration": {"type": "string", "description": "会话持续时间（ISO 8601格式）"}, "interaction_count": {"type": "integer", "minimum": 0}, "last_interaction": {"type": "string", "format": "date-time"}, "frequent_topics": {"type": "array", "items": {"type": "string"}}, "success_rate": {"type": "number", "minimum": 0.0, "maximum": 1.0, "description": "交互成功率"}}}}}, "task_context": {"type": "object", "description": "任务情境", "properties": {"current_task": {"type": "object", "properties": {"task_id": {"type": "string"}, "task_type": {"type": "string"}, "task_phase": {"type": "string", "enum": ["planning", "execution", "monitoring", "completion", "review"]}, "progress": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "complexity": {"type": "string", "enum": ["simple", "moderate", "complex", "very_complex"]}}}, "task_history": {"type": "array", "description": "任务历史", "items": {"type": "object", "properties": {"task_id": {"type": "string"}, "completion_status": {"type": "string", "enum": ["completed", "failed", "cancelled", "in_progress"]}, "completion_time": {"type": "string", "format": "date-time"}, "success_score": {"type": "number", "minimum": 0.0, "maximum": 1.0}}}}, "available_resources": {"type": "array", "description": "可用资源", "items": {"type": "object", "properties": {"resource_id": {"type": "string"}, "resource_type": {"type": "string", "enum": ["computational", "knowledge", "human", "tool", "service"]}, "availability": {"type": "string", "enum": ["available", "busy", "limited", "unavailable"]}, "capacity": {"type": "number", "minimum": 0.0, "maximum": 1.0}}}}}}, "social_context": {"type": "object", "description": "社会情境", "properties": {"participants": {"type": "array", "description": "参与者", "items": {"type": "object", "properties": {"participant_id": {"type": "string"}, "role": {"type": "string", "enum": ["user", "collaborator", "observer", "moderator", "expert"]}, "presence_status": {"type": "string", "enum": ["active", "idle", "away", "offline"]}, "interaction_level": {"type": "string", "enum": ["high", "medium", "low", "none"]}}}}, "group_dynamics": {"type": "object", "properties": {"group_size": {"type": "integer", "minimum": 1}, "collaboration_mode": {"type": "string", "enum": ["individual", "pair", "small_group", "large_group", "crowd"]}, "communication_pattern": {"type": "string", "enum": ["one_to_one", "one_to_many", "many_to_many", "broadcast"]}, "hierarchy_level": {"type": "string", "enum": ["flat", "hierarchical", "matrix", "network"]}}}, "cultural_factors": {"type": "object", "properties": {"cultural_background": {"type": "array", "items": {"type": "string"}}, "communication_norms": {"type": "array", "items": {"type": "string"}}, "etiquette_rules": {"type": "array", "items": {"type": "string"}}}}}}, "data_sources": {"type": "object", "description": "数据源信息", "properties": {"sensor_data": {"type": "array", "description": "传感器数据", "items": {"type": "object", "properties": {"sensor_id": {"type": "string"}, "sensor_type": {"type": "string", "enum": ["temperature", "humidity", "light", "motion", "sound", "location", "biometric"]}, "data_quality": {"type": "string", "enum": ["high", "medium", "low", "unreliable"]}, "update_frequency": {"type": "string", "description": "更新频率"}, "last_update": {"type": "string", "format": "date-time"}}}}, "system_logs": {"type": "array", "description": "系统日志", "items": {"type": "object", "properties": {"log_source": {"type": "string"}, "log_level": {"type": "string", "enum": ["debug", "info", "warning", "error", "critical"]}, "relevance": {"type": "number", "minimum": 0.0, "maximum": 1.0}}}}, "external_apis": {"type": "array", "description": "外部API", "items": {"type": "object", "properties": {"api_name": {"type": "string"}, "api_type": {"type": "string"}, "response_time": {"type": "number", "description": "响应时间（ms）"}, "reliability": {"type": "number", "minimum": 0.0, "maximum": 1.0}}}}}}, "context_dynamics": {"type": "object", "description": "情境动态性", "properties": {"change_frequency": {"type": "string", "description": "变化频率", "enum": ["static", "slow", "moderate", "fast", "real_time"]}, "volatility": {"type": "string", "description": "波动性", "enum": ["stable", "moderate", "volatile", "chaotic"]}, "predictability": {"type": "string", "description": "可预测性", "enum": ["predictable", "partially_predictable", "unpredictable"]}, "adaptation_requirements": {"type": "array", "description": "适应性要求", "items": {"type": "object", "properties": {"trigger_condition": {"type": "string"}, "adaptation_type": {"type": "string", "enum": ["parameter_adjustment", "strategy_change", "model_update", "complete_reconfiguration"]}, "response_time": {"type": "string", "description": "响应时间要求"}}}}}}, "quality_metrics": {"type": "object", "description": "质量度量", "properties": {"completeness": {"type": "number", "minimum": 0.0, "maximum": 1.0, "description": "完整性"}, "accuracy": {"type": "number", "minimum": 0.0, "maximum": 1.0, "description": "准确性"}, "timeliness": {"type": "number", "minimum": 0.0, "maximum": 1.0, "description": "时效性"}, "relevance": {"type": "number", "minimum": 0.0, "maximum": 1.0, "description": "相关性"}, "consistency": {"type": "number", "minimum": 0.0, "maximum": 1.0, "description": "一致性"}, "confidence_level": {"type": "number", "minimum": 0.0, "maximum": 1.0, "description": "置信水平"}}}, "provenance_info": {"type": "object", "description": "数据溯源信息", "properties": {"collection_method": {"type": "string", "enum": ["sensor_reading", "user_input", "system_monitoring", "api_call", "inference", "aggregation", "manual_annotation"]}, "data_lineage": {"type": "array", "description": "数据血缘", "items": {"type": "object", "properties": {"source_id": {"type": "string"}, "transformation": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}}}}, "collection_timestamp": {"type": "string", "format": "date-time"}, "collector_id": {"type": "string"}, "version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$"}}, "required": ["collection_method", "collection_timestamp", "version"]}, "extensions": {"type": "object", "description": "扩展字段", "patternProperties": {"^ext_[a-zA-Z_][a-zA-Z0-9_]*$": {"description": "扩展字段，字段名必须以ext_开头"}}}}, "required": ["metadata_info", "context_definition", "quality_metrics", "provenance_info"], "additionalProperties": false}