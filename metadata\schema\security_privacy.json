{"$schema": "http://json-schema.org/draft-07/schema#", "title": "AGI安全与隐私层元数据标准", "description": "定义AGI系统中安全与隐私层的元数据结构和标准", "version": "1.0.0", "type": "object", "properties": {"metadata_info": {"type": "object", "description": "元数据基本信息", "properties": {"schema_version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "created_by": {"type": "string"}, "last_modified_by": {"type": "string"}, "classification_level": {"type": "string", "enum": ["public", "internal", "confidential", "restricted", "top_secret"]}}, "required": ["schema_version", "created_at", "created_by", "classification_level"]}, "access_control": {"type": "object", "description": "访问控制", "properties": {"access_policy": {"type": "object", "description": "访问策略", "properties": {"policy_id": {"type": "string"}, "policy_name": {"type": "string"}, "policy_type": {"type": "string", "enum": ["rbac", "abac", "dac", "mac", "custom"]}, "policy_description": {"type": "string"}, "enforcement_level": {"type": "string", "enum": ["strict", "moderate", "lenient"]}}, "required": ["policy_id", "policy_name", "policy_type"]}, "user_permissions": {"type": "array", "description": "用户权限", "items": {"type": "object", "properties": {"user_id": {"type": "string"}, "user_type": {"type": "string", "enum": ["human", "system", "service", "application"]}, "role": {"type": "string"}, "permissions": {"type": "array", "items": {"type": "string", "enum": ["read", "write", "execute", "delete", "admin", "audit"]}}, "access_level": {"type": "string", "enum": ["none", "limited", "standard", "elevated", "full"]}, "granted_at": {"type": "string", "format": "date-time"}, "expires_at": {"type": "string", "format": "date-time"}, "granted_by": {"type": "string"}}, "required": ["user_id", "user_type", "role", "permissions", "access_level"]}}, "group_permissions": {"type": "array", "description": "组权限", "items": {"type": "object", "properties": {"group_id": {"type": "string"}, "group_name": {"type": "string"}, "group_type": {"type": "string", "enum": ["department", "project", "role_based", "security_level"]}, "permissions": {"type": "array", "items": {"type": "string"}}, "members": {"type": "array", "items": {"type": "string"}}}, "required": ["group_id", "group_name", "group_type", "permissions"]}}, "resource_access_matrix": {"type": "object", "description": "资源访问矩阵", "patternProperties": {"^[a-zA-Z0-9_]+$": {"type": "object", "properties": {"resource_type": {"type": "string"}, "access_requirements": {"type": "array", "items": {"type": "string"}}, "restrictions": {"type": "array", "items": {"type": "string"}}}}}}}, "required": ["access_policy"]}, "data_protection": {"type": "object", "description": "数据保护", "properties": {"encryption": {"type": "object", "description": "加密", "properties": {"encryption_at_rest": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "algorithm": {"type": "string", "enum": ["AES-256", "AES-128", "RSA", "ECC", "custom"]}, "key_management": {"type": "string", "enum": ["internal", "external_kms", "hsm", "manual"]}, "key_rotation_frequency": {"type": "string", "enum": ["daily", "weekly", "monthly", "quarterly", "yearly", "manual"]}}, "required": ["enabled"]}, "encryption_in_transit": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "protocol": {"type": "string", "enum": ["TLS_1.3", "TLS_1.2", "HTTPS", "SFTP", "custom"]}, "certificate_type": {"type": "string", "enum": ["self_signed", "ca_signed", "wildcard", "ev"]}}, "required": ["enabled"]}, "field_level_encryption": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "encrypted_fields": {"type": "array", "items": {"type": "string"}}, "encryption_keys": {"type": "object", "patternProperties": {"^[a-zA-Z0-9_]+$": {"type": "string"}}}}}}}, "anonymization": {"type": "object", "description": "匿名化", "properties": {"anonymization_level": {"type": "string", "enum": ["none", "pseudonymization", "anonymization", "differential_privacy", "k_anonymity"]}, "techniques_applied": {"type": "array", "items": {"type": "string", "enum": ["data_masking", "generalization", "suppression", "perturbation", "synthetic_data"]}}, "privacy_budget": {"type": "number", "minimum": 0.0, "description": "隐私预算（用于差分隐私）"}, "k_value": {"type": "integer", "minimum": 2, "description": "k-匿名性的k值"}, "reversibility": {"type": "boolean", "description": "是否可逆"}}}, "data_masking": {"type": "object", "description": "数据脱敏", "properties": {"masking_rules": {"type": "array", "items": {"type": "object", "properties": {"field_name": {"type": "string"}, "masking_type": {"type": "string", "enum": ["static", "dynamic", "format_preserving", "tokenization"]}, "masking_method": {"type": "string", "enum": ["substitution", "shuffling", "nulling", "hashing", "encryption"]}, "preserve_format": {"type": "boolean"}, "preserve_length": {"type": "boolean"}}, "required": ["field_name", "masking_type", "masking_method"]}}, "masking_consistency": {"type": "boolean", "description": "是否保持脱敏一致性"}}}}}, "privacy_compliance": {"type": "object", "description": "隐私合规", "properties": {"regulatory_framework": {"type": "array", "description": "监管框架", "items": {"type": "string", "enum": ["GDPR", "CCPA", "PIPEDA", "LGPD", "PDPA", "HIPAA", "SOX", "PCI_DSS", "custom"]}}, "consent_management": {"type": "object", "description": "同意管理", "properties": {"consent_required": {"type": "boolean"}, "consent_type": {"type": "string", "enum": ["explicit", "implicit", "opt_in", "opt_out"]}, "consent_granularity": {"type": "string", "enum": ["all_or_nothing", "purpose_based", "data_type_based", "granular"]}, "consent_withdrawal": {"type": "boolean", "description": "是否支持撤回同意"}, "consent_records": {"type": "array", "items": {"type": "object", "properties": {"user_id": {"type": "string"}, "consent_given": {"type": "boolean"}, "consent_timestamp": {"type": "string", "format": "date-time"}, "consent_purpose": {"type": "string"}, "consent_version": {"type": "string"}}}}}}, "data_subject_rights": {"type": "object", "description": "数据主体权利", "properties": {"right_to_access": {"type": "boolean"}, "right_to_rectification": {"type": "boolean"}, "right_to_erasure": {"type": "boolean"}, "right_to_portability": {"type": "boolean"}, "right_to_restrict_processing": {"type": "boolean"}, "right_to_object": {"type": "boolean"}, "automated_decision_making": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "human_review_required": {"type": "boolean"}, "explanation_provided": {"type": "boolean"}}}}}, "data_retention": {"type": "object", "description": "数据保留", "properties": {"retention_policy": {"type": "string", "description": "保留政策"}, "retention_period": {"type": "string", "description": "保留期限"}, "deletion_schedule": {"type": "string", "description": "删除计划"}, "legal_hold": {"type": "boolean", "description": "是否有法律保留要求"}, "archival_requirements": {"type": "string", "description": "归档要求"}}}}}, "security_measures": {"type": "object", "description": "安全措施", "properties": {"authentication": {"type": "object", "description": "身份认证", "properties": {"authentication_methods": {"type": "array", "items": {"type": "string", "enum": ["password", "mfa", "biometric", "certificate", "token", "sso", "o<PERSON>h"]}}, "password_policy": {"type": "object", "properties": {"min_length": {"type": "integer", "minimum": 1}, "complexity_requirements": {"type": "array", "items": {"type": "string", "enum": ["uppercase", "lowercase", "numbers", "special_chars"]}}, "expiration_days": {"type": "integer", "minimum": 1}, "history_count": {"type": "integer", "minimum": 1}}}, "session_management": {"type": "object", "properties": {"session_timeout": {"type": "integer", "description": "会话超时时间（分钟）"}, "concurrent_sessions": {"type": "integer", "description": "允许的并发会话数"}, "session_encryption": {"type": "boolean"}}}}}, "authorization": {"type": "object", "description": "授权", "properties": {"authorization_model": {"type": "string", "enum": ["rbac", "abac", "pbac", "custom"]}, "privilege_escalation": {"type": "object", "properties": {"allowed": {"type": "boolean"}, "approval_required": {"type": "boolean"}, "time_limited": {"type": "boolean"}, "audit_required": {"type": "boolean"}}}, "least_privilege": {"type": "boolean", "description": "是否遵循最小权限原则"}}}, "audit_logging": {"type": "object", "description": "审计日志", "properties": {"audit_enabled": {"type": "boolean"}, "audit_scope": {"type": "array", "items": {"type": "string", "enum": ["access", "modification", "deletion", "configuration", "authentication", "authorization"]}}, "log_retention_period": {"type": "string", "description": "日志保留期限"}, "log_integrity": {"type": "object", "properties": {"tamper_protection": {"type": "boolean"}, "digital_signature": {"type": "boolean"}, "hash_verification": {"type": "boolean"}}}, "real_time_monitoring": {"type": "boolean"}}}, "threat_detection": {"type": "object", "description": "威胁检测", "properties": {"intrusion_detection": {"type": "boolean"}, "anomaly_detection": {"type": "boolean"}, "behavioral_analysis": {"type": "boolean"}, "threat_intelligence": {"type": "boolean"}, "automated_response": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "response_actions": {"type": "array", "items": {"type": "string", "enum": ["alert", "block", "quarantine", "terminate", "escalate"]}}}}}}}}, "risk_assessment": {"type": "object", "description": "风险评估", "properties": {"risk_categories": {"type": "array", "description": "风险类别", "items": {"type": "object", "properties": {"category_name": {"type": "string", "enum": ["confidentiality", "integrity", "availability", "privacy", "compliance", "operational"]}, "risk_level": {"type": "string", "enum": ["low", "medium", "high", "critical"]}, "risk_score": {"type": "number", "minimum": 0.0, "maximum": 10.0}, "mitigation_measures": {"type": "array", "items": {"type": "string"}}, "residual_risk": {"type": "string", "enum": ["low", "medium", "high", "critical"]}}, "required": ["category_name", "risk_level"]}}, "vulnerability_assessment": {"type": "object", "description": "漏洞评估", "properties": {"last_assessment_date": {"type": "string", "format": "date"}, "assessment_frequency": {"type": "string", "enum": ["weekly", "monthly", "quarterly", "annually"]}, "vulnerabilities_found": {"type": "array", "items": {"type": "object", "properties": {"vulnerability_id": {"type": "string"}, "severity": {"type": "string", "enum": ["low", "medium", "high", "critical"]}, "cvss_score": {"type": "number", "minimum": 0.0, "maximum": 10.0}, "status": {"type": "string", "enum": ["open", "in_progress", "resolved", "accepted"]}, "remediation_plan": {"type": "string"}}}}}}, "impact_assessment": {"type": "object", "description": "影响评估", "properties": {"business_impact": {"type": "string", "enum": ["low", "medium", "high", "critical"]}, "technical_impact": {"type": "string", "enum": ["low", "medium", "high", "critical"]}, "regulatory_impact": {"type": "string", "enum": ["low", "medium", "high", "critical"]}, "reputational_impact": {"type": "string", "enum": ["low", "medium", "high", "critical"]}}}}}, "incident_response": {"type": "object", "description": "事件响应", "properties": {"incident_classification": {"type": "object", "description": "事件分类", "properties": {"incident_types": {"type": "array", "items": {"type": "string", "enum": ["data_breach", "unauthorized_access", "malware", "ddos", "insider_threat", "system_failure"]}}, "severity_levels": {"type": "array", "items": {"type": "string", "enum": ["low", "medium", "high", "critical"]}}}}, "response_procedures": {"type": "object", "description": "响应程序", "properties": {"detection_procedures": {"type": "array", "items": {"type": "string"}}, "containment_procedures": {"type": "array", "items": {"type": "string"}}, "eradication_procedures": {"type": "array", "items": {"type": "string"}}, "recovery_procedures": {"type": "array", "items": {"type": "string"}}}}, "notification_requirements": {"type": "object", "description": "通知要求", "properties": {"internal_notification": {"type": "object", "properties": {"required": {"type": "boolean"}, "timeframe": {"type": "string"}, "recipients": {"type": "array", "items": {"type": "string"}}}}, "external_notification": {"type": "object", "properties": {"regulatory_notification": {"type": "boolean"}, "customer_notification": {"type": "boolean"}, "public_disclosure": {"type": "boolean"}, "timeframe": {"type": "string"}}}}}}}, "compliance_monitoring": {"type": "object", "description": "合规监控", "properties": {"compliance_status": {"type": "object", "description": "合规状态", "patternProperties": {"^[A-Z_]+$": {"type": "object", "properties": {"compliant": {"type": "boolean"}, "compliance_score": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "last_assessment": {"type": "string", "format": "date"}, "next_assessment": {"type": "string", "format": "date"}, "gaps_identified": {"type": "array", "items": {"type": "string"}}}}}}, "monitoring_controls": {"type": "array", "description": "监控控制", "items": {"type": "object", "properties": {"control_id": {"type": "string"}, "control_name": {"type": "string"}, "control_type": {"type": "string", "enum": ["preventive", "detective", "corrective", "compensating"]}, "effectiveness": {"type": "string", "enum": ["effective", "partially_effective", "ineffective"]}, "testing_frequency": {"type": "string"}, "last_test_date": {"type": "string", "format": "date"}}}}}}, "provenance_info": {"type": "object", "description": "数据溯源信息", "properties": {"security_classification": {"type": "string", "enum": ["public", "internal", "confidential", "restricted", "top_secret"]}, "data_owner": {"type": "string", "description": "数据所有者"}, "data_steward": {"type": "string", "description": "数据管理员"}, "security_review": {"type": "object", "properties": {"reviewed": {"type": "boolean"}, "reviewer": {"type": "string"}, "review_date": {"type": "string", "format": "date"}, "approval_status": {"type": "string", "enum": ["approved", "pending", "rejected"]}}}, "version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$"}}, "required": ["security_classification", "data_owner", "version"]}, "extensions": {"type": "object", "description": "扩展字段", "patternProperties": {"^ext_[a-zA-Z_][a-zA-Z0-9_]*$": {"description": "扩展字段，字段名必须以ext_开头"}}}}, "required": ["metadata_info", "access_control", "data_protection", "privacy_compliance", "security_measures", "provenance_info"], "additionalProperties": false}